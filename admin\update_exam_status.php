<?php

if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) && php_sapi_name() !== 'cli') {

    session_start();
    require_once '../config/database.php';
    require_once '../includes/functions.php';

    if (!isLoggedIn() || (!isAdmin() && !isAssistant())) {
        http_response_code(403);
        echo json_encode(['success' => false, 'error' => 'غير مصرح']);
        exit();
    }
} else {
    require_once '../config/database.php';
    require_once '../includes/functions.php';
}

try {

    $stmt = $pdo->prepare("
        UPDATE exams
        SET status = 'completed',
            updated_at = NOW()
        WHERE status = 'active'
        AND end_time < NOW()
    ");

    $stmt->execute();
    $updated_count = $stmt->rowCount();

    if ($updated_count > 0) {
        $user_id = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : null;
        logActivity($user_id, 'auto_complete_exams', "تم تحديث حالة {$updated_count} امتحان إلى مكتمل تلقائياً");

        $stmt = $pdo->query("
            SELECT id, title, end_time
            FROM exams
            WHERE status = 'completed'
            AND updated_at >= DATE_SUB(NOW(), INTERVAL 1 MINUTE)
        ");
        $updated_exams = $stmt->fetchAll();

        foreach ($updated_exams as $exam) {
            error_log("Auto-completed exam: ID={$exam['id']}, Title={$exam['title']}, EndTime={$exam['end_time']}");
        }
    }

    if (php_sapi_name() === 'cli') {

        echo "تم تحديث {$updated_count} امتحان\n";
    } else {

        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'updated_count' => $updated_count,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }

} catch (PDOException $e) {
    error_log('Error in update_exam_status.php: ' . $e->getMessage());

    if (php_sapi_name() === 'cli') {
        echo "خطأ: " . $e->getMessage() . "\n";
        exit(1);
    } else {
        header('Content-Type: application/json');
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => 'خطأ في قاعدة البيانات'
        ]);
    }
}
?>
