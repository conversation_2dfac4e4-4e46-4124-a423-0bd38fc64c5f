<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

if (!isLoggedIn()) {
    header('Location: ../index.php');
    exit();
}

if (!isAdmin() && !isAssistant()) {
    header('Location: ../index.php');
    exit();
}

$message = '';
$error = '';

$subject_id = isset($_GET['subject_id']) ? (int)$_GET['subject_id'] : 0;
if (!$subject_id) {
    header('Location: subjects.php');
    exit();
}

try {
    $stmt = $pdo->prepare("SELECT * FROM subjects WHERE id = ?");
    $stmt->execute([$subject_id]);
    $subject = $stmt->fetch();

    if (!$subject) {
        header('Location: subjects.php');
        exit();
    }
} catch (PDOException $e) {
    $error = 'خطأ في تحميل بيانات المادة';
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    if (!verifyCSRFToken($_POST['csrf_token'])) {
        $error = 'خطأ في التحقق من الأمان';
    } else {
        if ($_POST['action'] === 'add') {
            $title = sanitize($_POST['title']);
            $description = sanitize($_POST['description']);

            try {
                $stmt = $pdo->prepare("SELECT MAX(lecture_number) as max_number FROM lectures WHERE subject_id = ?");
                $stmt->execute([$subject_id]);
                $result = $stmt->fetch();
                $lecture_number = ($result['max_number'] ?? 0) + 1;
            } catch (PDOException $e) {
                $lecture_number = 1;
            }

            if (empty($title)) {
                $error = 'يرجى إدخال عنوان المحاضرة';
            } else {
                try {
                    $stmt = $pdo->prepare("INSERT INTO lectures (subject_id, title, lecture_number, description, created_by) VALUES (?, ?, ?, ?, ?)");
                    if ($stmt->execute([$subject_id, $title, $lecture_number, $description, $_SESSION['user_id']])) {
                        $message = 'تم إضافة المحاضرة بنجاح';
                        logActivity($_SESSION['user_id'], 'add_lecture', "إضافة محاضرة جديدة: $title");
                    } else {
                        $error = 'خطأ في إضافة المحاضرة';
                    }
                } catch (PDOException $e) {
                    if ($e->getCode() == 23000) {
                        $error = 'رقم المحاضرة موجود مسبقاً في هذه المادة';
                    } else {
                        $error = 'خطأ في قاعدة البيانات';
                    }
                }
            }
        } elseif ($_POST['action'] === 'edit') {
            $id = (int)$_POST['id'];
            $title = sanitize($_POST['title']);
            $description = sanitize($_POST['description']);
            $status = $_POST['status'];

            if (empty($title)) {
                $error = 'يرجى إدخال عنوان المحاضرة';
            } else {
                try {
                    $stmt = $pdo->prepare("UPDATE lectures SET title = ?, description = ?, status = ? WHERE id = ? AND subject_id = ?");
                    if ($stmt->execute([$title, $description, $status, $id, $subject_id])) {
                        $message = 'تم تحديث المحاضرة بنجاح';
                        logActivity($_SESSION['user_id'], 'edit_lecture', "تحديث محاضرة: $title");
                    } else {
                        $error = 'خطأ في تحديث المحاضرة';
                    }
                } catch (PDOException $e) {
                    $error = 'خطأ في قاعدة البيانات';
                }
            }
        } elseif ($_POST['action'] === 'delete') {
            $id = (int)$_POST['id'];

            try {

                $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM questions WHERE lecture_id = ?");
                $stmt->execute([$id]);
                $questionCount = $stmt->fetch()['count'];

                if ($questionCount > 0) {
                    $error = 'لا يمكن حذف المحاضرة لوجود أسئلة مرتبطة بها';
                } else {
                    $stmt = $pdo->prepare("DELETE FROM lectures WHERE id = ? AND subject_id = ?");
                    if ($stmt->execute([$id, $subject_id])) {
                        $message = 'تم حذف المحاضرة بنجاح';
                        logActivity($_SESSION['user_id'], 'delete_lecture', "حذف محاضرة برقم: $id");
                    } else {
                        $error = 'خطأ في حذف المحاضرة';
                    }
                }
            } catch (PDOException $e) {
                $error = 'خطأ في قاعدة البيانات';
            }
        } elseif ($_POST['action'] === 'move') {
            $id = (int)$_POST['id'];
            $direction = $_POST['direction'];

            try {

                $stmt = $pdo->prepare("SELECT lecture_number FROM lectures WHERE id = ? AND subject_id = ?");
                $stmt->execute([$id, $subject_id]);
                $currentLecture = $stmt->fetch();

                if (!$currentLecture) {
                    $error = 'المحاضرة غير موجودة';
                } else {
                    $currentNumber = $currentLecture['lecture_number'];

                    if ($direction === 'up') {

                        $stmt = $pdo->prepare("SELECT id, lecture_number FROM lectures WHERE subject_id = ? AND lecture_number < ? ORDER BY lecture_number DESC LIMIT 1");
                        $stmt->execute([$subject_id, $currentNumber]);
                        $previousLecture = $stmt->fetch();

                        if ($previousLecture) {

                            $pdo->beginTransaction();

                            $tempNumber = 999999;
                            $stmt = $pdo->prepare("UPDATE lectures SET lecture_number = ? WHERE id = ?");
                            $stmt->execute([$tempNumber, $id]);

                            $stmt = $pdo->prepare("UPDATE lectures SET lecture_number = ? WHERE id = ?");
                            $stmt->execute([$currentNumber, $previousLecture['id']]);

                            $stmt = $pdo->prepare("UPDATE lectures SET lecture_number = ? WHERE id = ?");
                            $stmt->execute([$previousLecture['lecture_number'], $id]);

                            $pdo->commit();

                            $message = 'تم تحريك المحاضرة لأعلى بنجاح';
                            logActivity($_SESSION['user_id'], 'move_lecture', "تحريك محاضرة لأعلى: $id");
                        }
                    } elseif ($direction === 'down') {

                        $stmt = $pdo->prepare("SELECT id, lecture_number FROM lectures WHERE subject_id = ? AND lecture_number > ? ORDER BY lecture_number ASC LIMIT 1");
                        $stmt->execute([$subject_id, $currentNumber]);
                        $nextLecture = $stmt->fetch();

                        if ($nextLecture) {

                            $pdo->beginTransaction();

                            $tempNumber = 999999;
                            $stmt = $pdo->prepare("UPDATE lectures SET lecture_number = ? WHERE id = ?");
                            $stmt->execute([$tempNumber, $id]);

                            $stmt = $pdo->prepare("UPDATE lectures SET lecture_number = ? WHERE id = ?");
                            $stmt->execute([$currentNumber, $nextLecture['id']]);

                            $stmt = $pdo->prepare("UPDATE lectures SET lecture_number = ? WHERE id = ?");
                            $stmt->execute([$nextLecture['lecture_number'], $id]);

                            $pdo->commit();

                            $message = 'تم تحريك المحاضرة لأسفل بنجاح';
                            logActivity($_SESSION['user_id'], 'move_lecture', "تحريك محاضرة لأسفل: $id");
                        }
                    }
                }
            } catch (PDOException $e) {
                if ($pdo->inTransaction()) {
                    $pdo->rollBack();
                }
                $error = 'خطأ في قاعدة البيانات: ' . $e->getMessage();
            }
        }
    }
}

try {
    $stmt = $pdo->prepare("SELECT l.*, u.full_name as created_by_name,
                          (SELECT COUNT(*) FROM questions WHERE lecture_id = l.id) as questions_count
                          FROM lectures l 
                          LEFT JOIN users u ON l.created_by = u.id 
                          WHERE l.subject_id = ?
                          ORDER BY l.lecture_number ASC");
    $stmt->execute([$subject_id]);
    $lectures = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = 'خطأ في تحميل البيانات';
}

$csrf_token = generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>إدارة المحاضرات - <?php echo htmlspecialchars($subject['name']); ?></title>
    <link <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;500;600&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="../assets/css/mobile.css" rel="stylesheet">
    <style>
        body {
            background: #183046;
            min-height: 100vh;
            font-family: 'Cairo', sans-serif;
            overflow-x: hidden;
            overflow-y: auto;
        }
        .navbar {
            background: #fff !important;
            box-shadow: 0 2px 10px rgba(0,0,0,0.07);
            border-bottom: 1px solid #e3e6f0;
        }
        .navbar-brand {
            font-weight: 700;
            font-size: 1.2rem;
            color: #2c3e50 !important;
            line-height: 1.2;
            display: flex;
            align-items: center;
        }

        .brand-text {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .nav-link, .navbar-nav .nav-link {
            color: #2c3e50 !important;
            font-weight: 600;
            border-radius: 25px;
            margin: 0 0.3rem;
        }
        .nav-link.active, .nav-link:hover {
            background: #2196f3;
            color: #fff !important;
        }
        .lectures-header {
            margin-top: 2.5rem;
            margin-bottom: 2.5rem;
            text-align: right;
        }
        .lectures-title {
            font-size: 2.3rem;
            font-weight: 900;
            color: #fff;
            margin-bottom: 0.5rem;
        }
        .lectures-subtitle {
            color: #e0e0e0;
            font-size: 1.1rem;
            font-weight: 400;
        }
        .modern-card {
            background: #fff;
            border-radius: 16px;
            box-shadow: 0 4px 24px rgba(102,126,234,0.08);
            padding: 1.5rem 1.2rem;
            margin-bottom: 2rem;
        }
        .modern-card .card-header {
            background: none;
            border: none;
            padding: 0 0 1rem 0;
            color: #2196f3;
            font-weight: 700;
            font-size: 1.2rem;
        }
        .modern-card .list-group-item {
            border: none;
            border-bottom: 1px solid #f0f0f0;
            padding: 0.7rem 0.2rem;
        }
        .modern-card .list-group-item:last-child {
            border-bottom: none;
        }
        .card.border-0.shadow-sm {
            border-radius: 18px;
            background: #fff;
            box-shadow: 0 8px 32px rgba(102,126,234,0.08);
            padding: 1.5rem 1.2rem;
        }
        .card-header.bg-primary {
            background: #2196f3 !important;
            color: #fff !important;
            border-radius: 16px 16px 0 0 !important;
            font-weight: 700;
            font-size: 1.2rem;
        }
        .btn-primary, .btn-outline-primary:hover {
            background: #2196f3 !important;
            border-color: #2196f3 !important;
            color: #fff !important;
        }
        .btn-outline-primary {
            color: #2196f3 !important;
            border-color: #2196f3 !important;
            background: #fff !important;
        }
        .btn-outline-primary:focus, .btn-outline-primary:active {
            background: #2196f3 !important;
            color: #fff !important;
        }
        .table {
            background: #fff;
            border-radius: 12px;
            overflow: hidden;
        }
        .table th, .table td {
            vertical-align: middle;
        }
        .modal-content {
            border-radius: 16px;
        }
        .modal-header {
            border-bottom: 1px solid #f0f0f0;
        }
        .modal-title {
            color: #2196f3;
            font-weight: 700;
        }
        .form-label {
            font-weight: 600;
            color: #2c3e50;
        }
        .form-control, .form-select {
            border-radius: 10px;
        }
        .btn-close {
            margin: 0;
        }
        .footer {
            background: #2c3e50;
            color: #fff;
            padding: 2.5rem 0 1rem;
            margin-top: 3rem;
        }
        .footer-title {
            font-size: 1.2rem;
            font-weight: 700;
        }
        .footer-bottom {
            border-top: 1px solid rgba(255,255,255,0.1);
            padding-top: 1rem;
            text-align: center;
            opacity: 0.7;
        }
        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        .btn-group .btn {
            border-radius: 0;
        }
        .btn-group .btn:first-child {
            border-top-right-radius: 0.375rem;
            border-bottom-right-radius: 0.375rem;
        }
        .btn-group .btn:last-child {
            border-top-left-radius: 0.375rem;
            border-bottom-left-radius: 0.375rem;
        }
        @media (max-width: 991px) {
            .lectures-header {margin-top: 1.5rem;}
        }

        @media (max-width: 768px) {
            .lectures-header .d-flex {
                flex-direction: column;
                align-items: flex-start !important;
                gap: 1rem;
            }

            .lectures-header .d-flex > div:last-child {
                width: 100%;
                display: flex;
                flex-direction: column;
                gap: 0.5rem;
            }

            .lectures-header .btn {
                width: 100%;
                justify-content: center;
                padding: 0.75rem 1rem;
                font-size: 0.9rem;
            }

            .lectures-header .btn i {
                margin-left: 0.5rem;
            }

            .lectures-title {
                font-size: 1.5rem;
                margin-bottom: 0.5rem;
            }

            .lectures-subtitle {
                font-size: 0.9rem;
                margin-bottom: 1rem;
            }
        }

        @media (max-width: 576px) {
            .lectures-header .btn {
                font-size: 0.85rem;
                padding: 0.6rem 0.8rem;
            }

            .lectures-title {
                font-size: 1.3rem;
            }

            .lectures-subtitle {
                font-size: 0.8rem;
            }
        }

        @media (max-width: 768px) {
            .table-responsive {
                border: none;
                margin-bottom: 1rem;
            }

            .table {
                font-size: 0.85rem;
            }

            .table th,
            .table td {
                padding: 0.5rem 0.3rem;
                white-space: nowrap;
            }

            .table .btn {
                padding: 0.25rem 0.5rem;
                font-size: 0.75rem;
                margin: 0.1rem;
            }

            .table .btn i {
                font-size: 0.7rem;
            }
        }

        @media (max-width: 576px) {
            .table {
                font-size: 0.8rem;
            }

            .table th,
            .table td {
                padding: 0.4rem 0.2rem;
            }

            .table .btn {
                padding: 0.2rem 0.4rem;
                font-size: 0.7rem;
            }
        }

        @media (max-width: 768px) {
            .card {
                margin-bottom: 1rem;
                border-radius: 12px;
            }

            .card-header {
                padding: 1rem;
                font-size: 1rem;
            }

            .card-body {
                padding: 1rem;
            }
        }

        @media (max-width: 576px) {
            .card-header {
                padding: 0.8rem;
                font-size: 0.95rem;
            }

            .card-body {
                padding: 0.8rem;
            }
        }

        @media (max-width: 768px) {
            .modal-dialog {
                margin: 0.5rem;
                max-width: calc(100% - 1rem);
            }

            .modal-content {
                border-radius: 12px;
            }

            .modal-header {
                padding: 1rem;
                border-bottom: 1px solid #dee2e6;
            }

            .modal-body {
                padding: 1rem;
            }

            .modal-footer {
                padding: 1rem;
                border-top: 1px solid #dee2e6;
            }

            .modal-footer .btn {
                width: 100%;
                margin-bottom: 0.5rem;
            }

            .form-control,
            .form-select {
                font-size: 16px !important;
                padding: 0.75rem;
            }

            .form-label {
                font-weight: 600;
                margin-bottom: 0.5rem;
            }
        }

        @media (max-width: 576px) {
            .modal-dialog {
                margin: 0.25rem;
                max-width: calc(100% - 0.5rem);
            }

            .modal-header,
            .modal-body,
            .modal-footer {
                padding: 0.8rem;
            }

            .modal-title {
                font-size: 1.1rem;
            }
        }

        @media (max-width: 768px) {
            .container {
                padding-left: 15px;
                padding-right: 15px;
            }

            .alert {
                border-radius: 8px;
                padding: 1rem;
                font-size: 0.9rem;
            }

            .alert .btn-close {
                padding: 0.5rem;
            }

            .badge {
                font-size: 0.7rem;
                padding: 0.3rem 0.6rem;
            }

            .text-muted {
                font-size: 0.8rem;
            }

            .table .btn-group {
                display: flex;
                flex-direction: column;
                gap: 0.2rem;
            }

            .table .btn-group .btn {
                width: 100%;
                margin: 0;
            }
        }

        @media (max-width: 576px) {
            .container {
                padding-left: 10px;
                padding-right: 10px;
            }

            .alert {
                padding: 0.8rem;
                font-size: 0.85rem;
            }

            .badge {
                font-size: 0.65rem;
                padding: 0.25rem 0.5rem;
            }
        }

        @media (max-width: 768px) {
            .btn:active {
                transform: scale(0.98);
            }

            .card:hover {
                transform: none;
            }

            .table-hover tbody tr:hover {
                background-color: rgba(0, 123, 255, 0.05);
            }
        }
    </style>
</head>
<body>
    <?php include '../includes/mobile_navbar.php'; ?>
    <div class="container" style="margin-top: 80px;">
        <div class="lectures-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="lectures-title">
                        <i class="fas fa-chalkboard-teacher text-primary me-2"></i>
                        إدارة المحاضرات
                    </div>
                    <div class="lectures-subtitle">
                        <?php echo htmlspecialchars($subject['name']); ?> - جامعة المنيا - قسم الـ Forensic
                    </div>
                </div>
                <div>
                    <a href="subjects.php" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للمواد
                    </a>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addLectureModal" id="addLectureBtn">
                        <i class="fas fa-plus me-2"></i>
                        إضافة محاضرة جديدة
                    </button>
                </div>
            </div>
        </div>

        <!-- Messages -->
        <?php if ($message): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo $message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?php echo $error; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Lectures Table -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    قائمة المحاضرات
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($lectures)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-chalkboard-teacher text-muted" style="font-size: 3rem;"></i>
                        <p class="text-muted mt-3">لا توجد محاضرات مضافة بعد</p>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addLectureModal">
                            <i class="fas fa-plus me-2"></i>
                            إضافة أول محاضرة
                        </button>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover" id="lecturesTable">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>رقم المحاضرة</th>
                                    <th>عنوان المحاضرة</th>
                                    <th>الوصف</th>
                                    <th>عدد الأسئلة</th>
                                    <th>الحالة</th>
                                    <th>أنشئ بواسطة</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($lectures as $index => $lecture): ?>
                                    <tr>
                                        <td><?php echo $index + 1; ?></td>
                                        <td>
                                            <span class="badge bg-primary"><?php echo $lecture['lecture_number']; ?></span>
                                        </td>
                                        <td>
                                            <strong><?php echo htmlspecialchars($lecture['title']); ?></strong>
                                        </td>
                                        <td>
                                            <?php echo htmlspecialchars(substr($lecture['description'], 0, 50)) . (strlen($lecture['description']) > 50 ? '...' : ''); ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-info"><?php echo $lecture['questions_count']; ?></span>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?php echo $lecture['status'] === 'active' ? 'success' : 'danger'; ?>">
                                                <?php echo $lecture['status'] === 'active' ? 'نشط' : 'غير نشط'; ?>
                                            </span>
                                        </td>
                                        <td><?php echo htmlspecialchars($lecture['created_by_name']); ?></td>
                                        <td><?php echo date('Y-m-d', strtotime($lecture['created_at'])); ?></td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button class="btn btn-sm btn-outline-secondary" 
                                                        onclick="moveLecture(<?php echo $lecture['id']; ?>, 'up')"
                                                        title="تحريك لأعلى"
                                                        <?php echo ($index == 0) ? 'disabled' : ''; ?>>
                                                    <i class="fas fa-chevron-up"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-secondary" 
                                                        onclick="moveLecture(<?php echo $lecture['id']; ?>, 'down')"
                                                        title="تحريك لأسفل"
                                                        <?php echo ($index == count($lectures) - 1) ? 'disabled' : ''; ?>>
                                                    <i class="fas fa-chevron-down"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-primary" 
                                                        onclick="editLecture(<?php echo htmlspecialchars(json_encode($lecture)); ?>)"
                                                        title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <a href="questions.php?lecture_id=<?php echo $lecture['id']; ?>" 
                                                   class="btn btn-sm btn-outline-info" title="الأسئلة">
                                                    <i class="fas fa-question-circle"></i>
                                                </a>
                                                <?php if ($lecture['questions_count'] == 0): ?>
                                                    <button class="btn btn-sm btn-outline-danger" 
                                                            onclick="deleteLecture(<?php echo $lecture['id']; ?>, '<?php echo htmlspecialchars($lecture['title']); ?>')"
                                                            title="حذف">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Add Lecture Modal -->
    <div class="modal fade" id="addLectureModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-plus me-2"></i>
                        إضافة محاضرة جديدة
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" class="needs-validation" novalidate>
                    <div class="modal-body">
                        <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                        <input type="hidden" name="action" value="add">

                        <div class="mb-3">
                            <label for="lecture_number" class="form-label">رقم المحاضرة</label>
                            <input type="number" class="form-control" id="lecture_number" name="lecture_number" min="1" readonly style="background-color: #f8f9fa;">
                            <small class="form-text text-muted">سيتم تعيين الرقم تلقائياً</small>
                        </div>

                        <div class="mb-3">
                            <label for="title" class="form-label">عنوان المحاضرة *</label>
                            <input type="text" class="form-control" id="title" name="title" required>
                            <div class="invalid-feedback">يرجى إدخال عنوان المحاضرة</div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">وصف المحاضرة</label>
                            <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            حفظ
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Lecture Modal -->
    <div class="modal fade" id="editLectureModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-edit me-2"></i>
                        تعديل المحاضرة
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" class="needs-validation" novalidate>
                    <div class="modal-body">
                        <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                        <input type="hidden" name="action" value="edit">
                        <input type="hidden" name="id" id="edit_id">

                        <div class="mb-3">
                            <label for="edit_lecture_number" class="form-label">رقم المحاضرة</label>
                            <input type="number" class="form-control" id="edit_lecture_number" name="lecture_number" min="1" readonly style="background-color: #f8f9fa;">
                            <small class="form-text text-muted">لا يمكن تغيير رقم المحاضرة</small>
                        </div>

                        <div class="mb-3">
                            <label for="edit_title" class="form-label">عنوان المحاضرة *</label>
                            <input type="text" class="form-control" id="edit_title" name="title" required>
                            <div class="invalid-feedback">يرجى إدخال عنوان المحاضرة</div>
                        </div>

                        <div class="mb-3">
                            <label for="edit_description" class="form-label">وصف المحاضرة</label>
                            <textarea class="form-control" id="edit_description" name="description" rows="3"></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="edit_status" class="form-label">الحالة</label>
                            <select class="form-select" id="edit_status" name="status">
                                <option value="active">نشط</option>
                                <option value="inactive">غير نشط</option>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            حفظ التغييرات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <footer class="footer">
        <div class="container">
            <div class="footer-title">جامعة المنيا - قسم الـ Forensic</div>
            <div class="footer-bottom">
                &copy; 2024 جميع الحقوق محفوظة - جامعة المنيا - قسم الـ Forensic
            </div>
        </div>
    </footer>
    <script src="https:
    <script src="../assets/js/main.js"></script>
    <script>

        function editLecture(lecture) {
            document.getElementById('edit_id').value = lecture.id;
            document.getElementById('edit_lecture_number').value = lecture.lecture_number;
            document.getElementById('edit_title').value = lecture.title;
            document.getElementById('edit_description').value = lecture.description;
            document.getElementById('edit_status').value = lecture.status;

            new bootstrap.Modal(document.getElementById('editLectureModal')).show();
        }

        function deleteLecture(id, title) {
            if (confirm(`هل أنت متأكد من حذف المحاضرة: ${title}؟`)) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                    <input type="hidden" name="action" value="delete">
                    <input type="hidden" name="id" value="${id}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        function moveLecture(id, direction) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.innerHTML = `
                <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                <input type="hidden" name="action" value="move">
                <input type="hidden" name="id" value="${id}">
                <input type="hidden" name="direction" value="${direction}">
            `;
            document.body.appendChild(form);
            form.submit();
        }

        function searchLectures() {
            const input = document.getElementById('searchInput');
            const filter = input.value.toUpperCase();
            const table = document.getElementById('lecturesTable');
            const rows = table.getElementsByTagName('tr');

            for (let i = 1; i < rows.length; i++) {
                const cells = rows[i].getElementsByTagName('td');
                let found = false;

                for (let j = 0; j < cells.length; j++) {
                    if (cells[j].textContent.toUpperCase().indexOf(filter) > -1) {
                        found = true;
                        break;
                    }
                }

                rows[i].style.display = found ? '' : 'none';
            }
        }

        function calculateNextLectureNumber() {
            const table = document.getElementById('lecturesTable');
            if (!table) return 1;

            const rows = table.getElementsByTagName('tbody')[0].getElementsByTagName('tr');
            let maxNumber = 0;

            for (let i = 0; i < rows.length; i++) {
                const cells = rows[i].getElementsByTagName('td');
                if (cells.length > 1) {
                    const lectureNumber = parseInt(cells[1].textContent.trim());
                    if (!isNaN(lectureNumber) && lectureNumber > maxNumber) {
                        maxNumber = lectureNumber;
                    }
                }
            }

            return maxNumber + 1;
        }

        // دالة لتنظيف الـ modals وإزالة الخلفية المظلمة
        function cleanupModal() {
            // إزالة جميع الخلفيات المظلمة
            const backdrops = document.querySelectorAll('.modal-backdrop');
            backdrops.forEach(backdrop => backdrop.remove());

            // إزالة classes من body
            document.body.classList.remove('modal-open');
            document.body.style.overflow = '';
            document.body.style.paddingRight = '';

            // إخفاء جميع الـ modals
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                modal.style.display = 'none';
                modal.classList.remove('show');
                modal.setAttribute('aria-hidden', 'true');
                modal.removeAttribute('aria-modal');
            });
        }

        document.addEventListener('DOMContentLoaded', function() {
            // التأكد من تحميل Bootstrap
            if (typeof bootstrap === 'undefined') {
                console.error('Bootstrap JavaScript غير محمل');
                return;
            }

            const addModal = document.getElementById('addLectureModal');
            if (addModal) {
                addModal.addEventListener('show.bs.modal', function() {
                    const nextNumber = calculateNextLectureNumber();
                    const lectureNumberField = document.getElementById('lecture_number');
                    if (lectureNumberField) {
                        lectureNumberField.value = nextNumber;
                    }
                });

                // إصلاح مشكلة الخلفية المظلمة عند الإغلاق
                addModal.addEventListener('hidden.bs.modal', cleanupModal);
            }

            // إضافة event listener للزر
            const addButton = document.getElementById('addLectureBtn');
            if (addButton && addModal) {
                addButton.addEventListener('click', function(e) {
                    console.log('تم النقر على زر إضافة محاضرة');
                    e.preventDefault();

                    // إنشاء modal instance والتأكد من فتحه
                    try {
                        const modal = new bootstrap.Modal(addModal, {
                            backdrop: 'static',
                            keyboard: false
                        });
                        modal.show();
                    } catch (error) {
                        console.error('خطأ في فتح الـ modal:', error);
                        // fallback - محاولة فتح الـ modal بطريقة أخرى
                        addModal.classList.add('show');
                        addModal.style.display = 'block';
                        document.body.classList.add('modal-open');
                    }
                });
            }

            // إصلاح مشكلة الخلفية المظلمة للـ edit modal
            const editModal = document.getElementById('editLectureModal');
            if (editModal) {
                editModal.addEventListener('hidden.bs.modal', cleanupModal);
            }

            const lectureNumberField = document.getElementById('lecture_number');
            const editLectureNumberField = document.getElementById('edit_lecture_number');

            if (lectureNumberField) {
                lectureNumberField.style.cursor = 'not-allowed';
            }
            if (editLectureNumberField) {
                editLectureNumberField.style.cursor = 'not-allowed';
            }

            // Form validation
            const forms = document.querySelectorAll('.needs-validation');
            Array.from(forms).forEach(form => {
                form.addEventListener('submit', event => {
                    if (!form.checkValidity()) {
                        event.preventDefault();
                        event.stopPropagation();
                    }
                    form.classList.add('was-validated');
                });
            });

            // إضافة event listeners لجميع أزرار الإغلاق
            const closeButtons = document.querySelectorAll('[data-bs-dismiss="modal"], .btn-close');
            closeButtons.forEach(button => {
                button.addEventListener('click', function() {
                    setTimeout(cleanupModal, 300); // تأخير قصير للسماح للـ animation بالانتهاء
                });
            });

            // إضافة event listener للنقر خارج الـ modal
            document.addEventListener('click', function(e) {
                if (e.target.classList.contains('modal')) {
                    setTimeout(cleanupModal, 300);
                }
            });

            // إضافة event listener لمفتاح Escape
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    setTimeout(cleanupModal, 300);
                }
            });
        });
    </script>

    <!-- Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/mobile.js"></script>
</body>
</html>