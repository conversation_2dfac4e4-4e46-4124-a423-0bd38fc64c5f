<?php
// إعدادات الجلسة الآمنة
ini_set('session.cookie_httponly', 1);
ini_set('session.cookie_secure', isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 1 : 0);
ini_set('session.use_strict_mode', 1);

session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// تطبيق headers الأمنية
setSecurityHeaders();

// فحص الصلاحيات والأمان
preventDirectAccess(['admin', 'assistant']);

$error = '';
$success = '';

try {
    $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $user = $stmt->fetch();

    if (!$user) {
        header('Location: ../logout.php');
        exit();
    }
} catch (PDOException $e) {
    $error = 'خطأ في تحميل البيانات';
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_profile'])) {
    if (!verifyCSRFToken($_POST['csrf_token'])) {
        $error = 'خطأ في التحقق من الأمان';
    } else {
        $full_name = sanitize($_POST['full_name']);
        $email = sanitize($_POST['email']);

        if (empty($full_name)) {
            $error = 'يرجى إدخال الاسم الكامل';
        } elseif (!empty($email) && !isValidEmail($email)) {
            $error = 'يرجى إدخال بريد إلكتروني صحيح';
        } else {
            try {
                $stmt = $pdo->prepare("UPDATE users SET full_name = ?, email = ?, updated_at = NOW() WHERE id = ?");
                if ($stmt->execute([$full_name, $email, $_SESSION['user_id']])) {
                    $success = 'تم تحديث الملف الشخصي بنجاح';
                    $_SESSION['full_name'] = $full_name;
                    logActivity($_SESSION['user_id'], 'update_profile', 'تحديث الملف الشخصي');

                    $user['full_name'] = $full_name;
                    $user['email'] = $email;
                } else {
                    $error = 'فشل في تحديث الملف الشخصي';
                }
            } catch (PDOException $e) {
                $error = 'خطأ في النظام';
            }
        }
    }
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['change_password'])) {
    if (!verifyCSRFToken($_POST['csrf_token'])) {
        $error = 'خطأ في التحقق من الأمان';
    } else {
        $current_password = $_POST['current_password'];
        $new_password = $_POST['new_password'];
        $confirm_password = $_POST['confirm_password'];

        if (empty($current_password) || empty($new_password) || empty($confirm_password)) {
            $error = 'يرجى ملء جميع الحقول';
        } elseif (!verifyPassword($current_password, $user['password'])) {
            $error = 'كلمة المرور الحالية غير صحيحة';
        } elseif ($new_password !== $confirm_password) {
            $error = 'كلمة المرور الجديدة غير متطابقة';
        } elseif (!isStrongPassword($new_password)) {
            $error = 'كلمة المرور الجديدة يجب أن تكون قوية (8 أحرف على الأقل، حرف كبير، حرف صغير، رقم)';
        } else {
            try {
                $hashed_password = hashPassword($new_password);
                $stmt = $pdo->prepare("UPDATE users SET password = ?, updated_at = NOW() WHERE id = ?");
                if ($stmt->execute([$hashed_password, $_SESSION['user_id']])) {
                    $success = 'تم تغيير كلمة المرور بنجاح';
                    logActivity($_SESSION['user_id'], 'change_password', 'تغيير كلمة المرور');
                } else {
                    $error = 'فشل في تغيير كلمة المرور';
                }
            } catch (PDOException $e) {
                $error = 'خطأ في النظام';
            }
        }
    }
}

try {
    $stmt = $pdo->prepare("SELECT * FROM activity_logs WHERE user_id = ? ORDER BY created_at DESC LIMIT 10");
    $stmt->execute([$_SESSION['user_id']]);
    $recent_activities = $stmt->fetchAll();
} catch (PDOException $e) {
    $recent_activities = [];
}

$csrf_token = generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>الملف الشخصي - نظام الامتحانات الإلكترونية</title>
    <link <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;500;600&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="../assets/css/mobile.css" rel="stylesheet">
    <style>
        body {
            background: #183046;
            min-height: 100vh;
            font-family: 'Cairo', sans-serif;
            overflow-x: hidden;
            overflow-y: auto;
        }
        .navbar {
            background: #fff !important;
            box-shadow: 0 2px 10px rgba(0,0,0,0.07);
            border-bottom: 1px solid #e3e6f0;
        }
        .navbar-brand {
            font-weight: 700;
            font-size: 1.2rem;
            color: #2c3e50 !important;
            line-height: 1.2;
            display: flex;
            align-items: center;
        }

        .brand-text {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .nav-link, .navbar-nav .nav-link {
            color: #2c3e50 !important;
            font-weight: 600;
            border-radius: 25px;
            margin: 0 0.3rem;
        }
        .nav-link.active, .nav-link:hover {
            background: #2196f3;
            color: #fff !important;
        }
        .profile-header {
            margin-top: 2.5rem;
            margin-bottom: 2.5rem;
            text-align: right;
        }
        .profile-title {
            font-size: 2.3rem;
            font-weight: 900;
            color: #fff;
            margin-bottom: 0.5rem;
        }
        .profile-subtitle {
            color: #e0e0e0;
            font-size: 1.1rem;
            font-weight: 400;
        }
        .modern-card {
            background: #fff;
            border-radius: 16px;
            box-shadow: 0 4px 24px rgba(102,126,234,0.08);
            padding: 1.5rem 1.2rem;
            margin-bottom: 2rem;
        }
        .modern-card .card-header {
            background: none;
            border: none;
            padding: 0 0 1rem 0;
            color: #2196f3;
            font-weight: 700;
            font-size: 1.2rem;
        }
        .modern-card .list-group-item {
            border: none;
            border-bottom: 1px solid #f0f0f0;
            padding: 0.7rem 0.2rem;
        }
        .modern-card .list-group-item:last-child {
            border-bottom: none;
        }
        .card.border-0.shadow-sm {
            border-radius: 18px;
            background: #fff;
            box-shadow: 0 8px 32px rgba(102,126,234,0.08);
            padding: 1.5rem 1.2rem;
        }
        .card-header.bg-primary {
            background: #2196f3 !important;
            color: #fff !important;
            border-radius: 16px 16px 0 0 !important;
            font-weight: 700;
            font-size: 1.2rem;
        }
        .btn-primary, .btn-outline-primary:hover {
            background: #2196f3 !important;
            border-color: #2196f3 !important;
            color: #fff !important;
        }
        .btn-outline-primary {
            color: #2196f3 !important;
            border-color: #2196f3 !important;
            background: #fff !important;
        }
        .btn-outline-primary:focus, .btn-outline-primary:active {
            background: #2196f3 !important;
            color: #fff !important;
        }
        .table {
            background: #fff;
            border-radius: 12px;
            overflow: hidden;
        }
        .table th, .table td {
            vertical-align: middle;
        }
        .modal-content {
            border-radius: 16px;
        }
        .modal-header {
            border-bottom: 1px solid #f0f0f0;
        }
        .modal-title {
            color: #2196f3;
            font-weight: 700;
        }
        .form-label {
            font-weight: 600;
            color: #2c3e50;
        }
        .form-control, .form-select {
            border-radius: 10px;
        }
        .btn-close {
            margin: 0;
        }
        .footer {
            background: #2c3e50;
            color: #fff;
            padding: 2.5rem 0 1rem;
            margin-top: 3rem;
        }
        .footer-title {
            font-size: 1.2rem;
            font-weight: 700;
        }
        .footer-bottom {
            border-top: 1px solid rgba(255,255,255,0.1);
            padding-top: 1rem;
            text-align: center;
            opacity: 0.7;
        }
        @media (max-width: 991px) {
            .profile-header {margin-top: 1.5rem;}
        }

        @media (max-width: 768px) {
            .profile-header {
                text-align: center;
                margin-bottom: 2rem;
            }

            .profile-title {
                font-size: 1.5rem;
                margin-bottom: 0.5rem;
            }

            .profile-subtitle {
                font-size: 0.9rem;
                margin-bottom: 1rem;
            }

            .card {
                margin-bottom: 1.5rem;
                border-radius: 12px;
            }

            .card-header {
                padding: 1rem;
                font-size: 1rem;
                text-align: center;
            }

            .card-body {
                padding: 1.5rem;
            }

            .form-control,
            .form-select {
                font-size: 16px !important;
                padding: 0.75rem;
                border-radius: 8px;
            }

            .form-label {
                font-weight: 600;
                margin-bottom: 0.5rem;
                font-size: 0.9rem;
            }

            .btn {
                padding: 0.75rem 1.5rem;
                font-size: 0.9rem;
                border-radius: 8px;
                width: 100%;
                margin-bottom: 0.5rem;
            }

            .btn i {
                margin-left: 0.5rem;
            }

            .alert {
                border-radius: 8px;
                padding: 1rem;
                font-size: 0.9rem;
                text-align: center;
            }

            .row {
                margin: 0;
            }

            .col-md-6 {
                padding: 0 0.5rem;
                margin-bottom: 1rem;
            }
        }

        @media (max-width: 576px) {
            .profile-title {
                font-size: 1.3rem;
            }

            .profile-subtitle {
                font-size: 0.8rem;
            }

            .card-header {
                padding: 0.8rem;
                font-size: 0.95rem;
            }

            .card-body {
                padding: 1rem;
            }

            .btn {
                padding: 0.6rem 1rem;
                font-size: 0.85rem;
            }

            .alert {
                padding: 0.8rem;
                font-size: 0.85rem;
            }

            .col-md-6 {
                padding: 0 0.25rem;
            }
        }
    </style>
</head>
<body>
    <?php include '../includes/mobile_navbar.php'; ?>
    <div class="container" style="margin-top: 80px;">
        <div class="profile-header">
            <div class="profile-title">
                <i class="fas fa-user text-primary me-2"></i>
                الملف الشخصي
            </div>
            <div class="profile-subtitle">إدارة معلومات الحساب الشخصي - جامعة المنيا - قسم الـ Forensic</div>
        </div>

        <div class="row">
            <!-- Profile Information -->
            <div class="col-lg-8">
                <!-- Basic Information -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            المعلومات الأساسية
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if ($error): ?>
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <?php echo $error; ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>

                        <?php if ($success): ?>
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                <i class="fas fa-check-circle me-2"></i>
                                <?php echo $success; ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>

                        <form method="POST" class="needs-validation" novalidate>
                            <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="username" class="form-label">اسم المستخدم</label>
                                    <input type="text" class="form-control" id="username" value="<?php echo htmlspecialchars($user['username']); ?>" readonly>
                                    <div class="form-text">لا يمكن تغيير اسم المستخدم</div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="role" class="form-label">الصلاحية</label>
                                    <input type="text" class="form-control" id="role" value="<?php echo $user['role'] === 'admin' ? 'مدير' : 'مساعد مدير'; ?>" readonly>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="full_name" class="form-label">الاسم الكامل *</label>
                                    <input type="text" class="form-control" id="full_name" name="full_name"
                                           value="<?php echo htmlspecialchars($user['full_name']); ?>" required>
                                    <div class="invalid-feedback">
                                        يرجى إدخال الاسم الكامل
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label">البريد الإلكتروني</label>
                                    <input type="email" class="form-control" id="email" name="email"
                                           value="<?php echo htmlspecialchars($user['email'] ?? ''); ?>">
                                    <div class="invalid-feedback">
                                        يرجى إدخال بريد إلكتروني صحيح
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="status" class="form-label">الحالة</label>
                                    <input type="text" class="form-control" id="status"
                                           value="<?php echo $user['status'] === 'active' ? 'نشط' : 'غير نشط'; ?>" readonly>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="last_login" class="form-label">آخر تسجيل دخول</label>
                                    <input type="text" class="form-control" id="last_login"
                                           value="<?php echo $user['last_login'] ? date('Y-m-d H:i', strtotime($user['last_login'])) : 'غير محدد'; ?>" readonly>
                                </div>
                            </div>

                            <button type="submit" name="update_profile" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                حفظ التغييرات
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Change Password -->
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">
                            <i class="fas fa-lock me-2"></i>
                            تغيير كلمة المرور
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" class="needs-validation" novalidate>
                            <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">

                            <div class="row">
                                <div class="col-md-12 mb-3">
                                    <label for="current_password" class="form-label">كلمة المرور الحالية *</label>
                                    <div class="input-group">
                                        <input type="password" class="form-control" id="current_password" name="current_password" required>
                                        <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('current_password')">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                    <div class="invalid-feedback">
                                        يرجى إدخال كلمة المرور الحالية
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="new_password" class="form-label">كلمة المرور الجديدة *</label>
                                    <div class="input-group">
                                        <input type="password" class="form-control" id="new_password" name="new_password" required>
                                        <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('new_password')">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                    <div class="invalid-feedback">
                                        يرجى إدخال كلمة المرور الجديدة
                                    </div>
                                    <div class="form-text">
                                        يجب أن تحتوي على 8 أحرف على الأقل، حرف كبير، حرف صغير، رقم
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="confirm_password" class="form-label">تأكيد كلمة المرور *</label>
                                    <div class="input-group">
                                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                        <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('confirm_password')">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                    <div class="invalid-feedback">
                                        يرجى تأكيد كلمة المرور الجديدة
                                    </div>
                                </div>
                            </div>

                            <button type="submit" name="change_password" class="btn btn-warning">
                                <i class="fas fa-key me-2"></i>
                                تغيير كلمة المرور
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- User Info Card -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-body text-center">
                        <div class="mb-3">
                            <i class="fas fa-user-circle text-primary" style="font-size: 4rem;"></i>
                        </div>
                        <h5 class="card-title"><?php echo htmlspecialchars($user['full_name']); ?></h5>
                        <p class="text-muted"><?php echo $user['role'] === 'admin' ? 'مدير النظام' : 'مساعد مدير'; ?></p>
                        <hr>
                        <div class="row text-start">
                            <div class="col-6">
                                <small class="text-muted">اسم المستخدم:</small>
                                <br><strong><?php echo htmlspecialchars($user['username']); ?></strong>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">الحالة:</small>
                                <br>
                                <span class="badge bg-<?php echo $user['status'] === 'active' ? 'success' : 'danger'; ?>">
                                    <?php echo $user['status'] === 'active' ? 'نشط' : 'غير نشط'; ?>
                                </span>
                            </div>
                        </div>
                        <hr>
                        <div class="row text-start">
                            <div class="col-6">
                                <small class="text-muted">تاريخ الإنشاء:</small>
                                <br><strong><?php echo date('Y-m-d', strtotime($user['created_at'])); ?></strong>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">آخر تحديث:</small>
                                <br><strong><?php echo date('Y-m-d', strtotime($user['updated_at'])); ?></strong>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Activities -->
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-light">
                        <h6 class="mb-0">
                            <i class="fas fa-history me-2"></i>
                            آخر النشاطات
                        </h6>
                    </div>
                    <div class="card-body p-0">
                        <?php if (empty($recent_activities)): ?>
                            <div class="text-center py-3">
                                <p class="text-muted mb-0">لا توجد نشاطات حديثة</p>
                            </div>
                        <?php else: ?>
                            <div class="list-group list-group-flush">
                                <?php foreach ($recent_activities as $activity): ?>
                                    <div class="list-group-item border-0">
                                        <div class="d-flex w-100 justify-content-between">
                                            <h6 class="mb-1"><?php echo htmlspecialchars($activity['action']); ?></h6>
                                            <small class="text-muted"><?php echo date('m-d H:i', strtotime($activity['created_at'])); ?></small>
                                        </div>
                                        <?php if ($activity['details']): ?>
                                            <p class="mb-1 text-muted"><?php echo htmlspecialchars($activity['details']); ?></p>
                                        <?php endif; ?>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    </div>
    <footer class="footer">
        <div class="container">
            <div class="footer-title">جامعة المنيا - قسم الـ Forensic</div>
            <div class="footer-bottom">
                &copy; 2024 جميع الحقوق محفوظة - جامعة المنيا - قسم الـ Forensic
            </div>
        </div>
    </footer>
    <script src="https:
    <script src="../assets/js/main.js"></script>
    <script>
        function togglePassword(inputId) {
            const input = document.getElementById(inputId);
            const icon = input.nextElementSibling.querySelector('i');

            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                input.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        }

        const forms = document.querySelectorAll('.needs-validation');
        forms.forEach(form => {
            form.addEventListener('submit', function(event) {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            });
        });
    </script>
    <script src="../assets/js/mobile.js"></script>
    <!-- Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>