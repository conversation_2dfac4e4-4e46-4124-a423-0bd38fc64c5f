<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');

if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit();
}

if (!isset($_GET['subject_id']) || empty($_GET['subject_id'])) {
    echo json_encode(['success' => false, 'message' => 'معرف المادة مطلوب']);
    exit();
}

$subject_id = (int)$_GET['subject_id'];

try {

    $stmt = $pdo->prepare("SELECT l.id, l.title, l.lecture_number, s.name as subject_name,
                          (SELECT COUNT(*) FROM questions q WHERE q.lecture_id = l.id AND q.status = 'active') as questions_count
                          FROM lectures l
                          JOIN subjects s ON l.subject_id = s.id
                          WHERE l.subject_id = ? AND l.status = 'active'
                          ORDER BY l.lecture_number");
    $stmt->execute([$subject_id]);
    $lectures = $stmt->fetchAll();

    echo json_encode([
        'success' => true,
        'lectures' => $lectures
    ]);

} catch (PDOException $e) {
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في قاعدة البيانات'
    ]);
}
?>