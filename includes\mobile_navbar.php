<?php

$current_page = basename($_SERVER['PHP_SELF']);
$nav_items = [
    'dashboard.php' => 'لوحة التحكم',
    'subjects.php' => 'المواد',
    'exams.php' => 'الامتحانات',
    'results.php' => 'النتائج'
];
?>

<nav class="navbar navbar-expand-lg navbar-light fixed-top">
    <div class="container">
        <a class="navbar-brand" href="dashboard.php">
            <i class="fas fa-graduation-cap me-2"></i>
            <span class="brand-text">جامعة المنيا - قسم الـ Forensic</span>
        </a>

        <!-- أزرار منفصلة للموبايل -->
        <div class="mobile-nav-buttons d-lg-none">
            <a href="profile.php" class="btn btn-outline-light btn-sm me-2" title="الملف الشخصي">
                <i class="fas fa-user"></i>
            </a>
            <a href="../logout.php" class="btn btn-outline-danger btn-sm me-2" title="تسجيل الخروج">
                <i class="fas fa-sign-out-alt"></i>
            </a>
        </div>

        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>

        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <?php foreach ($nav_items as $page => $title): ?>
                    <li class="nav-item">
                        <a class="nav-link <?php echo ($current_page === $page) ? 'active' : ''; ?>" href="<?php echo $page; ?>">
                            <?php echo $title; ?>
                        </a>
                    </li>
                <?php endforeach; ?>
            </ul>

            <!-- قائمة المستخدم للديسكتوب فقط -->
            <ul class="navbar-nav d-none d-lg-flex">
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <?php echo htmlspecialchars($_SESSION['full_name'] ?? $_SESSION['username'] ?? 'المستخدم'); ?>
                    </a>
                    <ul class="dropdown-menu" aria-labelledby="navbarDropdown">
                        <li>
                            <a class="dropdown-item" href="profile.php">
                                <i class="fas fa-user me-2"></i>الملف الشخصي
                            </a>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <a class="dropdown-item" href="../logout.php">
                                <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                            </a>
                        </li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
</nav>
