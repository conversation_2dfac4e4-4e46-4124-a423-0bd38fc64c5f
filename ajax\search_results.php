<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit();
}

$exam_id = isset($_GET['exam_id']) ? (int)$_GET['exam_id'] : 0;
$student_id = isset($_GET['student_id']) ? sanitize($_GET['student_id']) : '';
$exam_type = isset($_GET['exam_type']) ? sanitize($_GET['exam_type']) : '';

try {
    $where_conditions = [];
    $params = [];

    if ($exam_id > 0) {
        $where_conditions[] = "er.exam_id = ?";
        $params[] = $exam_id;
    }

    if (!empty($student_id)) {
        $where_conditions[] = "er.student_id LIKE ?";
        $params[] = "%$student_id%";
    }

    if (!empty($exam_type)) {
        $where_conditions[] = "e.exam_type = ?";
        $params[] = $exam_type;
    }

    $where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";

    $sql = "SELECT er.*, e.title as exam_title, e.exam_type, s.name as subject_name 
            FROM exam_results er 
            LEFT JOIN exams e ON er.exam_id = e.id 
            LEFT JOIN subjects s ON e.subject_id = s.id 
            $where_clause 
            ORDER BY er.submitted_at DESC 
            LIMIT 100";

    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $results = $stmt->fetchAll();

    echo json_encode([
        'success' => true,
        'results' => $results
    ]);

} catch (PDOException $e) {
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في قاعدة البيانات'
    ]);
}
?>