<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');

if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit();
}

try {

    $stmt = $pdo->query("SELECT COUNT(*) as total FROM exams");
    $totalExams = $stmt->fetch()['total'];

    $stmt = $pdo->query("SELECT COUNT(*) as active FROM exams WHERE status = 'active'");
    $activeExams = $stmt->fetch()['active'];

    $stmt = $pdo->query("SELECT COUNT(DISTINCT student_id) as total FROM exam_results");
    $totalStudents = $stmt->fetch()['total'];

    $stmt = $pdo->query("SELECT COUNT(*) as completed FROM exams WHERE status = 'completed'");
    $completedExams = $stmt->fetch()['completed'];

    echo json_encode([
        'success' => true,
        'stats' => [
            'total_exams' => $totalExams,
            'active_exams' => $activeExams,
            'total_students' => $totalStudents,
            'completed_exams' => $completedExams
        ]
    ]);

} catch (PDOException $e) {
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في قاعدة البيانات'
    ]);
}
?>