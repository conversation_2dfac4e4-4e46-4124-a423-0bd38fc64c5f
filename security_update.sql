-- تحديث قاعدة البيانات لإضافة ميزات الأمان الجديدة
-- نظام الامتحانات الإلكترونية - تحديث الأمان

-- إنشاء جدول تسجيل الأحداث الأمنية
CREATE TABLE IF NOT EXISTS `security_logs` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `event_type` varchar(100) NOT NULL,
    `details` text DEFAULT NULL,
    `severity` enum('low','medium','high','critical') DEFAULT 'medium',
    `ip_address` varchar(45) DEFAULT NULL,
    `user_agent` text DEFAULT NULL,
    `user_id` int(11) DEFAULT NULL,
    `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
    PRIMARY KEY (`id`),
    KEY `idx_event_type` (`event_type`),
    KEY `idx_severity` (`severity`),
    KEY `idx_created_at` (`created_at`),
    KEY `idx_user_id` (`user_id`),
    <PERSON>EY `idx_ip_address` (`ip_address`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء جدول Rate Limiting
CREATE TABLE IF NOT EXISTS `rate_limiting` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `identifier` varchar(255) NOT NULL,
    `attempts` int(11) DEFAULT 1,
    `first_attempt` timestamp NOT NULL DEFAULT current_timestamp(),
    `last_attempt` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_identifier` (`identifier`),
    KEY `idx_last_attempt` (`last_attempt`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء جدول جلسات آمنة (اختياري)
CREATE TABLE IF NOT EXISTS `secure_sessions` (
    `id` varchar(128) NOT NULL,
    `user_id` int(11) DEFAULT NULL,
    `ip_address` varchar(45) DEFAULT NULL,
    `user_agent` text DEFAULT NULL,
    `data` text DEFAULT NULL,
    `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
    `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
    `expires_at` timestamp NOT NULL,
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_expires_at` (`expires_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إضافة فهارس للأداء على الجداول الموجودة
ALTER TABLE `activity_logs` 
ADD INDEX IF NOT EXISTS `idx_ip_address` (`ip_address`),
ADD INDEX IF NOT EXISTS `idx_action` (`action`);

-- إضافة عمود لتتبع محاولات تسجيل الدخول الفاشلة للمستخدمين
ALTER TABLE `users` 
ADD COLUMN IF NOT EXISTS `failed_login_attempts` int(11) DEFAULT 0,
ADD COLUMN IF NOT EXISTS `last_failed_login` timestamp NULL DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `account_locked_until` timestamp NULL DEFAULT NULL;

-- إضافة عمود لتتبع آخر تسجيل دخول
ALTER TABLE `users` 
ADD COLUMN IF NOT EXISTS `last_login_at` timestamp NULL DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `last_login_ip` varchar(45) DEFAULT NULL;

-- إنشاء جدول للملفات المرفوعة (للمراقبة الأمنية)
CREATE TABLE IF NOT EXISTS `uploaded_files` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `user_id` int(11) DEFAULT NULL,
    `original_name` varchar(255) NOT NULL,
    `stored_name` varchar(255) NOT NULL,
    `file_path` varchar(500) NOT NULL,
    `file_size` int(11) NOT NULL,
    `mime_type` varchar(100) NOT NULL,
    `file_hash` varchar(64) DEFAULT NULL,
    `upload_ip` varchar(45) DEFAULT NULL,
    `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_file_hash` (`file_hash`),
    KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء جدول للتحقق من التوقيعات الرقمية (للمستقبل)
CREATE TABLE IF NOT EXISTS `digital_signatures` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `exam_id` int(11) NOT NULL,
    `student_id` varchar(50) NOT NULL,
    `signature_hash` varchar(256) NOT NULL,
    `signature_data` text NOT NULL,
    `ip_address` varchar(45) DEFAULT NULL,
    `user_agent` text DEFAULT NULL,
    `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
    PRIMARY KEY (`id`),
    KEY `idx_exam_student` (`exam_id`, `student_id`),
    KEY `idx_signature_hash` (`signature_hash`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج بعض الأحداث الأمنية الأولية
INSERT IGNORE INTO `security_logs` (`event_type`, `details`, `severity`, `ip_address`, `user_id`) VALUES
('system_update', 'تم تطبيق تحديثات الأمان على النظام', 'medium', '127.0.0.1', 1),
('database_update', 'تم إنشاء جداول الأمان الجديدة', 'low', '127.0.0.1', 1);

-- إنشاء stored procedures للأمان (اختياري)
DELIMITER //

-- دالة لتنظيف السجلات القديمة
CREATE PROCEDURE IF NOT EXISTS CleanupSecurityLogs()
BEGIN
    -- حذف سجلات الأمان الأقدم من 90 يوم
    DELETE FROM security_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 90 DAY);
    
    -- حذف سجلات rate limiting الأقدم من 24 ساعة
    DELETE FROM rate_limiting WHERE last_attempt < DATE_SUB(NOW(), INTERVAL 24 HOUR);
    
    -- حذف الجلسات المنتهية الصلاحية
    DELETE FROM secure_sessions WHERE expires_at < NOW();
END //

-- دالة للحصول على إحصائيات الأمان
CREATE PROCEDURE IF NOT EXISTS GetSecurityStats()
BEGIN
    SELECT 
        'failed_logins_24h' as metric,
        COUNT(*) as value
    FROM security_logs 
    WHERE event_type = 'failed_login' 
    AND created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)
    
    UNION ALL
    
    SELECT 
        'blocked_ips' as metric,
        COUNT(*) as value
    FROM rate_limiting 
    WHERE attempts >= 5
    
    UNION ALL
    
    SELECT 
        'high_severity_events_7d' as metric,
        COUNT(*) as value
    FROM security_logs 
    WHERE severity IN ('high', 'critical')
    AND created_at > DATE_SUB(NOW(), INTERVAL 7 DAY);
END //

DELIMITER ;

-- إنشاء event scheduler لتنظيف البيانات تلقائياً (إذا كان مفعل)
-- SET GLOBAL event_scheduler = ON;

CREATE EVENT IF NOT EXISTS cleanup_security_data
ON SCHEDULE EVERY 1 DAY
STARTS CURRENT_TIMESTAMP
DO
  CALL CleanupSecurityLogs();

-- إضافة تعليقات للجداول
ALTER TABLE `security_logs` COMMENT = 'جدول تسجيل الأحداث الأمنية';
ALTER TABLE `rate_limiting` COMMENT = 'جدول تتبع محاولات الوصول للحماية من الهجمات';
ALTER TABLE `uploaded_files` COMMENT = 'جدول مراقبة الملفات المرفوعة';

-- إنشاء views مفيدة للمراقبة
CREATE OR REPLACE VIEW security_dashboard AS
SELECT 
    DATE(created_at) as date,
    event_type,
    severity,
    COUNT(*) as count
FROM security_logs 
WHERE created_at > DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY DATE(created_at), event_type, severity
ORDER BY date DESC, count DESC;

-- إنشاء view للمستخدمين المشبوهين
CREATE OR REPLACE VIEW suspicious_activities AS
SELECT 
    ip_address,
    COUNT(*) as failed_attempts,
    MAX(created_at) as last_attempt,
    GROUP_CONCAT(DISTINCT event_type) as event_types
FROM security_logs 
WHERE severity IN ('high', 'critical')
AND created_at > DATE_SUB(NOW(), INTERVAL 7 DAY)
GROUP BY ip_address
HAVING failed_attempts > 3
ORDER BY failed_attempts DESC;

-- تحديث إصدار قاعدة البيانات
INSERT INTO activity_logs (user_id, action, details, ip_address) VALUES 
(1, 'security_update', 'تم تطبيق تحديث الأمان - إصدار 2.0', '127.0.0.1');
