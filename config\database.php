<?php
// تحميل إعدادات الأمان
define('SECURITY_CONFIG_LOADED', true);
require_once __DIR__ . '/security.php';

// إعدادات قاعدة البيانات - استخدام متغيرات البيئة للأمان
define('DB_HOST', $_ENV['DB_HOST'] ?? 'localhost');
define('DB_NAME', $_ENV['DB_NAME'] ?? 'u831955567_fo');
define('DB_USER', $_ENV['DB_USER'] ?? 'u831955567_foor6');
define('DB_PASS', $_ENV['DB_PASS'] ?? 'Foor6foor6@');

// إعدادات أمنية إضافية (من ملف security.php)
define('MAX_LOGIN_ATTEMPTS', RATE_LIMIT_LOGIN);
define('LOGIN_LOCKOUT_TIME', RATE_LIMIT_LOGIN_WINDOW);
define('SESSION_TIMEOUT', SESSION_MAX_LIFETIME);

// تكوين DSN مع إعدادات إضافية
$dsn = "mysql:host=" . DB_HOST .
       ";dbname=" . DB_NAME .
       ";charset=utf8mb4";

$options = [
    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    PDO::ATTR_EMULATE_PREPARES => false,
    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
];

try {
    $pdo = new PDO($dsn, DB_USER, DB_PASS, $options);

    $pdo->query("SELECT 1");
} catch(PDOException $e) {
    error_log("Database Connection Error: " . $e->getMessage());
    error_log("DSN: " . $dsn);
    error_log("Error Code: " . $e->getCode());
    die("خطأ في الاتصال بقاعدة البيانات. يرجى مراجعة سجل الأخطاء.");
}

function sanitize($data) {
    if (is_array($data)) {
        return array_map('sanitize', $data);
    }
    return htmlspecialchars(strip_tags(trim($data)), ENT_QUOTES, 'UTF-8');
}

function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT, ['cost' => 12]);
}

function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

function generateRandomString($length = 10) {
    return bin2hex(random_bytes($length));
}

function generateExamCode() {
    return strtoupper(substr(md5(uniqid(mt_rand(), true)), 0, 8));
}

function validateSession() {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    if (!isset($_SESSION['exam_data'])) {
        return false;
    }

    if (isset($_SESSION['LAST_ACTIVITY']) &&
        (time() - $_SESSION['LAST_ACTIVITY'] > SESSION_TIMEOUT)) {
        session_unset();
        session_destroy();
        return false;
    }

    $_SESSION['LAST_ACTIVITY'] = time();
    return true;
}

// دوال أمنية جديدة
function setSecurityHeaders() {
    // فحص User Agent المشبوه
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    if (isBlockedUserAgent($user_agent)) {
        logSecurityEvent('blocked_user_agent', "Blocked suspicious user agent: $user_agent", 'high');
        http_response_code(403);
        die('Access Denied');
    }

    // فحص IP محظور
    $client_ip = $_SERVER['REMOTE_ADDR'] ?? '';
    if (isBlockedIP($client_ip)) {
        logSecurityEvent('blocked_ip', "Blocked IP address: $client_ip", 'high');
        http_response_code(403);
        die('Access Denied');
    }

    header('X-Content-Type-Options: nosniff');
    header('X-Frame-Options: DENY');
    header('X-XSS-Protection: 1; mode=block');
    header('Referrer-Policy: strict-origin-when-cross-origin');
    header('Permissions-Policy: geolocation=(), microphone=(), camera=()');

    // إضافة CSP header
    header('Content-Security-Policy: ' . generateCSPHeader());

    // إذا كان HTTPS متاح
    if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') {
        header('Strict-Transport-Security: max-age=31536000; includeSubDomains');
    }
}

function checkRateLimit($identifier, $max_attempts = 5, $time_window = 900) {
    global $pdo;

    try {
        // إنشاء جدول rate_limiting إذا لم يكن موجود
        $pdo->exec("CREATE TABLE IF NOT EXISTS rate_limiting (
            id INT AUTO_INCREMENT PRIMARY KEY,
            identifier VARCHAR(255) NOT NULL,
            attempts INT DEFAULT 1,
            last_attempt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY idx_identifier (identifier)
        )");

        // تنظيف السجلات القديمة
        $pdo->prepare("DELETE FROM rate_limiting WHERE last_attempt < DATE_SUB(NOW(), INTERVAL ? SECOND)")
            ->execute([$time_window]);

        // فحص المحاولات الحالية
        $stmt = $pdo->prepare("SELECT attempts FROM rate_limiting WHERE identifier = ?");
        $stmt->execute([$identifier]);
        $record = $stmt->fetch();

        if ($record && $record['attempts'] >= $max_attempts) {
            return false; // تم تجاوز الحد الأقصى
        }

        // تحديث أو إضافة المحاولة
        $pdo->prepare("INSERT INTO rate_limiting (identifier, attempts) VALUES (?, 1)
                      ON DUPLICATE KEY UPDATE attempts = attempts + 1")
            ->execute([$identifier]);

        return true;
    } catch (PDOException $e) {
        error_log("Rate limiting error: " . $e->getMessage());
        return true; // في حالة الخطأ، اسمح بالمتابعة
    }
}

function clearRateLimit($identifier) {
    global $pdo;
    try {
        $pdo->prepare("DELETE FROM rate_limiting WHERE identifier = ?")->execute([$identifier]);
    } catch (PDOException $e) {
        error_log("Clear rate limit error: " . $e->getMessage());
    }
}

function logSecurityEvent($event_type, $details, $severity = 'medium') {
    // تسجيل بسيط في ملف الأخطاء
    $log_message = date('Y-m-d H:i:s') . " [$severity] $event_type: $details";
    error_log($log_message);
}