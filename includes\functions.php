<?php
// دالة للتحقق من تسجيل الدخول مع فحص انتهاء الجلسة
function isLoggedIn() {
    if (!isset($_SESSION['user_id']) || empty($_SESSION['user_id'])) {
        return false;
    }

    // فحص انتهاء صلاحية الجلسة
    if (isset($_SESSION['LAST_ACTIVITY']) &&
        (time() - $_SESSION['LAST_ACTIVITY'] > SESSION_TIMEOUT)) {
        session_unset();
        session_destroy();
        return false;
    }

    // تحديث وقت آخر نشاط
    $_SESSION['LAST_ACTIVITY'] = time();
    return true;
}

// دالة للتحقق من صلاحيات المدير
function isAdmin() {
    return isset($_SESSION['role']) && $_SESSION['role'] === 'admin';
}

// دالة للتحقق من صلاحيات مساعد المدير
function isAssistant() {
    return isset($_SESSION['role']) && $_SESSION['role'] === 'assistant';
}

// دالة للحماية من CSRF
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

function verifyCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

// دالة لتسجيل النشاط
function logActivity($user_id, $action, $details = '') {
    global $pdo;
    $stmt = $pdo->prepare("INSERT INTO activity_logs (user_id, action, details, ip_address, user_agent, created_at) VALUES (?, ?, ?, ?, ?, NOW())");
    $stmt->execute([$user_id, $action, $details, $_SERVER['REMOTE_ADDR'], $_SERVER['HTTP_USER_AGENT']]);
}

// دالة للتحقق من صحة الرابط
function validateExamLink($exam_id) {
    global $pdo;
    $stmt = $pdo->prepare("SELECT * FROM exams WHERE id = ? AND status = 'active'");
    $stmt->execute([$exam_id]);
    return $stmt->fetch();
}

// دالة للتحقق من وقت الامتحان
function isExamTimeValid($exam_id) {
    global $pdo;
    $stmt = $pdo->prepare("SELECT start_time, end_time FROM exams WHERE id = ?");
    $stmt->execute([$exam_id]);
    $exam = $stmt->fetch();
    
    if (!$exam) return false;
    
    $now = new DateTime();
    $start_time = new DateTime($exam['start_time']);
    $end_time = new DateTime($exam['end_time']);
    
    return $now >= $start_time && $now <= $end_time;
}

// احذف دوال calculateResult و saveResult وأي استخدام للأعمدة المحذوفة من exam_results

// دالة للتحقق من صحة اسم الطالب
function validateStudentName($name) {
    // إزالة المسافات الزائدة
    $name = trim($name);
    
    // التحقق من الطول
    if (strlen($name) < 3 || strlen($name) > 100) {
        return false;
    }
    
    // السماح بالحروف العربية والإنجليزية والمسافات
    return preg_match('/^[\p{Arabic}\p{Latin}\s]+$/u', $name);
}

// دالة للتحقق من صحة رقم الطالب
function validateStudentId($id) {
    // إزالة المسافات
    $id = trim($id);
    
    // التحقق من الطول (3-50 حرف)
    if (strlen($id) < 3 || strlen($id) > 50) {
        return false;
    }
    
    // السماح بالأرقام والحروف والشرطة والشرطة السفلية
    return preg_match('/^[A-Za-z0-9\-_]+$/', $id);
}

// دالة لتنظيف النص
function cleanText($text) {
    return preg_replace('/[^\p{L}\p{N}\s\-_.,!?()]/u', '', $text);
}

// دالة للتحقق من صحة البريد الإلكتروني
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

// دالة للتحقق من قوة كلمة المرور
function isStrongPassword($password) {
    // على الأقل 8 أحرف، حرف كبير، حرف صغير، رقم
    return strlen($password) >= 8 && 
           preg_match('/[A-Z]/', $password) && 
           preg_match('/[a-z]/', $password) && 
           preg_match('/[0-9]/', $password);
}

// دالة لإرسال إشعار
function sendNotification($user_id, $title, $message, $type = 'info') {
    global $pdo;
    $stmt = $pdo->prepare("INSERT INTO notifications (user_id, title, message, type, created_at) VALUES (?, ?, ?, ?, NOW())");
    return $stmt->execute([$user_id, $title, $message, $type]);
}

// دالة للحصول على الإشعارات غير المقروءة
function getUnreadNotifications($user_id) {
    global $pdo;
    $stmt = $pdo->prepare("SELECT * FROM notifications WHERE user_id = ? AND is_read = 0 ORDER BY created_at DESC");
    $stmt->execute([$user_id]);
    return $stmt->fetchAll();
}

// دالة لتحديد الإشعار كمقروء
function markNotificationAsRead($notification_id) {
    global $pdo;
    $stmt = $pdo->prepare("UPDATE notifications SET is_read = 1 WHERE id = ?");
    return $stmt->execute([$notification_id]);
}

// دالة بسيطة للتحقق من الصلاحيات
function preventDirectAccess($allowed_roles = ['admin', 'assistant']) {
    if (!isLoggedIn()) {
        header('Location: ../index.php');
        exit();
    }

    if (!empty($allowed_roles) && !in_array($_SESSION['role'], $allowed_roles)) {
        header('Location: ../index.php');
        exit();
    }
}