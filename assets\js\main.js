// دالة لتهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializeTooltips();
    initializeModals();
    initializeForms();
    initializeTimer();
    initializeExamInterface();
});

// تهيئة Tooltips
function initializeTooltips() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

// تهيئة Modals
function initializeModals() {
    var modalTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="modal"]'));
    var modalList = modalTriggerList.map(function (modalTriggerEl) {
        return new bootstrap.Modal(modalTriggerEl);
    });
}

// تهيئة النماذج
function initializeForms() {
    const forms = document.querySelectorAll('.needs-validation');
    
    forms.forEach(form => {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        });
    });
}

// تهيئة المؤقت
function initializeTimer() {
    const timerElement = document.getElementById('exam-timer');
    if (!timerElement) return;
    
    const endTime = new Date(timerElement.dataset.endTime).getTime();
    
    const timer = setInterval(function() {
        const now = new Date().getTime();
        const distance = endTime - now;
        
        if (distance < 0) {
            clearInterval(timer);
            timerElement.innerHTML = "انتهى الوقت!";
            timerElement.classList.add('text-danger');
            // إرسال الامتحان تلقائياً
            submitExam();
            return;
        }
        
        const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((distance % (1000 * 60)) / 1000);
        
        timerElement.innerHTML = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        
        // تحذير عند اقتراب انتهاء الوقت
        if (distance < 300000) { // 5 دقائق
            timerElement.classList.add('text-danger');
        }
    }, 1000);
}

// تهيئة واجهة الامتحان
function initializeExamInterface() {
    // اختيار الإجابات
    const answerOptions = document.querySelectorAll('.answer-option');
    answerOptions.forEach(option => {
        option.addEventListener('click', function() {
            const questionId = this.dataset.questionId;
            const answer = this.dataset.answer;
            
            // إزالة الاختيار السابق
            document.querySelectorAll(`[data-question-id="${questionId}"]`).forEach(opt => {
                opt.classList.remove('selected');
            });
            
            // تحديد الاختيار الجديد
            this.classList.add('selected');
            
            // حفظ الإجابة
            saveAnswer(questionId, answer);
        });
    });
    
    // زر إرسال الامتحان
    const submitBtn = document.getElementById('submit-exam');
    if (submitBtn) {
        submitBtn.addEventListener('click', function(e) {
            e.preventDefault();
            if (confirm('هل أنت متأكد من إرسال الامتحان؟')) {
                submitExam();
            }
        });
    }
}

// حفظ الإجابة
function saveAnswer(questionId, answer) {
    const formData = new FormData();
    formData.append('question_id', questionId);
    formData.append('answer', answer);
    formData.append('action', 'save_answer');
    
    fetch('ajax/save_answer.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('تم حفظ الإجابة بنجاح', 'success');
        } else {
            showNotification('خطأ في حفظ الإجابة', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('خطأ في الاتصال', 'error');
    });
}

// إرسال الامتحان
function submitExam() {
    const formData = new FormData();
    formData.append('action', 'submit_exam');
    
    fetch('ajax/submit_exam.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('تم إرسال الامتحان بنجاح', 'success');
            setTimeout(() => {
                window.location.href = data.redirect_url;
            }, 2000);
        } else {
            showNotification('خطأ في إرسال الامتحان', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('خطأ في الاتصال', 'error');
    });
}

// عرض الإشعارات
function showNotification(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    // إزالة الإشعار تلقائياً بعد 5 ثواني
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// دالة للبحث في الجداول
function searchTable(tableId, searchInput) {
    const table = document.getElementById(tableId);
    const input = document.getElementById(searchInput);
    const filter = input.value.toUpperCase();
    const tbody = table.querySelector('tbody');
    const rows = tbody.querySelectorAll('tr');
    
    rows.forEach(row => {
        const cells = row.querySelectorAll('td');
        let found = false;
        
        cells.forEach(cell => {
            if (cell.textContent.toUpperCase().indexOf(filter) > -1) {
                found = true;
            }
        });
        
        row.style.display = found ? '' : 'none';
    });
}

// دالة لترتيب الجداول
function sortTable(tableId, columnIndex) {
    const table = document.getElementById(tableId);
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    
    rows.sort((a, b) => {
        const aValue = a.cells[columnIndex].textContent.trim();
        const bValue = b.cells[columnIndex].textContent.trim();
        
        // محاولة تحويل إلى أرقام للترتيب
        const aNum = parseFloat(aValue);
        const bNum = parseFloat(bValue);
        
        if (!isNaN(aNum) && !isNaN(bNum)) {
            return aNum - bNum;
        }
        
        return aValue.localeCompare(bValue, 'ar');
    });
    
    // إعادة ترتيب الصفوف
    rows.forEach(row => tbody.appendChild(row));
}

// دالة لتصدير الجداول إلى Excel
function exportToExcel(tableId, filename) {
    const table = document.getElementById(tableId);
    const wb = XLSX.utils.table_to_book(table, {sheet: "Sheet1"});
    XLSX.writeFile(wb, filename + '.xlsx');
}

// دالة لطباعة الصفحة
function printPage() {
    window.print();
}

// دالة لتحميل البيانات
function loadData(url, targetElement) {
    const element = document.getElementById(targetElement);
    if (!element) return;
    
    element.innerHTML = '<div class="text-center"><div class="spinner"></div></div>';
    
    fetch(url)
    .then(response => response.text())
    .then(data => {
        element.innerHTML = data;
    })
    .catch(error => {
        console.error('Error:', error);
        element.innerHTML = '<div class="alert alert-danger">خطأ في تحميل البيانات</div>';
    });
}

// دالة للتحقق من الاتصال بالإنترنت
function checkOnlineStatus() {
    if (!navigator.onLine) {
        showNotification('لا يوجد اتصال بالإنترنت', 'warning');
    }
}

// مراقبة حالة الاتصال
window.addEventListener('online', function() {
    showNotification('تم استعادة الاتصال بالإنترنت', 'success');
});

window.addEventListener('offline', function() {
    showNotification('انقطع الاتصال بالإنترنت', 'warning');
});

// منع النسخ واللصق في الامتحان
function preventCopyPaste() {
    const examContainer = document.querySelector('.exam-container');
    if (!examContainer) return;
    
    examContainer.addEventListener('copy', function(e) {
        e.preventDefault();
        showNotification('غير مسموح بالنسخ في الامتحان', 'warning');
    });
    
    examContainer.addEventListener('paste', function(e) {
        e.preventDefault();
        showNotification('غير مسموح باللصق في الامتحان', 'warning');
    });
    
    examContainer.addEventListener('contextmenu', function(e) {
        e.preventDefault();
        showNotification('غير مسموح بالنقر بالزر الأيمن', 'warning');
    });
}

// تهيئة منع النسخ واللصق
document.addEventListener('DOMContentLoaded', function() {
    preventCopyPaste();
});

// دالة لتحديث الإحصائيات
function updateStats() {
    // التحقق من وجود العناصر المطلوبة أولاً
    const totalExamsElement = document.getElementById('total-exams');
    const totalStudentsElement = document.getElementById('total-students');
    const activeExamsElement = document.getElementById('active-exams');
    const completedExamsElement = document.getElementById('completed-exams');
    
    // إذا لم تكن العناصر موجودة، لا نحتاج لتحديث الإحصائيات
    if (!totalExamsElement && !totalStudentsElement && !activeExamsElement && !completedExamsElement) {
        return;
    }
    
    // تحديد المسار الصحيح بناءً على الصفحة الحالية
    const isAdminPage = window.location.pathname.includes('/admin/');
    const ajaxPath = isAdminPage ? '../ajax/get_stats.php' : 'ajax/get_stats.php';
    
    fetch(ajaxPath)
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            if (totalExamsElement) totalExamsElement.textContent = data.stats.total_exams;
            if (totalStudentsElement) totalStudentsElement.textContent = data.stats.total_students;
            if (activeExamsElement) activeExamsElement.textContent = data.stats.active_exams;
            if (completedExamsElement) completedExamsElement.textContent = data.stats.completed_exams;
        }
    })
    .catch(error => {
        console.error('Error updating stats:', error);
        // لا نعرض رسالة خطأ للمستخدم لأن هذا تحديث خلفي
    });
}

// تحديث الإحصائيات كل دقيقة
setInterval(updateStats, 60000); 