<?php
// إعدادات الجلسة الآمنة
ini_set('session.cookie_httponly', 1);
ini_set('session.cookie_secure', isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 1 : 0);
ini_set('session.use_strict_mode', 1);

session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// تطبيق headers الأمنية
setSecurityHeaders();

// فحص الصلاحيات والأمان
preventDirectAccess(['admin', 'assistant']);

// تنظيف الجلسات القديمة (مرة واحدة كل 100 طلب)
if (rand(1, 100) === 1) {
    cleanupOldSessions();
}

try {

    $stmt = $pdo->query("SELECT COUNT(*) as total FROM exams");
    $totalExams = $stmt->fetch()['total'];

    $stmt = $pdo->query("SELECT COUNT(*) as active FROM exams WHERE status = 'active'");
    $activeExams = $stmt->fetch()['active'];

    $stmt = $pdo->query("SELECT COUNT(DISTINCT student_id) as total FROM exam_results");
    $totalStudents = $stmt->fetch()['total'];

    $stmt = $pdo->query("SELECT COUNT(*) as completed FROM exams WHERE status = 'completed'");
    $completedExams = $stmt->fetch()['completed'];

    $stmt = $pdo->query("SELECT COUNT(*) as total FROM subjects");
    $totalSubjects = $stmt->fetch()['total'];

    $stmt = $pdo->query("SELECT COUNT(*) as total FROM lectures");
    $totalLectures = $stmt->fetch()['total'];

    $stmt = $pdo->query("SELECT COUNT(*) as total FROM questions");
    $totalQuestions = $stmt->fetch()['total'];

    $stmt = $pdo->query("SELECT e.*, s.name as subject_name FROM exams e
                        LEFT JOIN subjects s ON e.subject_id = s.id
                        ORDER BY e.created_at DESC LIMIT 5");
    $recentExams = $stmt->fetchAll();

    $stmt = $pdo->query("
        SELECT
            er.*,
            e.title as exam_title,
            e.passing_percentage,
            COALESCE(
                ROUND(
                    (SELECT SUM(CASE WHEN sa.selected_answer COLLATE utf8mb4_unicode_ci = q.correct_answer THEN q.points ELSE 0 END)
                     FROM student_answers sa
                     JOIN questions q ON sa.question_id = q.id
                     WHERE sa.exam_id = er.exam_id AND sa.student_id COLLATE utf8mb4_unicode_ci = er.student_id AND sa.attempt_id = er.id) * 100.0 /
                    NULLIF((SELECT SUM(q.points)
                            FROM student_answers sa
                            JOIN questions q ON sa.question_id = q.id
                            WHERE sa.exam_id = er.exam_id AND sa.student_id COLLATE utf8mb4_unicode_ci = er.student_id AND sa.attempt_id = er.id), 0),
                    2
                ), 0
            ) as percentage
        FROM exam_results er
        LEFT JOIN exams e ON er.exam_id = e.id
        WHERE er.end_time IS NOT NULL
        ORDER BY er.submitted_at DESC
        LIMIT 10
    ");
    $recentResults = $stmt->fetchAll();

} catch (PDOException $e) {
    $error = 'خطأ في تحميل البيانات';
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>لوحة التحكم - نظام الامتحانات الإلكترونية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;500;600&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            background: #183046;
            min-height: 100vh;
            font-family: 'Cairo', sans-serif;
            overflow-x: hidden;
            overflow-y: auto;
        }
        .navbar {
            background: #fff !important;
            box-shadow: 0 2px 10px rgba(0,0,0,0.07);
            border-bottom: 1px solid #e3e6f0;
        }
        .navbar-brand {
            font-weight: 700;
            font-size: 1.2rem;
            color: #2c3e50 !important;
            line-height: 1.2;
            display: flex;
            align-items: center;
        }

        .brand-text {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .nav-link, .navbar-nav .nav-link {
            color: #2c3e50 !important;
            font-weight: 600;
            border-radius: 25px;
            margin: 0 0.3rem;
        }
        .nav-link.active, .nav-link:hover {
            background: #2196f3;
            color: #fff !important;
        }
        .dashboard-header {
            margin-top: 2.5rem;
            margin-bottom: 2.5rem;
            text-align: right;
        }
        .dashboard-title {
            font-size: 2.3rem;
            font-weight: 900;
            color: #fff;
            margin-bottom: 0.5rem;
        }
        .dashboard-subtitle {
            color: #e0e0e0;
            font-size: 1.1rem;
            font-weight: 400;
        }
        .stat-card {
            border-radius: 18px;
            background: #fff;
            box-shadow: 0 8px 32px rgba(102,126,234,0.08);
            padding: 2.2rem 1.5rem 1.5rem 1.5rem;
            text-align: center;
            margin-bottom: 2rem;
            transition: box-shadow 0.2s;
        }
        .stat-card:hover {
            box-shadow: 0 16px 48px rgba(102,126,234,0.13);
        }
        .stat-icon {
            font-size: 2.5rem;
            margin-bottom: 0.7rem;
            color: #2196f3;
        }
        .stat-number {
            font-size: 2.2rem;
            font-weight: 900;
            color: #2c3e50;
        }
        .stat-label {
            color: #888;
            font-size: 1.1rem;
            font-weight: 600;
        }
        .section-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 1.5rem;
        }
        .modern-card {
            background: #fff;
            border-radius: 16px;
            box-shadow: 0 4px 24px rgba(102,126,234,0.08);
            padding: 1.5rem 1.2rem;
            margin-bottom: 2rem;
        }
        .modern-card .card-header {
            background: none;
            border: none;
            padding: 0 0 1rem 0;
            color: #2196f3;
            font-weight: 700;
            font-size: 1.2rem;
        }
        .modern-card .list-group-item {
            border: none;
            border-bottom: 1px solid #f0f0f0;
            padding: 0.7rem 0.2rem;
        }
        .modern-card .list-group-item:last-child {
            border-bottom: none;
        }
        .badge {
            font-size: 1rem;
            border-radius: 8px;
            padding: 0.4em 1em;
        }
        .quick-actions .btn {
            border-radius: 12px;
            font-weight: 700;
            font-size: 1.05rem;
            margin-bottom: 1rem;
        }
        .footer {
            background: #2c3e50;
            color: #fff;
            padding: 2.5rem 0 1rem;
            margin-top: 3rem;
        }
        .footer-title {
            font-size: 1.2rem;
            font-weight: 700;
        }
        .footer-bottom {
            border-top: 1px solid rgba(255,255,255,0.1);
            padding-top: 1rem;
            text-align: center;
            opacity: 0.7;
        }

        @media (max-width: 991px) {
            .dashboard-header {
                margin-top: 1.5rem;
                text-align: center;
            }
            .stat-card {
                margin-bottom: 1.2rem;
                padding: 1.5rem 1rem;
            }
            .navbar-brand {
                font-size: 1rem;
                max-width: none;
            }
            .dashboard-title {
                font-size: 1.8rem;
            }
        }

        .mobile-nav-buttons {
            display: flex !important;
            align-items: center;
            gap: 0.5rem;
            z-index: 1000;
        }

        .mobile-nav-buttons .btn {
            width: 40px !important;
            height: 40px !important;
            border-radius: 50% !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            padding: 0 !important;
            border-width: 2px !important;
            transition: all 0.3s ease;
            color: #fff !important;
            border-color: rgba(255, 255, 255, 0.8) !important;
            margin: 0 !important;
        }

        .mobile-nav-buttons .btn i {
            font-size: 16px;
        }

        .mobile-nav-buttons .btn-outline-light {
            background-color: rgba(0, 123, 255, 0.2) !important;
            border-color: #007bff !important;
            color: #007bff !important;
        }

        .mobile-nav-buttons .btn-outline-light:hover {
            background-color: rgba(0, 123, 255, 0.3) !important;
            border-color: #0056b3 !important;
            color: #0056b3 !important;
        }

        .mobile-nav-buttons .btn-outline-danger {
            background-color: rgba(220, 53, 69, 0.2) !important;
            border-color: #dc3545 !important;
            color: #dc3545 !important;
        }

        .mobile-nav-buttons .btn-outline-danger:hover {
            background-color: rgba(220, 53, 69, 0.3) !important;
            border-color: #c82333 !important;
            color: #c82333 !important;
        }

        @media (max-width: 768px) {
            body {
                -webkit-text-size-adjust: 100%;
                -ms-text-size-adjust: 100%;
                touch-action: pan-y;
                overflow-y: auto;
                -webkit-overflow-scrolling: touch;
            }

            .brand-text {
                max-width: calc(100vw - 140px);
            }

            .container {
                padding-left: 15px;
                padding-right: 15px;
            }

            .navbar-brand {
                font-size: 0.9rem;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                max-width: none;
                flex: 1;
                min-width: 0;
            }

            .navbar .container {
                display: flex;
                align-items: center;
                justify-content: space-between;
                position: relative;
                gap: 0.5rem;
            }

            .navbar-brand {
                order: 1;
                flex: 1;
                margin-right: 0.5rem;
            }

            .mobile-nav-buttons {
                order: 2;
                flex-shrink: 0;
                display: flex !important;
                visibility: visible !important;
                opacity: 1 !important;
            }

            .navbar-toggler {
                order: 3;
                flex-shrink: 0;
                margin-left: 0.5rem;
            }

            @media (max-width: 991.98px) {
                .mobile-nav-buttons {
                    display: flex !important;
                }
            }

            .navbar-toggler {
                border: none;
                padding: 0.25rem 0.5rem;
                font-size: 1.1rem;
            }

            .navbar-toggler:focus {
                box-shadow: none;
            }

            .navbar-nav {
                text-align: center;
                padding-top: 1rem;
            }

            .nav-link {
                padding: 0.8rem 1rem !important;
                border-bottom: 1px solid rgba(255,255,255,0.1);
            }

            .dropdown-menu {
                border: none;
                box-shadow: 0 4px 15px rgba(0,0,0,0.1);
                border-radius: 8px;
                min-width: 200px;
                padding: 0.5rem 0;
                margin-top: 0.5rem;
            }

            .dropdown-item {
                padding: 0.8rem 1.2rem;
                font-size: 0.95rem;
                color: #333;
                transition: all 0.2s ease;
            }

            .dropdown-item:hover {
                background-color: #f8f9fa;
                color: #007bff;
            }

            .dropdown-item i {
                margin-left: 0.5rem;
                width: 16px;
                text-align: center;
            }

            .dashboard-header {
                margin-top: 1rem;
                margin-bottom: 1.5rem;
                text-align: center;
            }

            .dashboard-title {
                font-size: 1.5rem;
                margin-bottom: 0.3rem;
            }

            .dashboard-subtitle {
                font-size: 0.9rem;
            }

            .stat-card {
                padding: 1.2rem 0.8rem;
                margin-bottom: 1rem;
                border-radius: 12px;
            }

            .stat-icon {
                font-size: 2rem;
                margin-bottom: 0.5rem;
            }

            .stat-number {
                font-size: 1.8rem;
                margin-bottom: 0.3rem;
            }

            .stat-label {
                font-size: 0.9rem;
            }

            .recent-section {
                margin-top: 2rem;
            }

            .section-title {
                font-size: 1.3rem;
                margin-bottom: 1rem;
            }

            .recent-item {
                padding: 1rem;
                margin-bottom: 0.8rem;
                border-radius: 8px;
            }

            .recent-title {
                font-size: 0.95rem;
                margin-bottom: 0.3rem;
            }

            .recent-meta {
                font-size: 0.8rem;
            }

            .footer {
                margin-top: 2rem;
                padding: 1.5rem 0 1rem;
                text-align: center;
            }

            .footer-title {
                font-size: 1rem;
                margin-bottom: 0.5rem;
            }

            .footer-bottom {
                font-size: 0.8rem;
                opacity: 0.8;
            }

            .table-responsive {
                border: none;
                margin-bottom: 1rem;
            }

            .table {
                font-size: 0.85rem;
            }

            .table th,
            .table td {
                padding: 0.5rem 0.3rem;
                white-space: nowrap;
            }
        }

        @media (max-width: 576px) {
            .container {
                padding-left: 8px;
                padding-right: 8px;
            }

            .navbar .container {
                gap: 0.3rem;
            }

            .navbar-brand {
                font-size: 0.8rem;
                max-width: none;
                flex: 1;
                margin-right: 0.3rem;
            }

            .brand-text {
                max-width: calc(100vw - 120px);
            }

            .mobile-nav-buttons {
                gap: 0.2rem;
                display: flex !important;
                visibility: visible !important;
            }

            .mobile-nav-buttons .btn {
                width: 32px;
                height: 32px;
                display: flex !important;
                visibility: visible !important;
            }

            .mobile-nav-buttons .btn i {
                font-size: 12px;
            }

            .navbar-toggler {
                padding: 0.2rem 0.4rem;
                font-size: 1rem;
            }
        }

        @media screen and (max-width: 991px) {
            .d-lg-none {
                display: flex !important;
            }

            .mobile-nav-buttons,
            .mobile-nav-buttons .btn {
                display: flex !important;
                visibility: visible !important;
                opacity: 1 !important;
            }

            .dropdown-menu {
                position: fixed !important;
                top: 60px !important;
                left: 10px !important;
                right: 10px !important;
                width: auto !important;
                margin: 0 !important;
                border-radius: 12px;
                box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            }

            .dropdown-item {
                padding: 1rem 1.5rem;
                font-size: 1rem;
                text-align: center;
            }

            .dashboard-title {
                font-size: 1.3rem;
            }

            .stat-card {
                padding: 1rem 0.5rem;
            }

            .stat-icon {
                font-size: 1.8rem;
            }

            .stat-number {
                font-size: 1.5rem;
            }

            .stat-label {
                font-size: 0.8rem;
            }

            .recent-item {
                padding: 0.8rem;
            }

            .recent-title {
                font-size: 0.9rem;
            }

            .recent-meta {
                font-size: 0.75rem;
            }
        }

        input, select, textarea {
            font-size: 16px !important;
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
        }

        @media (max-width: 768px) {
            .quick-actions .btn {
                padding: 1rem 0.5rem;
                font-size: 0.9rem;
                margin-bottom: 0.8rem;
                border-radius: 8px;
                min-height: 60px;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
            }

            .quick-actions .btn i {
                font-size: 1.3rem;
                margin-bottom: 0.3rem;
                margin-right: 0 !important;
            }
        }

        @media (max-width: 576px) {
            .quick-actions .btn {
                font-size: 0.8rem;
                padding: 0.8rem 0.3rem;
                min-height: 55px;
            }

            .quick-actions .btn i {
                font-size: 1.1rem;
            }
        }

        @media (max-width: 768px) {
            .list-group-item {
                padding: 1rem 0.8rem;
                border-left: none;
                border-right: none;
            }

            .list-group-item h6 {
                font-size: 0.95rem;
                margin-bottom: 0.3rem;
            }

            .list-group-item small {
                font-size: 0.8rem;
                display: block;
                margin-bottom: 0.3rem;
            }

            .badge {
                font-size: 0.7rem;
                padding: 0.3rem 0.6rem;
            }

            .modern-card .card-header {
                padding: 1rem;
                font-size: 1rem;
            }

            .d-flex.justify-content-between {
                flex-direction: column;
                align-items: flex-start !important;
            }

            .d-flex.justify-content-between .badge {
                align-self: flex-end;
                margin-top: 0.5rem;
            }
        }

        @media (max-width: 576px) {
            .list-group-item {
                padding: 0.8rem 0.5rem;
            }

            .list-group-item h6 {
                font-size: 0.9rem;
            }

            .list-group-item small {
                font-size: 0.75rem;
            }

            .modern-card .card-header {
                padding: 0.8rem;
                font-size: 0.95rem;
            }
        }

        .nav-link, .btn, .card {
            -webkit-tap-highlight-color: transparent;
            touch-action: manipulation;
        }

        html, body {
            height: 100%;
            -webkit-overflow-scrolling: touch;
        }

        .container-fluid, .container {
            touch-action: pan-y;
        }

        .row {
            margin-left: 0;
            margin-right: 0;
        }

        .col-md-3, .col-md-4, .col-md-6, .col-6, .col-12 {
            padding-left: 0.5rem;
            padding-right: 0.5rem;
        }

        @media (max-width: 991px) {
            .dashboard-header {margin-top: 1.5rem;}
            .navbar-brand {
                font-size: 1rem;
                max-width: none;
            }
        }

        @media (max-width: 768px) {
            .container {
                padding-left: 15px;
                padding-right: 15px;
            }

            .navbar .container {
                display: flex;
                align-items: center;
                justify-content: space-between;
                position: relative;
                gap: 0.5rem;
            }

            .navbar-brand {
                order: 1;
                flex: 1;
                margin-right: 0.5rem;
                font-size: 0.9rem;
                max-width: none;
                min-width: 0;
            }

            .mobile-nav-buttons {
                order: 2;
                flex-shrink: 0;
                display: flex !important;
                visibility: visible !important;
                opacity: 1 !important;
            }

            .navbar-toggler {
                order: 3;
                flex-shrink: 0;
                margin-left: 0.5rem;
                border: none;
                padding: 0.25rem 0.5rem;
                font-size: 1.1rem;
            }

            .navbar-toggler:focus {
                box-shadow: none;
            }

            .navbar-nav {
                text-align: center;
                padding-top: 1rem;
            }

            .nav-link {
                padding: 0.75rem 1rem;
                margin: 0.25rem 0;
                border-radius: 25px;
                transition: all 0.3s ease;
            }
        }

        @media screen and (max-width: 991px) {
            .d-lg-none {
                display: flex !important;
            }

            .mobile-nav-buttons,
            .mobile-nav-buttons .btn {
                display: flex !important;
                visibility: visible !important;
                opacity: 1 !important;
            }
        }



    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
        <div class="container">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-graduation-cap me-2"></i>
                <span class="brand-text">جامعة المنيا - قسم الـ Forensic</span>
            </a>

            <!-- أزرار منفصلة للموبايل -->
            <div class="mobile-nav-buttons d-lg-none">
                <a href="profile.php" class="btn btn-outline-light btn-sm me-2">
                    <i class="fas fa-user"></i>
                </a>
                <a href="../logout.php" class="btn btn-outline-danger btn-sm me-2">
                    <i class="fas fa-sign-out-alt"></i>
                </a>
            </div>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="dashboard.php">لوحة التحكم</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="subjects.php">المواد</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="exams.php">الامتحانات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="results.php">النتائج</a>
                    </li>
                </ul>

                <!-- قائمة المستخدم للديسكتوب فقط -->
                <ul class="navbar-nav d-none d-lg-flex">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <?php echo htmlspecialchars($_SESSION['full_name'] ?? $_SESSION['username'] ?? 'المستخدم'); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="profile.php">
                                <i class="fas fa-user me-2"></i>الملف الشخصي
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php">
                                <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    <div class="container" style="margin-top: 80px;">
        <div class="dashboard-header">
            <div class="dashboard-title">لوحة التحكم</div>
            <div class="dashboard-subtitle">نظام إدارة الامتحانات الإلكترونية - جامعة المنيا - قسم الـ Forensic</div>
        </div>
        <div class="row g-4 mb-4">
            <div class="col-md-3 col-6">
                <div class="stat-card">
                    <div class="stat-icon"><i class="fas fa-file-alt"></i></div>
                    <div class="stat-number"><?php echo $totalExams; ?></div>
                    <div class="stat-label">إجمالي الامتحانات</div>
                </div>
            </div>
            <div class="col-md-3 col-6">
                <div class="stat-card">
                    <div class="stat-icon"><i class="fas fa-play-circle"></i></div>
                    <div class="stat-number"><?php echo $activeExams; ?></div>
                    <div class="stat-label">الامتحانات النشطة</div>
                </div>
            </div>
            <div class="col-md-3 col-6">
                <div class="stat-card">
                    <div class="stat-icon"><i class="fas fa-users"></i></div>
                    <div class="stat-number"><?php echo $totalStudents; ?></div>
                    <div class="stat-label">إجمالي الطلاب</div>
                </div>
            </div>
            <div class="col-md-3 col-6">
                <div class="stat-card">
                    <div class="stat-icon"><i class="fas fa-check-circle"></i></div>
                    <div class="stat-number"><?php echo $completedExams; ?></div>
                    <div class="stat-label">الامتحانات المكتملة</div>
                </div>
            </div>
        </div>
        <div class="row g-4 mb-4">
            <div class="col-md-4 col-12">
                <div class="stat-card">
                    <div class="stat-icon"><i class="fas fa-book"></i></div>
                    <div class="stat-number"><?php echo $totalSubjects; ?></div>
                    <div class="stat-label">إجمالي المواد</div>
                </div>
            </div>
            <div class="col-md-4 col-12">
                <div class="stat-card">
                    <div class="stat-icon"><i class="fas fa-chalkboard-teacher"></i></div>
                    <div class="stat-number"><?php echo $totalLectures; ?></div>
                    <div class="stat-label">إجمالي المحاضرات</div>
                </div>
            </div>
            <div class="col-md-4 col-12">
                <div class="stat-card">
                    <div class="stat-icon"><i class="fas fa-question-circle"></i></div>
                    <div class="stat-number"><?php echo $totalQuestions; ?></div>
                    <div class="stat-label">إجمالي الأسئلة</div>
                </div>
            </div>
        </div>
        <div class="row mb-4 quick-actions">
            <div class="col-md-4 col-6">
                <a href="subjects.php?action=add" class="btn btn-outline-primary w-100">
                    <i class="fas fa-plus me-2"></i>إضافة مادة جديدة
                </a>
            </div>
            <div class="col-md-4 col-6">
                <a href="exams.php?action=add" class="btn btn-outline-success w-100">
                    <i class="fas fa-plus me-2"></i>إنشاء امتحان جديد
                </a>
            </div>
            <div class="col-md-4 col-6">
                <a href="results.php" class="btn btn-outline-info w-100">
                    <i class="fas fa-chart-bar me-2"></i>عرض النتائج
                </a>
            </div>
        </div>
        <div class="row g-4">
            <div class="col-md-6">
                <div class="modern-card">
                    <div class="card-header"><i class="fas fa-clock me-2"></i>آخر الامتحانات</div>
                    <div class="card-body p-0">
                        <?php if (empty($recentExams)): ?>
                            <p class="text-muted text-center">لا توجد امتحانات حديثة</p>
                        <?php else: ?>
                            <div class="list-group list-group-flush">
                                <?php foreach ($recentExams as $exam): ?>
                                    <div class="list-group-item">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <h6 class="mb-1"><?php echo htmlspecialchars($exam['title']); ?></h6>
                                                <small class="text-muted">
                                                    <?php echo htmlspecialchars($exam['subject_name']); ?> |
                                                    <?php echo date('Y-m-d H:i', strtotime($exam['created_at'])); ?>
                                                </small>
                                            </div>
                                            <span class="badge bg-<?php echo $exam['status'] === 'active' ? 'success' : ($exam['status'] === 'completed' ? 'warning' : 'secondary'); ?>">
                                                <?php echo $exam['status'] === 'active' ? 'نشط' : ($exam['status'] === 'completed' ? 'مكتمل' : 'غير نشط'); ?>
                                            </span>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="modern-card">
                    <div class="card-header"><i class="fas fa-chart-line me-2"></i>آخر النتائج</div>
                    <div class="card-body p-0">
                        <?php if (empty($recentResults)): ?>
                            <p class="text-muted text-center">لا توجد نتائج حديثة</p>
                        <?php else: ?>
                            <div class="list-group list-group-flush">
                                <?php foreach ($recentResults as $result): ?>
                                    <div class="list-group-item">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <h6 class="mb-1"><?php echo htmlspecialchars($result['exam_title']); ?></h6>
                                                <small class="text-muted">
                                                    الطالب: <?php echo $result['student_id']; ?> |
                                                    <?php echo date('Y-m-d H:i', strtotime($result['submitted_at'])); ?>
                                                </small>
                                            </div>
                                            <?php
                                                $percentage = isset($result['percentage']) ? (float)$result['percentage'] : 0;
                                                $passing_percentage = isset($result['passing_percentage']) ? (float)$result['passing_percentage'] : 60;
                                            ?>
                                            <span class="badge bg-<?php echo $percentage >= $passing_percentage ? 'success' : 'danger'; ?>">
                                                <?php echo number_format($percentage, 1); ?>%
                                            </span>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <footer class="footer">
        <div class="container">
            <div class="footer-title">جامعة المنيا - قسم الـ Forensic</div>
            <div class="footer-bottom">
                &copy; 2024 جميع الحقوق محفوظة - جامعة المنيا - قسم الـ Forensic
            </div>
        </div>
    </footer>
    <script>

        document.addEventListener('DOMContentLoaded', function() {

            document.addEventListener('gesturestart', function (e) {
                e.preventDefault();
            });

            document.addEventListener('gesturechange', function (e) {
                e.preventDefault();
            });

            document.addEventListener('gestureend', function (e) {
                e.preventDefault();
            });

            let lastTouchEnd = 0;
            document.addEventListener('touchend', function (event) {
                const now = (new Date()).getTime();
                if (now - lastTouchEnd <= 300) {
                    event.preventDefault();
                }
                lastTouchEnd = now;
            }, false);

            document.addEventListener('touchmove', function (event) {
                if (event.touches.length > 1) {
                    event.preventDefault();
                }
            }, { passive: false });

            function setVH() {
                let vh = window.innerHeight * 0.01;
                document.documentElement.style.setProperty('--vh', `${vh}px`);
            }

            setVH();
            window.addEventListener('resize', setVH);
            window.addEventListener('orientationchange', function() {
                setTimeout(setVH, 100);
            });

            const navLinks = document.querySelectorAll('.navbar-nav .nav-link');
            const navbarCollapse = document.querySelector('.navbar-collapse');

            navLinks.forEach(link => {
                link.addEventListener('click', () => {
                    if (window.innerWidth < 992) {
                        const bsCollapse = new bootstrap.Collapse(navbarCollapse, {
                            toggle: false
                        });
                        bsCollapse.hide();
                    }
                });
            });

            const tables = document.querySelectorAll('.table');
            tables.forEach(table => {
                if (!table.parentElement.classList.contains('table-responsive')) {
                    const wrapper = document.createElement('div');
                    wrapper.className = 'table-responsive';
                    table.parentNode.insertBefore(wrapper, table);
                    wrapper.appendChild(table);
                }
            });

            function optimizeForMobile() {
                const isMobile = window.innerWidth < 768;
                const statCards = document.querySelectorAll('.stat-card');

                statCards.forEach(card => {
                    if (isMobile) {
                        card.style.transform = 'none';
                        card.style.transition = 'box-shadow 0.2s ease';
                    }
                });

                if (isMobile) {
                    document.body.style.touchAction = 'pan-y';
                    document.body.style.overflowY = 'auto';
                    document.body.style.webkitOverflowScrolling = 'touch';
                }
            }

            optimizeForMobile();
            window.addEventListener('resize', optimizeForMobile);

            if (/iPad|iPhone|iPod/.test(navigator.userAgent)) {
                document.body.style.webkitOverflowScrolling = 'touch';
                document.body.style.overflowY = 'auto';
            }

            // إصلاح أزرار الموبايل
            const mobileButtons = document.querySelectorAll('.mobile-nav-buttons .btn');

            mobileButtons.forEach(button => {

                button.style.display = 'flex';
                button.style.visibility = 'visible';
                button.style.opacity = '1';

                button.addEventListener('touchstart', function() {
                    this.style.transform = 'scale(0.95)';
                });

                button.addEventListener('touchend', function() {
                    this.style.transform = 'scale(1)';
                });

                button.addEventListener('touchcancel', function() {
                    this.style.transform = 'scale(1)';
                });
            });

            const mobileButtonsContainer = document.querySelector('.mobile-nav-buttons');
            if (mobileButtonsContainer) {
                mobileButtonsContainer.style.display = 'flex';
                mobileButtonsContainer.style.visibility = 'visible';
                mobileButtonsContainer.style.opacity = '1';
            }

            const mobileButtons = document.querySelectorAll('.mobile-nav-buttons .btn');

            mobileButtons.forEach(button => {

                button.style.display = 'flex';
                button.style.visibility = 'visible';
                button.style.opacity = '1';

                button.addEventListener('touchstart', function() {
                    this.style.transform = 'scale(0.95)';
                });

                button.addEventListener('touchend', function() {
                    this.style.transform = 'scale(1)';
                });

                button.addEventListener('touchcancel', function() {
                    this.style.transform = 'scale(1)';
                });
            });

            const mobileButtonsContainer = document.querySelector('.mobile-nav-buttons');
            if (mobileButtonsContainer) {
                mobileButtonsContainer.style.display = 'flex';
                mobileButtonsContainer.style.visibility = 'visible';
                mobileButtonsContainer.style.opacity = '1';
            }

            const logoutButton = document.querySelector('.mobile-nav-buttons .btn-outline-danger');
            if (logoutButton) {
                logoutButton.addEventListener('click', function(e) {
                    if (!confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                        e.preventDefault();
                    }
                });
            }

            const tables = document.querySelectorAll('.table');
            tables.forEach(table => {
                if (!table.parentElement.classList.contains('table-responsive')) {
                    const wrapper = document.createElement('div');
                    wrapper.className = 'table-responsive';
                    table.parentNode.insertBefore(wrapper, table);
                    wrapper.appendChild(table);
                }
            });
        });

        setInterval(function() {
            // تحديث الإحصائيات كل 30 ثانية
        }, 30000);



    </script>

    <!-- Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>


</body>
</html>