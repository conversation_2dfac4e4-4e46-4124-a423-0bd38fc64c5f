<?php
// إعدادات الجلسة الآمنة
ini_set('session.cookie_httponly', 1);
ini_set('session.cookie_secure', isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 1 : 0);
ini_set('session.use_strict_mode', 1);
ini_set('session.cookie_samesite', 'Strict');

session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// تطبيق headers الأمنية
setSecurityHeaders();

// التحقق من تسجيل الدخول - إعادة توجيه إذا كان مسجل دخول بالفعل
if (isLoggedIn()) {
    header('Location: admin/dashboard.php');
    exit();
}

// إعداد CSRF token
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}
$csrf_token = $_SESSION['csrf_token'];

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // التحقق من CSRF token
    if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
        $error = 'خطأ في التحقق من الأمان';
        logSecurityEvent('csrf_token_mismatch', 'CSRF token mismatch in login', 'high');
    } else {
        $username = trim($_POST['username']);
        $password = $_POST['password'];

        if (empty($username) || empty($password)) {
            $error = 'يرجى ملء جميع الحقول';
        } else {
            // فحص Rate Limiting
            $client_ip = $_SERVER['REMOTE_ADDR'];
            $rate_limit_key = 'login_' . $client_ip;

            if (!checkRateLimit($rate_limit_key)) {
                $error = 'تم تجاوز الحد الأقصى لمحاولات تسجيل الدخول. يرجى المحاولة بعد 15 دقيقة.';
                logSecurityEvent('rate_limit_exceeded', "Rate limit exceeded for IP: $client_ip", 'high');
            } else {

                try {
                    $stmt = $pdo->prepare("SELECT id, username, password, role, full_name FROM users WHERE username = ? OR email = ?");
                    $stmt->execute([$username, $username]);
                    $user = $stmt->fetch();

                    if ($user && password_verify($password, $user['password'])) {
                        // تسجيل دخول ناجح - مسح rate limiting
                        clearRateLimit($rate_limit_key);

                        $_SESSION['user_id'] = $user['id'];
                        $_SESSION['username'] = $user['username'];
                        $_SESSION['role'] = $user['role'];
                        $_SESSION['full_name'] = $user['full_name'] ?? $user['username'];
                        $_SESSION['LAST_ACTIVITY'] = time();
                        $_SESSION['login_time'] = time();

                        session_regenerate_id(true);

                        logSecurityEvent('successful_login', "User {$user['username']} logged in successfully", 'low');

                        header('Location: admin/dashboard.php');
                        exit;
                    } else {
                        $error = 'اسم المستخدم أو كلمة المرور غير صحيحة';
                        logSecurityEvent('failed_login', "Failed login attempt for username: $username", 'medium');
                    }
                } catch (PDOException $e) {
                    $error = 'خطأ في الاتصال بقاعدة البيانات';
                    logSecurityEvent('database_error', "Database error during login: " . $e->getMessage(), 'high');
                }
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>تسجيل الدخول - نظام الامتحانات الإلكترونية</title>
    <link rel="preconnect" href="https://fonts.gstatic.com">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;500;600&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <style>
        *,
        *:before,
        *:after {
            padding: 0;
            margin: 0;
            box-sizing: border-box;
        }

        body {
            background-color: #080710;
            font-family: 'Poppins', 'Cairo', sans-serif;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
            touch-action: manipulation;
        }

        .background {
            width: 430px;
            height: 520px;
            position: absolute;
            transform: translate(-50%, -50%);
            left: 50%;
            top: 50%;
        }

        .background .shape {
            height: 200px;
            width: 200px;
            position: absolute;
            border-radius: 50%;
        }

        .shape:first-child {
            background: linear-gradient(
                #1845ad,
                #23a2f6
            );
            left: -80px;
            top: -80px;
        }

        .shape:last-child {
            background: linear-gradient(
                to right,
                #ff512f,
                #f09819
            );
            right: -30px;
            bottom: -80px;
        }

        form {
            height: 520px;
            width: 400px;
            background-color: rgba(255, 255, 255, 0.13);
            position: absolute;
            transform: translate(-50%, -50%);
            top: 50%;
            left: 50%;
            border-radius: 10px;
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 0 40px rgba(8, 7, 16, 0.6);
            padding: 50px 35px;
        }

        form * {
            font-family: 'Poppins', 'Cairo', sans-serif;
            color: #ffffff;
            letter-spacing: 0.5px;
            outline: none;
            border: none;
        }

        form h3 {
            font-size: 32px;
            font-weight: 500;
            line-height: 42px;
            text-align: center;
        }

        label {
            display: block;
            margin-top: 30px;
            font-size: 16px;
            font-weight: 500;
        }

        input {
            display: block;
            height: 50px;
            width: 100%;
            background-color: rgba(255, 255, 255, 0.07);
            border-radius: 3px;
            padding: 0 10px;
            margin-top: 8px;
            font-size: 14px;
            font-weight: 300;
            transition: all 0.3s ease;
            border: 1px solid transparent;
        }

        input:focus {
            background-color: rgba(255, 255, 255, 0.1);
            box-shadow: 0 0 0 2px rgba(35, 162, 246, 0.5);
            transform: translateY(-2px);
        }

        input:hover:not(:focus) {
            background-color: rgba(255, 255, 255, 0.09);
        }

        ::placeholder {
            color: #e5e5e5;
        }

        button {
            margin-top: 50px;
            width: 100%;
            background-color: #ffffff;
            color: #080710;
            padding: 15px 0;
            font-size: 18px;
            font-weight: 600;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(8, 7, 16, 0.1), transparent);
            transition: left 0.5s ease;
        }

        button:hover {
            background-color: #f0f0f0;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 255, 255, 0.3);
        }

        button:hover::before {
            left: 100%;
        }

        button:active {
            transform: translateY(0);
        }

        .password-container {
            position: relative;
        }

        .password-toggle {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #e5e5e5;
            cursor: pointer;
            font-size: 16px;
            transition: color 0.3s ease;
        }

        .password-toggle:hover {
            color: #ffffff;
        }

        .alert {
            background: rgba(220, 53, 69, 0.2);
            border: 1px solid rgba(220, 53, 69, 0.3);
            border-radius: 5px;
            color: #fff;
            padding: 15px;
            margin-bottom: 20px;
            text-align: center;
            backdrop-filter: blur(10px);
        }

        .alert-success {
            background: rgba(25, 135, 84, 0.2);
            border-color: rgba(25, 135, 84, 0.3);
        }

        @media (max-width: 768px) {
            body {
                overflow-y: auto;
                padding: 20px;
            }

            .background {
                width: 100%;
                height: 100%;
            }

            .background .shape {
                height: 150px;
                width: 150px;
            }

            .shape:first-child {
                left: -50px;
                top: -50px;
            }

            .shape:last-child {
                right: -20px;
                bottom: -50px;
            }

            form {
                width: 100%;
                max-width: 350px;
                height: auto;
                min-height: 450px;
                padding: 40px 30px;
                position: relative;
                transform: none;
                top: auto;
                left: auto;
            }

            form h3 {
                font-size: 28px;
                margin-bottom: 20px;
            }

            label {
                margin-top: 25px;
                font-size: 15px;
            }

            input {
                height: 45px;
                font-size: 16px !important;
                padding: 0 15px;
                -webkit-appearance: none;
                -moz-appearance: none;
                appearance: none;
            }

            button {
                margin-top: 40px;
                padding: 12px 0;
                font-size: 16px;
            }

            .password-toggle {
                left: 12px;
                font-size: 14px;
            }
        }

        @media (max-width: 480px) {
            form {
                padding: 30px 25px;
                max-width: 320px;
            }

            form h3 {
                font-size: 24px;
            }

            input {
                height: 42px;
                font-size: 15px;
            }

            button {
                font-size: 15px;
            }
        }

        input,
        button,
        .password-toggle {
            -webkit-tap-highlight-color: transparent;
            touch-action: manipulation;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }

        input[type="text"],
        input[type="password"],
        input[type="email"] {
            font-size: 16px !important;
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
        }

        @media screen and (-webkit-min-device-pixel-ratio: 0) {
            input:focus {
                font-size: 16px !important;
            }
        }

        .loading {
            opacity: 0.7;
            pointer-events: none;
        }

        .loading::after {
            content: '';
            position: absolute;
            width: 20px;
            height: 20px;
            border: 2px solid transparent;
            border-top: 2px solid #080710;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        @keyframes spin {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="background">
        <div class="shape"></div>
        <div class="shape"></div>
    </div>

    <form method="POST" id="loginForm">
        <h3>تسجيل الدخول</h3>

        <?php if ($error): ?>
            <div class="alert"><?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>

        <?php if ($success): ?>
            <div class="alert alert-success"><?php echo htmlspecialchars($success); ?></div>
        <?php endif; ?>

        <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">

        <label for="username">اسم المستخدم</label>
        <input type="text" placeholder="اسم المستخدم أو البريد الإلكتروني" id="username" name="username" required>

        <label for="password">كلمة المرور</label>
        <div class="password-container">
            <input type="password" placeholder="كلمة المرور" id="password" name="password" required>
            <span class="password-toggle" onclick="togglePassword()">
                <i class="fas fa-eye" id="eyeIcon"></i>
            </span>
        </div>

        <button type="submit">تسجيل الدخول</button>
    </form>

    <script>
        function setVH() {
            let vh = window.innerHeight * 0.01;
            document.documentElement.style.setProperty('--vh', `${vh}px`);
        }

        setVH();
        window.addEventListener('resize', setVH);
        window.addEventListener('orientationchange', function() {
            setTimeout(setVH, 100);
        });

        document.addEventListener('gesturestart', function (e) {
            e.preventDefault();
        });

        document.addEventListener('gesturechange', function (e) {
            e.preventDefault();
        });

        document.addEventListener('gestureend', function (e) {
            e.preventDefault();
        });

        let lastTouchEnd = 0;
        document.addEventListener('touchend', function (event) {
            const now = (new Date()).getTime();
            if (now - lastTouchEnd <= 300) {
                event.preventDefault();
            }
            lastTouchEnd = now;
        }, false);

        document.addEventListener('touchmove', function (event) {
            if (event.scale !== 1) {
                event.preventDefault();
            }
        }, { passive: false });

        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const eyeIcon = document.getElementById('eyeIcon');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                eyeIcon.classList.remove('fa-eye');
                eyeIcon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                eyeIcon.classList.remove('fa-eye-slash');
                eyeIcon.classList.add('fa-eye');
            }
        }

        const loginForm = document.getElementById('loginForm');
        const submitButton = document.querySelector('button[type="submit"]');
        const originalButtonText = submitButton.textContent;

        loginForm.addEventListener('submit', function() {
            submitButton.classList.add('loading');
            submitButton.textContent = 'جاري تسجيل الدخول...';
            submitButton.disabled = true;
        });

        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(alert => {
            setTimeout(() => {
                alert.style.opacity = '0';
                alert.style.transform = 'translateY(-10px)';
                setTimeout(() => {
                    alert.style.display = 'none';
                }, 300);
            }, 5000);
        });

        const inputs = document.querySelectorAll('input');
        inputs.forEach((input, index) => {
            input.addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    if (index < inputs.length - 1) {
                        inputs[index + 1].focus();
                    } else {
                        submitButton.click();
                    }
                }
            });
        });
    </script>
</body>
</html>
