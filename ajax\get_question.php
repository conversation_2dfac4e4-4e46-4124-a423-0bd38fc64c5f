<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

if (!isset($_SESSION['exam_data']) || !isset($_GET['q'])) {
    echo json_encode(['error' => 'بيانات غير صحيحة']);
    exit();
}

$exam_data = $_SESSION['exam_data'];
$current_index = (int)$_GET['q'];

try {

    $stmt = $pdo->prepare("SELECT * FROM exams WHERE id = ? AND status = 'active'");
    $stmt->execute([$exam_data['exam_id']]);
    $exam = $stmt->fetch();

    if (!$exam) {
        echo json_encode(['error' => 'الامتحان غير متاح']);
        exit();
    }

    $stmt = $pdo->prepare("
        SELECT q.*, eq.question_order 
        FROM questions q 
        JOIN exam_questions eq ON q.id = eq.question_id 
        WHERE eq.exam_id = ? AND q.status = 'active'
        ORDER BY eq.question_order
    ");
    $stmt->execute([$exam['id']]);
    $questions = $stmt->fetchAll();

    if (empty($questions)) {
        echo json_encode(['error' => 'لا توجد أسئلة متاحة']);
        exit();
    }

    $q = $questions[$current_index] ?? null;
    if (!$q) {
        echo json_encode(['error' => 'السؤال غير موجود']);
        exit();
    }

    $stmt = $pdo->prepare("SELECT selected_answer FROM student_answers WHERE exam_id = ? AND student_id = ? AND question_id = ?");
    $stmt->execute([$exam['id'], $exam_data['student_id'], $q['id']]);
    $student_answer = $stmt->fetchColumn();

    if (!isset($_SESSION['visited_questions'])) {
        $_SESSION['visited_questions'] = [];
    }
    if (!in_array($current_index, $_SESSION['visited_questions'])) {
        $_SESSION['visited_questions'][] = $current_index;
    }

    echo json_encode([
        'success' => true,
        'question' => [
            'id' => $q['id'],
            'number' => $current_index + 1,
            'total' => count($questions),
            'text' => $q['question_text'],
            'options' => [
                'a' => $q['option_a'],
                'b' => $q['option_b'],
                'c' => $q['option_c'],
                'd' => $q['option_d']
            ],
            'selected_answer' => $student_answer ?: ''
        ]
    ]);

} catch (PDOException $e) {
    echo json_encode(['error' => 'خطأ في تحميل السؤال']);
}