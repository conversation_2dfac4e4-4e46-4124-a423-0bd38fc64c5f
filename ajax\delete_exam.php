<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit();
}

if (!isset($_POST['exam_id']) || empty($_POST['exam_id'])) {
    echo json_encode(['success' => false, 'message' => 'معرف الامتحان مطلوب']);
    exit();
}

$exam_id = (int)$_POST['exam_id'];

try {

    $stmt = $pdo->prepare("SELECT * FROM exams WHERE id = ?");
    $stmt->execute([$exam_id]);
    $exam = $stmt->fetch();

    if (!$exam) {
        echo json_encode(['success' => false, 'message' => 'الامتحان غير موجود']);
        exit();
    }

    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM exam_results WHERE exam_id = ?");
    $stmt->execute([$exam_id]);
    $resultCount = $stmt->fetch()['count'];

    if ($resultCount > 0) {
        echo json_encode(['success' => false, 'message' => 'لا يمكن حذف الامتحان لوجود نتائج مرتبطة به']);
        exit();
    }

    $stmt = $pdo->prepare("DELETE FROM exams WHERE id = ?");
    if ($stmt->execute([$exam_id])) {

        logActivity($_SESSION['user_id'], 'delete_exam', "حذف امتحان: {$exam['title']}");

        echo json_encode([
            'success' => true, 
            'message' => 'تم حذف الامتحان بنجاح'
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'خطأ في حذف الامتحان']);
    }

} catch (PDOException $e) {
    echo json_encode(['success' => false, 'message' => 'خطأ في قاعدة البيانات']);
}
?>