<?php

ob_start();

ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);
error_reporting(0);

session_start();

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode(['success' => false, 'message' => 'طريقة طلب غير صحيحة']);
    exit();
}

require_once '../config/database.php';
require_once '../includes/functions.php';

header('Content-Type: application/json; charset=utf-8');

if (!isset($_SESSION['exam_data'])) {
    if (ob_get_level()) {
        ob_clean();
    }
    echo json_encode(['success' => false, 'message' => 'جلسة الامتحان غير صحيحة']);
    exit();
}

$exam_data = $_SESSION['exam_data'];

try {

    $stmt = $pdo->prepare("SELECT * FROM exams WHERE id = ?");
    $stmt->execute([$exam_data['exam_id']]);
    $exam = $stmt->fetch();

    if (!$exam) {
        if (ob_get_level()) {
            ob_clean();
        }
        echo json_encode(['success' => false, 'message' => 'الامتحان غير موجود']);
        exit();
    }

    // التحقق من عدد المحاولات
    // 0 = دخول واحد فقط، 1 = دخول مرتين، 2 = دخول 3 مرات، إلخ
    $stmt = $pdo->prepare("SELECT COUNT(*) as attempts FROM exam_results WHERE exam_id = ? AND student_id = ? AND end_time IS NOT NULL");
    $stmt->execute([$exam_data['exam_id'], $exam_data['student_id']]);
    $attempts = $stmt->fetch();

    $total_allowed_entries = $exam['max_attempts'] + 1; // المرة الأساسية + المحاولات الإضافية

    if ($attempts['attempts'] >= $total_allowed_entries) {
        if (ob_get_level()) {
            ob_clean();
        }
        if ($exam['max_attempts'] == 0) {
            echo json_encode(['success' => false, 'message' => 'لقد أنهيت الامتحان ولا يُسمح بالإعادة']);
        } else {
            echo json_encode(['success' => false, 'message' => 'لقد استنفذت جميع المحاولات المسموحة لهذا الامتحان']);
        }
        exit();
    }

    $stmt = $pdo->prepare("UPDATE exam_results
        SET end_time = NOW(),
            duration_minutes = ?
        WHERE id = ?");

    if ($stmt->execute([
        $exam_data['duration_minutes'],
        $exam_data['attempt_id']
    ])) {

        logActivity(null, 'exam_submitted', "امتحان مكتمل: {$exam_data['student_name']} - {$exam['title']}");

        unset($_SESSION['exam_data']);

        $selected_questions_key = 'selected_questions_' . $exam_data['exam_id'] . '_' . $exam_data['attempt_id'];
        unset($_SESSION[$selected_questions_key]);

        unset($_SESSION['visited_questions']);

        if (ob_get_level()) {
            ob_clean();
        }

        echo json_encode([
            'success' => true,
            'redirect_url' => "/forensic/exam_result.php/" . urlencode($exam['exam_link']) . "/" . urlencode($exam_data['attempt_id']) . "/" . urlencode($exam_data['student_id'])
        ]);
        exit;
    } else {

        if (ob_get_level()) {
            ob_clean();
        }

        echo json_encode([
            'success' => false,
            'message' => 'حدث خطأ أثناء حفظ نتيجة الامتحان'
        ]);
        exit;
    }

} catch (PDOException $e) {
    error_log("Database Error in submit_exam.php: " . $e->getMessage());

    if (ob_get_level()) {
        ob_clean();
    }

    echo json_encode(['success' => false, 'message' => 'خطأ في قاعدة البيانات']);
    exit;
} catch (Exception $e) {
    error_log("Error in submit_exam.php: " . $e->getMessage());

    if (ob_get_level()) {
        ob_clean();
    }

    echo json_encode(['success' => false, 'message' => 'حدث خطأ غير متوقع']);
    exit;
}

if (ob_get_level()) {
    ob_end_flush();
}