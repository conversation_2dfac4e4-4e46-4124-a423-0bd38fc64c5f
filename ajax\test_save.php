<?php
// منع أي output قبل JSON
ob_start();

// تعيين header
header('Content-Type: application/json; charset=utf-8');

// تنظيف أي output سابق
ob_clean();

// اختبار بسيط
$response = [
    'success' => true,
    'message' => 'Test successful',
    'timestamp' => date('Y-m-d H:i:s'),
    'method' => $_SERVER['REQUEST_METHOD'],
    'post_data' => $_POST,
    'get_data' => $_GET,
    'current_url' => $_SERVER['REQUEST_URI'] ?? 'unknown'
];

echo json_encode($response, JSON_UNESCAPED_UNICODE);

// إنهاء output buffer
ob_end_flush();
?>
