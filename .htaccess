RewriteEngine On

# حماية من هجمات الحقن والاختراق
RewriteCond %{QUERY_STRING} (\<|%3C).*script.*(\>|%3E) [NC,OR]
RewriteCond %{QUERY_STRING} GLOBALS(=|\[|\%[0-9A-Z]{0,2}) [OR]
RewriteCond %{QUERY_STRING} _REQUEST(=|\[|\%[0-9A-Z]{0,2}) [OR]
RewriteCond %{QUERY_STRING} (\<|%3C).*iframe.*(\>|%3E) [NC,OR]
RewriteCond %{QUERY_STRING} (\<|%3C).*object.*(\>|%3E) [NC,OR]
RewriteCond %{QUERY_STRING} (\<|%3C).*embed.*(\>|%3E) [NC,OR]
RewriteCond %{QUERY_STRING} (SELECT|UNION|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|EXECUTE) [NC,OR]
RewriteCond %{QUERY_STRING} (javascript:|vbscript:|onload|onerror|onclick) [NC]
RewriteRule ^(.*)$ - [F,L]

# منع الوصول للملفات المخفية
RewriteRule ^\.(.*)$ - [F,L]

# إعادة توجيه HTTP إلى HTTPS (قم بإلغاء التعليق عند تفعيل SSL)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# إعادة كتابة روابط الامتحانات - الشكل الصحيح
RewriteRule ^exam_entry\.php/([a-zA-Z0-9_]+)/?$ exam_entry.php?link=$1 [L,QSA]
RewriteRule ^exam_questions\.php/([a-zA-Z0-9_]+)/?$ exam_questions.php?link=$1 [L,QSA]
RewriteRule ^exam_result\.php/([a-zA-Z0-9_]+)/?$ exam_result.php?link=$1 [L,QSA]



# حماية الملفات الحساسة

# حماية الملفات الحساسة
<Files "*.sql">
    Order allow,deny
    Deny from all
</Files>

<Files "*.log">
    Order allow,deny
    Deny from all
</Files>

<Files ".env">
    Order allow,deny
    Deny from all
</Files>

# منع الوصول للمجلدات الحساسة
<IfModule mod_rewrite.c>
    # منع الوصول لمجلد config
    RewriteRule ^config/ - [F,L]
    
    # منع الوصول لمجلد includes
    RewriteRule ^includes/ - [F,L]
    
    # منع الوصول لملفات PHP في مجلد assets
    RewriteRule ^assets/.*\.php$ - [F,L]
</IfModule>

# تحسين الأمان
<IfModule mod_headers.c>
    # منع عرض معلومات الخادم
    Header always unset X-Powered-By
    Header always unset Server

    # حماية من XSS
    Header always set X-XSS-Protection "1; mode=block"

    # حماية من Clickjacking - تغيير إلى DENY للحماية القصوى
    Header always set X-Frame-Options "DENY"

    # منع MIME sniffing
    Header always set X-Content-Type-Options "nosniff"

    # سياسة المرجع المحسنة
    Header always set Referrer-Policy "strict-origin-when-cross-origin"

    # سياسة الصلاحيات
    Header always set Permissions-Policy "geolocation=(), microphone=(), camera=(), payment=(), usb=(), magnetometer=(), gyroscope=(), speaker=()"

    # HTTPS Strict Transport Security (قم بإلغاء التعليق عند تفعيل SSL)
    # Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"

    # سياسة أمان المحتوى المحسنة
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://fonts.googleapis.com; font-src 'self' https://cdnjs.cloudflare.com https://fonts.gstatic.com; img-src 'self' data:; connect-src 'self'; frame-ancestors 'none'; base-uri 'self'; form-action 'self';"

    # إضافة headers للملفات الحساسة
    <FilesMatch "\.(php|phtml)$">
        Header always set Cache-Control "no-cache, no-store, must-revalidate"
        Header always set Pragma "no-cache"
        Header always set Expires "0"
    </FilesMatch>
</IfModule>

# منع الوصول لملفات النظام
<Files ~ "^\.">
    Order allow,deny
    Deny from all
</Files>

# منع الوصول لملفات النسخ الاحتياطي
<FilesMatch "\.(bak|backup|old|tmp|temp)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# منع تنفيذ PHP في مجلدات الرفع
<Directory "uploads/">
    php_flag engine off
    AddType text/plain .php .php3 .phtml .pht
    Options -ExecCGI
</Directory>

<Directory "assets/">
    <FilesMatch "\.php$">
        Order allow,deny
        Deny from all
    </FilesMatch>
</Directory>

# حد أقصى لحجم الرفع والتنفيذ
php_value upload_max_filesize 10M
php_value post_max_size 10M
php_value max_execution_time 300
php_value max_input_time 300
php_value memory_limit 128M

# إعدادات الجلسة الآمنة
php_value session.cookie_httponly 1
php_value session.use_only_cookies 1
php_value session.cookie_secure 0
php_value session.use_strict_mode 1
php_value session.cookie_samesite Strict
php_value session.gc_maxlifetime 1800

# منع عرض أخطاء PHP في الإنتاج
php_flag display_errors Off
php_flag display_startup_errors Off
php_value error_reporting 0

# تسجيل الأخطاء
php_flag log_errors On
php_value error_log logs/php_errors.log

# منع الوصول لملفات التكوين الإضافية
<FilesMatch "\.(ini|conf|config)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# ضغط الملفات لتحسين الأداء
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# تحسين التخزين المؤقت
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/ico "access plus 1 month"
    ExpiresByType image/icon "access plus 1 month"
    ExpiresByType text/plain "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType text/html "access plus 0 seconds"
    ExpiresByType application/json "access plus 0 seconds"
</IfModule>

# منع الوصول لمجلدات النظام
Options -Indexes

# حماية إضافية من Directory Traversal
<IfModule mod_alias.c>
    RedirectMatch 403 /\..*$
    RedirectMatch 403 /.*\.sql$
    RedirectMatch 403 /.*\.log$
</IfModule>