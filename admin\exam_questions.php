<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    header('Location: ../index.php');
    exit();
}

// التحقق من صلاحيات المدير
if (!isAdmin() && !isAssistant()) {
    header('Location: ../index.php');
    exit();
}

$message = '';
$error = '';

// التحقق من وجود معرف الامتحان
$exam_id = isset($_GET['exam_id']) ? (int)$_GET['exam_id'] : 0;
if (!$exam_id) {
    header('Location: exams.php');
    exit();
}

// الحصول على معلومات الامتحان
try {
    $stmt = $pdo->prepare("SELECT e.*, 
                          (SELECT GROUP_CONCAT(DISTINCT s.name SEPARATOR ', ') 
                           FROM exam_lectures el 
                           JOIN lectures l ON el.lecture_id = l.id 
                           JOIN subjects s ON l.subject_id = s.id 
                           WHERE el.exam_id = e.id) as subjects_list
                          FROM exams e 
                          WHERE e.id = ?");
    $stmt->execute([$exam_id]);
    $exam = $stmt->fetch();
    
    if (!$exam) {
        header('Location: exams.php');
        exit();
    }
} catch (PDOException $e) {
    error_log("Error loading exam data: " . $e->getMessage());
    $error = 'خطأ في تحميل بيانات الامتحان';
    header('Location: exams.php');
    exit();
}

// التأكد من وجود الامتحان قبل المتابعة
if (!isset($exam) || !$exam) {
    header('Location: exams.php');
    exit();
}

// معالجة إضافة أسئلة للامتحان
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    if (!verifyCSRFToken($_POST['csrf_token'])) {
        $error = 'خطأ في التحقق من الأمان';
    } else {
        if ($_POST['action'] === 'add_questions') {
            $lecture_ids = isset($_POST['lecture_ids']) ? $_POST['lecture_ids'] : [];
            
            if (empty($lecture_ids)) {
                $error = 'يرجى اختيار محاضرة واحدة على الأقل';
            } else {
                try {
                    $pdo->beginTransaction();
                    
                    // الحصول على الأسئلة من المحاضرات المختارة
                    $placeholders = str_repeat('?,', count($lecture_ids) - 1) . '?';
                    $stmt = $pdo->prepare("SELECT id FROM questions WHERE lecture_id IN ($placeholders) AND status = 'active'");
                    $stmt->execute($lecture_ids);
                    $questions = $stmt->fetchAll();
                    
                    if (empty($questions)) {
                        $error = 'لا توجد أسئلة في المحاضرات المختارة';
                    } else {
                        // الحصول على آخر رقم ترتيب في الامتحان
                        $stmt = $pdo->prepare("SELECT COALESCE(MAX(question_order), 0) as max_order FROM exam_questions WHERE exam_id = ?");
                        $stmt->execute([$exam_id]);
                        $max_order = $stmt->fetch()['max_order'];

                        // إضافة الأسئلة للامتحان
                        $stmt = $pdo->prepare("INSERT IGNORE INTO exam_questions (exam_id, question_id, question_order) VALUES (?, ?, ?)");

                        $order = $max_order + 1;
                        $added_count = 0;
                        foreach ($questions as $question) {
                            if ($stmt->execute([$exam_id, $question['id'], $order])) {
                                if ($stmt->rowCount() > 0) { // تم إدراج سؤال جديد
                                    $added_count++;
                                }
                                $order++;
                            }
                        }
                        
                        $pdo->commit();
                        
                        if ($added_count > 0) {
                            $message = "تم إضافة $added_count سؤال للامتحان بنجاح";
                            logActivity($_SESSION['user_id'], 'add_exam_questions', "إضافة $added_count سؤال للامتحان: {$exam['title']}");
                        } else {
                            $error = 'فشل في إضافة الأسئلة للامتحان';
                        }
                    }
                } catch (PDOException $e) {
                    $pdo->rollBack();
                    error_log("Error adding questions to exam: " . $e->getMessage());
                    $error = 'خطأ في قاعدة البيانات: ' . $e->getMessage();
                }
            }
        } elseif ($_POST['action'] === 'remove_question') {
            $question_id = (int)$_POST['question_id'];
            
            try {
                $stmt = $pdo->prepare("DELETE FROM exam_questions WHERE exam_id = ? AND question_id = ?");
                if ($stmt->execute([$exam_id, $question_id])) {
                    if ($stmt->rowCount() > 0) {
                        $message = 'تم إزالة السؤال من الامتحان بنجاح';
                        logActivity($_SESSION['user_id'], 'remove_exam_question', "إزالة سؤال من الامتحان: {$exam['title']}");
                    } else {
                        $error = 'السؤال غير موجود في الامتحان';
                    }
                } else {
                    $error = 'خطأ في إزالة السؤال';
                }
            } catch (PDOException $e) {
                error_log("Error removing question from exam: " . $e->getMessage());
                $error = 'خطأ في قاعدة البيانات: ' . $e->getMessage();
            }
        }
    }
}

// الحصول على أسئلة الامتحان
try {
    $stmt = $pdo->prepare("SELECT q.*, eq.question_order, l.title as lecture_title, l.lecture_number
                          FROM questions q 
                          JOIN exam_questions eq ON q.id = eq.question_id 
                          JOIN lectures l ON q.lecture_id = l.id
                          WHERE eq.exam_id = ?
                          ORDER BY eq.question_order");
    $stmt->execute([$exam_id]);
    $exam_questions = $stmt->fetchAll();
    
    // الحصول على المحاضرات المتاحة للمادة
    $stmt = $pdo->prepare("SELECT l.*, s.name as subject_name,
                          (SELECT COUNT(*) FROM questions WHERE lecture_id = l.id AND status = 'active') as questions_count
                          FROM lectures l 
                          JOIN subjects s ON l.subject_id = s.id
                          JOIN exam_lectures el ON l.id = el.lecture_id
                          WHERE el.exam_id = ? AND l.status = 'active'
                          ORDER BY s.name, l.lecture_number");
    $stmt->execute([$exam_id]);
    $available_lectures = $stmt->fetchAll();
    
} catch (PDOException $e) {
    error_log("Error loading exam questions data: " . $e->getMessage());
    $error = 'خطأ في تحميل البيانات: ' . $e->getMessage();
    $exam_questions = [];
    $available_lectures = [];
}

// تهيئة المتغيرات إذا لم تكن موجودة
if (!isset($exam_questions)) {
    $exam_questions = [];
}
if (!isset($available_lectures)) {
    $available_lectures = [];
}

$csrf_token = generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>أسئلة الامتحان - <?php echo htmlspecialchars($exam['title']); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;500;600&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;500;600&family=Cairo:wght@300;400;600;700&display=swap"//cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;500;600&family=Cairo:wght@300;400;600;700&display=swap"//fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap" rel="stylesheet">
    <link href="../assets/css/mobile.css" rel="stylesheet">
    <style>
        body {
            background: #183046;
            min-height: 100vh;
            font-family: 'Cairo', sans-serif;
            overflow-x: hidden;
            overflow-y: auto;
        }
        .navbar {
            background: #fff !important;
            box-shadow: 0 2px 10px rgba(0,0,0,0.07);
            border-bottom: 1px solid #e3e6f0;
        }
        .navbar-brand {
            font-weight: 700;
            font-size: 1.2rem;
            color: #2c3e50 !important;
            line-height: 1.2;
            display: flex;
            align-items: center;
        }

        .brand-text {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .nav-link, .navbar-nav .nav-link {
            color: #2c3e50 !important;
            font-weight: 600;
            border-radius: 25px;
            margin: 0 0.3rem;
        }
        .nav-link.active, .nav-link:hover {
            background: #2196f3;
            color: #fff !important;
        }
        .exam-questions-header {
            margin-top: 2.5rem;
            margin-bottom: 2.5rem;
            text-align: right;
        }
        .exam-questions-title {
            font-size: 2.3rem;
            font-weight: 900;
            color: #fff;
            margin-bottom: 0.5rem;
        }
        .exam-questions-subtitle {
            color: #e0e0e0;
            font-size: 1.1rem;
            font-weight: 400;
        }

        .modern-card {
            background: #fff;
            border-radius: 16px;
            box-shadow: 0 4px 24px rgba(102,126,234,0.08);
            padding: 1.5rem 1.2rem;
            margin-bottom: 2rem;
        }
        .modern-card .card-header {
            background: none;
            border: none;
            padding: 0 0 1rem 0;
            color: #2196f3;
            font-weight: 700;
            font-size: 1.2rem;
        }
        .modern-card .list-group-item {
            border: none;
            border-bottom: 1px solid #f0f0f0;
            padding: 0.7rem 0.2rem;
        }
        .modern-card .list-group-item:last-child {
            border-bottom: none;
        }
        .card.border-0.shadow-sm {
            border-radius: 18px;
            background: #fff;
            box-shadow: 0 8px 32px rgba(102,126,234,0.08);
            padding: 1.5rem 1.2rem;
        }
        .card-header.bg-primary {
            background: #2196f3 !important;
            color: #fff !important;
            border-radius: 16px 16px 0 0 !important;
            font-weight: 700;
            font-size: 1.2rem;
        }
        .btn-primary, .btn-outline-primary:hover {
            background: #2196f3 !important;
            border-color: #2196f3 !important;
            color: #fff !important;
        }
        .btn-outline-primary {
            color: #2196f3 !important;
            border-color: #2196f3 !important;
            background: #fff !important;
        }
        .btn-outline-primary:focus, .btn-outline-primary:active {
            background: #2196f3 !important;
            color: #fff !important;
        }
        .question-card {
            border: 1px solid #e3e6f0;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            background: #fff;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }
        .question-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #f0f0f0;
        }
        .question-number {
            background: #2196f3;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 700;
        }

        .question-meta {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
            align-items: center;
        }
        .meta-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        .points-badge {
            background: #e8f5e9;
            color: #2e7d32;
        }
        .difficulty-easy {
            background: #e8f5e9;
            color: #2e7d32;
        }
        .difficulty-medium {
            background: #fff3e0;
            color: #f57c00;
        }
        .difficulty-hard {
            background: #ffebee;
            color: #d32f2f;
        }
        .subject-badge {
            background: #e3f2fd;
            color: #1976d2;
        }
        .question-text {
            font-size: 1.1rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 1rem;
            line-height: 1.6;
        }
        .options-list {
            list-style: none;
            padding: 0;
            margin: 0 0 1rem 0;
        }
        .option-item {
            padding: 0.5rem 0;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            align-items: center;
        }
        .option-item:last-child {
            border-bottom: none;
        }
        .option-item.correct {
            background: #d4edda;
            border-radius: 8px;
            padding: 0.5rem;
            margin: 0.25rem 0;
        }
        .option-label {
            font-weight: 700;
            color: #2196f3;
            margin-left: 0.5rem;
            min-width: 20px;
        }
        .option-text {
            flex: 1;
        }

        .modal-content {
            border-radius: 16px;
        }
        .modal-header {
            border-bottom: 1px solid #f0f0f0;
        }
        .modal-title {
            color: #2196f3;
            font-weight: 700;
        }
        .form-label {
            font-weight: 600;
            color: #2c3e50;
        }
        .form-control, .form-select {
            border-radius: 10px;
        }

        .footer {
            background: #2c3e50;
            color: #fff;
            padding: 2.5rem 0 1rem;
            margin-top: 3rem;
        }
        .footer-title {
            font-size: 1.2rem;
            font-weight: 700;
        }
        .footer-bottom {
            border-top: 1px solid rgba(255,255,255,0.1);
            padding-top: 1rem;
            text-align: center;
            opacity: 0.7;
        }

        @media (max-width: 991px) {
            .exam-questions-header {margin-top: 1.5rem;}
        }

        /* تحسينات الموبايل */
        @media (max-width: 768px) {
            .exam-questions-header {
                text-align: center;
                margin-top: 1.5rem;
                margin-bottom: 2rem;
            }

            .exam-questions-title {
                font-size: 1.8rem;
            }

            .exam-questions-subtitle {
                font-size: 1rem;
            }

            .question-card {
                padding: 1rem;
                margin-bottom: 1rem;
            }

            .question-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.75rem;
            }

            .question-meta {
                width: 100%;
                justify-content: flex-start;
            }

            .question-text {
                font-size: 1rem;
            }

        }
    </style>
</head>
<body>
    <?php include '../includes/mobile_navbar.php'; ?>

    <div class="container" style="margin-top: 80px;">
        <div class="exam-questions-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="exam-questions-title">
                        <i class="fas fa-question-circle text-primary me-2"></i>
                        أسئلة الامتحان
                    </div>
                    <div class="exam-questions-subtitle">
                        <?php echo htmlspecialchars($exam['title']); ?> - <?php echo htmlspecialchars($exam['subjects_list']); ?>
                        <span class="badge bg-info ms-2"><?php echo count($exam_questions); ?> سؤال مضافة</span>
                        <span class="badge bg-warning ms-2">العدد المطلوب: <?php echo $exam['questions_count']; ?> سؤال</span>
                        <span class="badge bg-<?php echo $exam['randomize_questions'] ? 'success' : 'secondary'; ?> ms-2">
                            <i class="fas fa-<?php echo $exam['randomize_questions'] ? 'random' : 'sort'; ?> me-1"></i>
                            <?php echo $exam['randomize_questions'] ? 'ترتيب عشوائي' : 'ترتيب منتظم'; ?>
                        </span>
                    </div>
                </div>
                <div>
                    <a href="exams.php" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للامتحانات
                    </a>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addQuestionsModal">
                        <i class="fas fa-plus me-2"></i>
                        إضافة أسئلة
                    </button>
                </div>
            </div>
        </div>

        <!-- Messages -->
        <?php if ($message): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo $message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?php echo $error; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php
        $current_questions_count = count($exam_questions);
        $required_questions_count = $exam['questions_count'];
        if ($current_questions_count < $required_questions_count):
        ?>
            <div class="alert alert-warning alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>تنبيه:</strong> عدد الأسئلة المضافة (<?php echo $current_questions_count; ?>) أقل من العدد المطلوب (<?php echo $required_questions_count; ?>)
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php elseif ($current_questions_count > $required_questions_count): ?>
            <div class="alert alert-info alert-dismissible fade show" role="alert">
                <i class="fas fa-info-circle me-2"></i>
                <strong>معلومات:</strong> عدد الأسئلة المضافة (<?php echo $current_questions_count; ?>) أكثر من العدد المطلوب (<?php echo $required_questions_count; ?>). سيتم اختيار <?php echo $required_questions_count; ?> سؤال فقط.
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php else: ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <strong>ممتاز:</strong> عدد الأسئلة المضافة (<?php echo $current_questions_count; ?>) مطابق للعدد المطلوب
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Exam Questions -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    قائمة أسئلة الامتحان
                    <span class="badge bg-light text-dark ms-2"><?php echo count($exam_questions); ?> سؤال مضافة</span>
                    <span class="badge bg-warning ms-2">العدد المطلوب: <?php echo $exam['questions_count']; ?> سؤال</span>
                </h5>
                <div class="mt-2">
                    <small>
                        <i class="fas fa-info-circle me-1"></i>
                        <?php if ($exam['randomize_questions']): ?>
                            سيتم عرض الأسئلة بترتيب عشوائي للطلاب
                        <?php else: ?>
                            سيتم عرض الأسئلة بالترتيب الموجود في بنك الأسئلة
                        <?php endif; ?>
                    </small>
                </div>
            </div>
            <div class="card-body">
                <?php if (empty($exam_questions)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-question-circle text-muted" style="font-size: 3rem;"></i>
                        <p class="text-muted mt-3">لا توجد أسئلة مضافة للامتحان بعد</p>
                        <div class="alert alert-info mt-3">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>معلومات الامتحان:</strong><br>
                            • العدد المطلوب: <?php echo $exam['questions_count']; ?> سؤال<br>
                            • ترتيب الأسئلة: <?php echo $exam['randomize_questions'] ? 'عشوائي' : 'منتظم حسب بنك الأسئلة'; ?>
                        </div>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addQuestionsModal">
                            <i class="fas fa-plus me-2"></i>
                            إضافة أول سؤال
                        </button>
                    </div>
                <?php else: ?>
                    <?php foreach ($exam_questions as $index => $question): ?>
                        <div class="question-card">
                            <div class="question-header">
                                <div class="d-flex align-items-center">
                                    <div class="question-number">
                                        السؤال رقم <?php echo $question['question_order']; ?>
                                    </div>
                                    <div class="question-meta">
                                        <span class="meta-badge points-badge">
                                            <i class="fas fa-star me-1"></i>
                                            <?php echo $question['points']; ?> نقاط
                                        </span>
                                        <span class="meta-badge difficulty-<?php echo $question['difficulty']; ?>">
                                            <i class="fas fa-signal me-1"></i>
                                            <?php echo $question['difficulty'] === 'easy' ? 'سهل' : ($question['difficulty'] === 'medium' ? 'متوسط' : 'صعب'); ?>
                                        </span>
                                        <span class="meta-badge subject-badge">
                                            <i class="fas fa-book me-1"></i>
                                            <?php echo htmlspecialchars($question['lecture_title']); ?>
                                        </span>
                                    </div>
                                </div>
                                <div>
                                    <button class="btn btn-sm btn-outline-danger"
                                            onclick="removeQuestion(<?php echo $question['id']; ?>, '<?php echo htmlspecialchars(substr($question['question_text'], 0, 30)); ?>')"
                                            title="حذف من الامتحان">
                                        <i class="fas fa-trash me-1"></i>
                                        حذف
                                    </button>
                                </div>
                            </div>

                            <div class="question-text">
                                <?php echo nl2br(htmlspecialchars($question['question_text'])); ?>
                            </div>

                            <ul class="options-list">
                                <?php
                                $options = [
                                    'a' => $question['option_a'],
                                    'b' => $question['option_b'],
                                    'c' => $question['option_c'],
                                    'd' => $question['option_d']
                                ];
                                $answers = ['a' => 'أ', 'b' => 'ب', 'c' => 'ج', 'd' => 'د'];
                                ?>
                                <?php foreach ($options as $key => $option): ?>
                                    <li class="option-item <?php echo $key === $question['correct_answer'] ? 'correct' : ''; ?>">
                                        <span class="option-label"><?php echo $answers[$key]; ?>.</span>
                                        <span class="option-text"><?php echo htmlspecialchars($option); ?></span>
                                        <?php if ($key === $question['correct_answer']): ?>
                                            <i class="fas fa-check-circle text-success ms-2"></i>
                                        <?php endif; ?>
                                    </li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Add Questions Modal -->
    <div class="modal fade" id="addQuestionsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-plus me-2"></i>
                        إضافة أسئلة للامتحان
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" class="needs-validation" novalidate>
                    <div class="modal-body">
                        <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                        <input type="hidden" name="action" value="add_questions">

                        <div class="alert alert-info mb-3">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>معلومات الامتحان:</strong><br>
                            • العدد المطلوب: <?php echo $exam['questions_count']; ?> سؤال<br>
                            • ترتيب الأسئلة: <?php echo $exam['randomize_questions'] ? 'عشوائي' : 'منتظم حسب بنك الأسئلة'; ?>
                        </div>

                        <div class="mb-3">
                            <label for="lecture_ids" class="form-label">اختر المحاضرات *</label>
                            <select class="form-select" id="lecture_ids" name="lecture_ids[]" multiple required style="height: 200px;">
                                <?php foreach ($available_lectures as $lecture): ?>
                                    <option value="<?php echo $lecture['id']; ?>">
                                        <?php echo htmlspecialchars($lecture['subject_name']); ?> - المحاضرة <?php echo $lecture['lecture_number']; ?>: <?php echo htmlspecialchars($lecture['title']); ?>
                                        (<?php echo $lecture['questions_count']; ?> سؤال)
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="form-text">
                                <i class="fas fa-info-circle me-1"></i>
                                اضغط Ctrl (أو Cmd على Mac) لاختيار محاضرات متعددة
                            </div>
                            <div class="invalid-feedback">يرجى اختيار محاضرة واحدة على الأقل</div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            إضافة الأسئلة
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <footer class="footer">
        <div class="container">
            <div class="footer-title">جامعة المنيا - قسم الـ Forensic</div>
            <div class="footer-bottom">
                &copy; 2024 جميع الحقوق محفوظة - جامعة المنيا - قسم الـ Forensic
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function removeQuestion(questionId, questionText) {
            if (confirm(`هل أنت متأكد من حذف السؤال: ${questionText}... من الامتحان؟`)) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                    <input type="hidden" name="action" value="remove_question">
                    <input type="hidden" name="question_id" value="${questionId}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }
    </script>

    <!-- Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/mobile.js"></script>
</body>
</html>
