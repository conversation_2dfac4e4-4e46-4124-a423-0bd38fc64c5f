<?php
// منع أي output قبل JSON
ob_start();

// إعدادات الأمان
ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);
error_reporting(0);

// تعيين header للـ JSON
header('Content-Type: application/json; charset=utf-8');

// تنظيف أي output سابق
ob_clean();

session_start();

// تطبيق headers الأمنية
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');

// تشخيص أولي
error_log("save_answer.php called - Method: " . $_SERVER['REQUEST_METHOD']);
error_log("save_answer.php - POST data: " . json_encode($_POST));

// فحص طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'error' => 'طريقة طلب غير صحيحة', 'method' => $_SERVER['REQUEST_METHOD']]);
    exit();
}

// محاولة تحميل الملفات مع معالجة الأخطاء
try {
    require_once 'config/database.php';
} catch (Exception $e) {
    echo json_encode(['success' => false, 'error' => 'Database config error: ' . $e->getMessage()]);
    exit();
}

// تشخيص الجلسة
error_log("save_answer.php - Session ID: " . session_id());
error_log("save_answer.php - Session data exists: " . (isset($_SESSION['exam_data']) ? 'yes' : 'no'));

if (!isset($_SESSION['exam_data']) || !isset($_SESSION['exam_data']['exam_id']) || !isset($_SESSION['exam_data']['student_id'])) {
    $session_debug = [
        'session_id' => session_id(),
        'exam_data_exists' => isset($_SESSION['exam_data']),
        'session_keys' => array_keys($_SESSION ?? [])
    ];
    
    echo json_encode([
        'success' => false, 
        'error' => 'جلسة الامتحان غير صالحة', 
        'debug' => $session_debug
    ]);
    exit();
}

if (!isset($_POST['question_id']) || !isset($_POST['answer'])) {
    echo json_encode([
        'success' => false,
        'error' => 'بيانات غير مكتملة',
        'received' => array_keys($_POST)
    ]);
    exit();
}

$exam_data = $_SESSION['exam_data'];
$question_id = (int)$_POST['question_id'];
$answer = $_POST['answer'];

try {
    $pdo->beginTransaction();

    // التحقق من وجود السؤال في الامتحان
    $stmt = $pdo->prepare("
        SELECT id FROM questions 
        WHERE id = ? AND lecture_id IN (
            SELECT id FROM lectures WHERE exam_id = ?
        )
    ");
    $stmt->execute([$question_id, $exam_data['exam_id']]);
    
    if (!$stmt->fetch()) {
        throw new Exception('السؤال غير موجود في هذا الامتحان');
    }

    // حفظ أو تحديث الإجابة
    $stmt = $pdo->prepare("
        INSERT INTO student_answers (exam_id, student_id, question_id, answer, attempt_id, created_at)
        VALUES (?, ?, ?, ?, ?, NOW())
        ON DUPLICATE KEY UPDATE
        answer = VALUES(answer),
        updated_at = NOW()
    ");
    
    $stmt->execute([
        $exam_data['exam_id'],
        $exam_data['student_id'],
        $question_id,
        $answer,
        $exam_data['attempt_id']
    ]);

    $pdo->commit();

    echo json_encode([
        'success' => true,
        'message' => 'تم حفظ الإجابة بنجاح',
        'question_id' => $question_id,
        'answer' => $answer
    ]);

} catch (Exception $e) {
    $pdo->rollback();
    error_log("save_answer.php error: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'error' => 'خطأ في حفظ الإجابة: ' . $e->getMessage()
    ]);
}

// تنظيف output buffer وإرسال JSON فقط
ob_clean();
ob_end_flush();
?>
