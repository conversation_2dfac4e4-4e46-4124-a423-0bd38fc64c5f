<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

if (!isLoggedIn()) {
    header('Location: ../index.php');
    exit();
}

if (!isAdmin() && !isAssistant()) {
    header('Location: ../index.php');
    exit();
}

$message = '';
$error = '';

$lecture_id = isset($_GET['lecture_id']) ? (int)$_GET['lecture_id'] : 0;
if (!$lecture_id) {
    header('Location: subjects.php');
    exit();
}

try {
    $stmt = $pdo->prepare("SELECT l.*, s.name as subject_name FROM lectures l 
                          JOIN subjects s ON l.subject_id = s.id 
                          WHERE l.id = ?");
    $stmt->execute([$lecture_id]);
    $lecture = $stmt->fetch();

    if (!$lecture) {
        header('Location: subjects.php');
        exit();
    }
} catch (PDOException $e) {
    $error = 'خطأ في تحميل بيانات المحاضرة';
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    if (!verifyCSRFToken($_POST['csrf_token'])) {
        $error = 'خطأ في التحقق من الأمان';
    } else {
        if ($_POST['action'] === 'bulk_add') {
            $questions_text = trim($_POST['questions_text']);

            if (empty($questions_text)) {
                $error = 'يرجى إدخال الأسئلة';
            } else {

                $questions = parseQuestionsText($questions_text);

                if (empty($questions)) {
                    $error = 'لم يتم العثور على أسئلة صحيحة في النص المدخل';
                } else {
                    $success_count = 0;
                    $error_count = 0;

                    try {
                        $pdo->beginTransaction();

                        $stmt = $pdo->prepare("INSERT INTO questions (lecture_id, question_text, option_a, option_b, option_c, option_d, correct_answer, points, difficulty, created_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");

                        foreach ($questions as $question) {
                            if ($stmt->execute([
                                $lecture_id,
                                $question['question_text'],
                                $question['option_a'],
                                $question['option_b'],
                                $question['option_c'],
                                $question['option_d'],
                                $question['correct_answer'],
                                $question['points'],
                                $question['difficulty'],
                                $_SESSION['user_id']
                            ])) {
                                $success_count++;
                            } else {
                                $error_count++;
                            }
                        }

                        $pdo->commit();

                        if ($success_count > 0) {
                            $message = "تم إضافة $success_count سؤال بنجاح";
                            if ($error_count > 0) {
                                $message .= " و $error_count سؤال فشل في الإضافة";
                            }
                            logActivity($_SESSION['user_id'], 'bulk_add_questions', "إضافة $success_count سؤال للمحاضرة: {$lecture['title']}");
                        } else {
                            $error = 'فشل في إضافة جميع الأسئلة';
                        }

                    } catch (PDOException $e) {
                        $pdo->rollBack();
                        $error = 'خطأ في قاعدة البيانات: ' . $e->getMessage();
                    }
                }
            }
        }
    }
}

function parseQuestionsText($text) {
    $questions = [];
    $lines = explode("\n", $text);
    $current_question = null;

    foreach ($lines as $line) {
        $line = trim($line);
        if (empty($line)) continue;

        if (preg_match('/^\d+\.\s*(.+)$/', $line, $matches)) {

            if ($current_question && isset($current_question['question_text'])) {
                $questions[] = $current_question;
            }

            $current_question = [
                'question_text' => trim($matches[1]),
                'option_a' => '',
                'option_b' => '',
                'option_c' => '',
                'option_d' => '',
                'correct_answer' => '',
                'points' => 1,
                'difficulty' => 'medium'
            ];
        }

        elseif (preg_match('/^([A-D])\)\s*(.+)$/', $line, $matches)) {
            if ($current_question) {
                $option_key = strtolower($matches[1]);
                $current_question["option_$option_key"] = trim($matches[2]);
            }
        }

        elseif (preg_match('/^ans\/([A-D])$/i', $line, $matches)) {
            if ($current_question) {
                $current_question['correct_answer'] = strtolower($matches[1]);
            }
        }
    }

    if ($current_question && isset($current_question['question_text'])) {
        $questions[] = $current_question;
    }

    return $questions;
}

$csrf_token = generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>إضافة أسئلة متعددة - <?php echo htmlspecialchars($lecture['title']); ?></title>
    <link <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;500;600&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="../assets/css/mobile.css" rel="stylesheet">
    <style>
        body {
            background: #183046;
            min-height: 100vh;
            font-family: 'Cairo', sans-serif;
            overflow-x: hidden;
            overflow-y: auto;
        }
        .navbar {
            background: #fff !important;
            box-shadow: 0 2px 10px rgba(0,0,0,0.07);
            border-bottom: 1px solid #e3e6f0;
        }
        .navbar-brand {
            font-weight: 700;
            font-size: 1.2rem;
            color: #2c3e50 !important;
            line-height: 1.2;
            display: flex;
            align-items: center;
        }

        .brand-text {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .nav-link, .navbar-nav .nav-link {
            color: #2c3e50 !important;
            font-weight: 600;
            border-radius: 25px;
            margin: 0 0.3rem;
        }
        .nav-link.active, .nav-link:hover {
            background: #2196f3;
            color: #fff !important;
        }
        .bulk-header {
            margin-top: 2.5rem;
            margin-bottom: 2.5rem;
            text-align: left;
        }
        .bulk-title {
            font-size: 2.3rem;
            font-weight: 900;
            color: #fff;
            margin-bottom: 0.5rem;
        }
        .bulk-subtitle {
            color: #e0e0e0;
            font-size: 1.1rem;
            font-weight: 400;
        }
        .card.border-0.shadow-sm {
            border-radius: 18px;
            background: #fff;
            box-shadow: 0 8px 32px rgba(102,126,234,0.08);
            padding: 1.5rem 1.2rem;
        }
        .card-header.bg-primary {
            background: #2196f3 !important;
            color: #fff !important;
            border-radius: 16px 16px 0 0 !important;
            font-weight: 700;
            font-size: 1.2rem;
        }
        .btn-primary, .btn-outline-primary:hover {
            background: #2196f3 !important;
            border-color: #2196f3 !important;
            color: #fff !important;
        }
        .btn-outline-primary {
            color: #2196f3 !important;
            border-color: #2196f3 !important;
            background: #fff !important;
        }
        .btn-outline-primary:focus, .btn-outline-primary:active {
            background: #2196f3 !important;
            color: #fff !important;
        }
        .form-control, .form-select {
            border-radius: 10px;
        }
        .footer {
            background: #2c3e50;
            color: #fff;
            padding: 2.5rem 0 1rem;
            margin-top: 3rem;
        }
        .footer-title {
            font-size: 1.2rem;
            font-weight: 700;
        }
        .footer-bottom {
            border-top: 1px solid rgba(255,255,255,0.1);
            padding-top: 1rem;
            text-align: center;
            opacity: 0.7;
        }
        .format-example {
            background: #f8f9fa;
            border: 1px solid #e3e6f0;
            border-radius: 10px;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            line-height: 1.6;
        }
        .format-example .highlight {
            background: #fff3cd;
            padding: 0.1rem 0.3rem;
            border-radius: 3px;
        }
        @media (max-width: 991px) {
            .bulk-header {margin-top: 1.5rem;}
        }

        @media (max-width: 768px) {
            .bulk-header .d-flex {
                flex-direction: column;
                align-items: flex-start !important;
                gap: 1rem;
            }

            .bulk-header .d-flex > div:last-child {
                width: 100%;
                display: flex;
                flex-direction: column;
                gap: 0.5rem;
            }

            .bulk-header .btn {
                width: 100%;
                justify-content: center;
                padding: 0.75rem 1rem;
                font-size: 0.9rem;
            }

            .bulk-header .btn i {
                margin-left: 0.5rem;
            }

            .bulk-title {
                font-size: 1.5rem;
                margin-bottom: 0.5rem;
            }

            .bulk-subtitle {
                font-size: 0.9rem;
                margin-bottom: 1rem;
            }
        }

        @media (max-width: 576px) {
            .bulk-header .btn {
                font-size: 0.85rem;
                padding: 0.6rem 0.8rem;
            }

            .bulk-title {
                font-size: 1.3rem;
            }

            .bulk-subtitle {
                font-size: 0.8rem;
            }
        }

        @media (max-width: 768px) {
            .card {
                margin-bottom: 1rem;
                border-radius: 12px;
            }

            .card-header {
                padding: 1rem;
                font-size: 1rem;
            }

            .card-body {
                padding: 1rem;
            }

            .form-control,
            .form-select {
                font-size: 16px !important;
                padding: 0.75rem;
            }

            .form-label {
                font-weight: 600;
                margin-bottom: 0.5rem;
            }

            .btn {
                padding: 0.75rem 1rem;
                font-size: 0.9rem;
            }

            .alert {
                border-radius: 8px;
                padding: 1rem;
                font-size: 0.9rem;
            }

            .progress {
                height: 25px;
                border-radius: 12px;
            }

            .progress-bar {
                font-size: 0.85rem;
            }
        }

        @media (max-width: 576px) {
            .card-header {
                padding: 0.8rem;
                font-size: 0.95rem;
            }

            .card-body {
                padding: 0.8rem;
            }

            .btn {
                padding: 0.6rem 0.8rem;
                font-size: 0.85rem;
            }

            .alert {
                padding: 0.8rem;
                font-size: 0.85rem;
            }
        }

        @media (max-width: 768px) {
            textarea.form-control {
                min-height: 200px;
                font-size: 16px !important;
                line-height: 1.5;
            }

            .form-text {
                font-size: 0.8rem;
                margin-top: 0.5rem;
            }
        }

        @media (max-width: 576px) {
            textarea.form-control {
                min-height: 150px;
                font-size: 15px !important;
            }

            .form-text {
                font-size: 0.75rem;
            }
        }
    </style>
</head>
<body>
    <?php include '../includes/mobile_navbar.php'; ?>
    <div class="container" style="margin-top: 80px;">
        <div class="bulk-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="bulk-title">
                        <i class="fas fa-upload text-primary me-2"></i>
                        إضافة أسئلة متعددة
                    </div>
                    <div class="bulk-subtitle">
                        <?php echo htmlspecialchars($lecture['title']); ?> - <?php echo htmlspecialchars($lecture['subject_name']); ?>
                        <?php 

                        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM questions WHERE lecture_id = ?");
                        $stmt->execute([$lecture_id]);
                        $current_questions = $stmt->fetch()['count'];
                        ?>
                        <span class="badge bg-info ms-2"><?php echo $current_questions; ?> سؤال حالياً</span>
                    </div>
                </div>
                <div>
                    <a href="questions.php?lecture_id=<?php echo $lecture_id; ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للأسئلة
                    </a>
                </div>
            </div>
        </div>

        <!-- Messages -->
        <?php if ($message): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo $message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?php echo $error; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <div class="row">
            <div class="col-lg-8">
                <!-- Questions Input Form -->
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-edit me-2"></i>
                            إدخال الأسئلة
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" class="needs-validation" novalidate>
                            <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                            <input type="hidden" name="action" value="bulk_add">

                            <div class="mb-3">
                                <label for="questions_text" class="form-label">أدخل الأسئلة هنا *</label>
                                <textarea class="form-control" id="questions_text" name="questions_text" rows="20" required 
                                          placeholder="أدخل الأسئلة بالتنسيق المطلوب..." style="direction: ltr; text-align: left;"></textarea>
                                <div class="invalid-feedback">يرجى إدخال الأسئلة</div>
                            </div>

                            <div class="d-flex justify-content-between">
                                <button type="button" class="btn btn-outline-info" onclick="loadExample()">
                                    <i class="fas fa-lightbulb me-2"></i>
                                    تحميل مثال
                                </button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>
                                    حفظ الأسئلة
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <!-- Format Instructions -->
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            التنسيق المطلوب
                        </h5>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-3">يجب إدخال الأسئلة بالتنسيق التالي:</p>

                        <div class="format-example">
                            <span class="highlight">1.</span> What is the capital of Italy?<br>
                            <span class="highlight">A)</span> Paris<br>
                            <span class="highlight">B)</span> Madrid<br>
                            <span class="highlight">C)</span> Rome<br>
                            <span class="highlight">D)</span> Athens<br>
                            <span class="highlight">ans/C</span><br><br>

                            <span class="highlight">2.</span> How many planets are in the Solar System?<br>
                            <span class="highlight">A)</span> 7<br>
                            <span class="highlight">B)</span> 8<br>
                            <span class="highlight">C)</span> 9<br>
                            <span class="highlight">D)</span> 10<br>
                            <span class="highlight">ans/B</span>
                        </div>

                        <div class="mt-3">
                            <h6>ملاحظات مهمة:</h6>
                            <ul class="text-muted small">
                                <li>يجب أن يبدأ كل سؤال برقم متبوع بنقطة</li>
                                <li>الخيارات يجب أن تكون A, B, C, D متبوعة بقوس</li>
                                <li>الإجابة الصحيحة تكتب في سطر منفصل: ans/X</li>
                                <li>النقاط ستكون 1 افتراضياً</li>
                                <li>مستوى الصعوبة سيكون متوسط افتراضياً</li>
                                <li>الحالة ستكون نشطة افتراضياً</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <footer class="footer">
        <div class="container">
            <div class="footer-title">جامعة المنيا - قسم الـ Forensic</div>
            <div class="footer-bottom">
                &copy; 2024 جميع الحقوق محفوظة - جامعة المنيا - قسم الـ Forensic
            </div>
        </div>
    </footer>
    <script src="https:
    <script src="../assets/js/main.js"></script>
    <script>

        function loadExample() {
            const example = `1. ما هي عاصمة إيطاليا؟
A) باريس
B) مدريد
C) روما
D) أثينا
ans/C

2. كم عدد الكواكب في المجموعة الشمسية؟
A) 7
B) 8
C) 9
D) 10
ans/B

3. ما هو الحيوان المعروف بـ "سفينة الصحراء"؟
A) الجمل
B) الحصان
C) الثعلب
D) النمر
ans/A

4. من اخترع المصباح الكهربائي؟
A) ألكسندر جراهام بيل
B) توماس إديسون
C) إسحاق نيوتن
D) نيكولا تسلا
ans/B

5. ما هو أقرب كوكب للشمس؟
A) الزهرة
B) الأرض
C) عطارد
D) المريخ
ans/C`;

            document.getElementById('questions_text').value = example;
        }
    </script>
    <script src="../assets/js/mobile.js"></script>
</body>
</html>