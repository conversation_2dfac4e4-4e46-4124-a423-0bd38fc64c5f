<?php
// إعدادات الجلسة الآمنة
ini_set('session.cookie_httponly', 1);
ini_set('session.cookie_secure', isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 1 : 0);
ini_set('session.use_strict_mode', 1);

session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// تطبيق headers الأمنية
setSecurityHeaders();

// فحص الصلاحيات والأمان
preventDirectAccess(['admin', 'assistant']);

$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    if (!verifyCSRFToken($_POST['csrf_token'])) {
        $error = 'خطأ في التحقق من الأمان';
    } else {
        if ($_POST['action'] === 'add') {
            $name = sanitize($_POST['name']);

            if (empty($name)) {
                $error = 'يرجى إدخال اسم المادة';
            } else {
                try {
                    $stmt = $pdo->prepare("INSERT INTO subjects (name, created_by) VALUES (?, ?)");
                    if ($stmt->execute([$name, $_SESSION['user_id']])) {
                        $message = 'تم إضافة المادة بنجاح';
                        logActivity($_SESSION['user_id'], 'add_subject', "إضافة مادة جديدة: $name");
                    } else {
                        $error = 'خطأ في إضافة المادة';
                    }
                } catch (PDOException $e) {
                    $error = 'خطأ في قاعدة البيانات';
                }
            }
        } elseif ($_POST['action'] === 'edit') {
            $id = (int)$_POST['id'];
            $name = sanitize($_POST['name']);
            $status = $_POST['status'];

            if (empty($name)) {
                $error = 'يرجى إدخال اسم المادة';
            } else {
                try {
                    $stmt = $pdo->prepare("UPDATE subjects SET name = ?, status = ? WHERE id = ?");
                    if ($stmt->execute([$name, $status, $id])) {
                        $message = 'تم تحديث المادة بنجاح';
                        logActivity($_SESSION['user_id'], 'edit_subject', "تحديث مادة: $name");
                    } else {
                        $error = 'خطأ في تحديث المادة';
                    }
                } catch (PDOException $e) {
                    $error = 'خطأ في قاعدة البيانات';
                }
            }
        } elseif ($_POST['action'] === 'delete') {
            $id = (int)$_POST['id'];

            try {

                $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM exams WHERE subject_id = ?");
                $stmt->execute([$id]);
                $examCount = $stmt->fetch()['count'];

                if ($examCount > 0) {
                    $error = 'لا يمكن حذف المادة لوجود امتحانات مرتبطة بها';
                } else {
                    $stmt = $pdo->prepare("DELETE FROM subjects WHERE id = ?");
                    if ($stmt->execute([$id])) {
                        $message = 'تم حذف المادة بنجاح';
                        logActivity($_SESSION['user_id'], 'delete_subject', "حذف مادة برقم: $id");
                    } else {
                        $error = 'خطأ في حذف المادة';
                    }
                }
            } catch (PDOException $e) {
                $error = 'خطأ في قاعدة البيانات';
            }
        }
    }
}

try {
    $stmt = $pdo->query("SELECT s.*, u.full_name as created_by_name,
                        (SELECT COUNT(*) FROM lectures WHERE subject_id = s.id) as lectures_count,
                        (SELECT COUNT(*) FROM exams WHERE subject_id = s.id) as exams_count,
                        (SELECT COUNT(*) FROM questions q JOIN lectures l ON q.lecture_id = l.id WHERE l.subject_id = s.id) as questions_count
                        FROM subjects s
                        LEFT JOIN users u ON s.created_by = u.id
                        ORDER BY s.created_at DESC");
    $subjects = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = 'خطأ في تحميل البيانات';
}

$csrf_token = generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>إدارة المواد - نظام الامتحانات الإلكترونية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;500;600&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            background: #183046;
            min-height: 100vh;
            font-family: 'Cairo', sans-serif;
            overflow-x: hidden;
            overflow-y: auto;
        }
        .navbar {
            background: #fff !important;
            box-shadow: 0 2px 10px rgba(0,0,0,0.07);
            border-bottom: 1px solid #e3e6f0;
        }
        .navbar-brand {
            font-weight: 700;
            font-size: 1.2rem;
            color: #2c3e50 !important;
            line-height: 1.2;
            display: flex;
            align-items: center;
        }

        .brand-text {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .nav-link, .navbar-nav .nav-link {
            color: #2c3e50 !important;
            font-weight: 600;
            border-radius: 25px;
            margin: 0 0.3rem;
        }
        .nav-link.active, .nav-link:hover {
            background: #2196f3;
            color: #fff !important;
        }
        .subjects-header {
            margin-top: 2.5rem;
            margin-bottom: 2.5rem;
            text-align: right;
        }
        .subjects-title {
            font-size: 2.3rem;
            font-weight: 900;
            color: #fff;
            margin-bottom: 0.5rem;
        }
        .subjects-subtitle {
            color: #e0e0e0;
            font-size: 1.1rem;
            font-weight: 400;
        }
        .modern-card {
            background: #fff;
            border-radius: 16px;
            box-shadow: 0 4px 24px rgba(102,126,234,0.08);
            padding: 1.5rem 1.2rem;
            margin-bottom: 2rem;
        }
        .modern-card .card-header {
            background: none;
            border: none;
            padding: 0 0 1rem 0;
            color: #2196f3;
            font-weight: 700;
            font-size: 1.2rem;
        }
        .modern-card .list-group-item {
            border: none;
            border-bottom: 1px solid #f0f0f0;
            padding: 0.7rem 0.2rem;
        }
        .modern-card .list-group-item:last-child {
            border-bottom: none;
        }
        .card.border-0.shadow-sm {
            border-radius: 18px;
            background: #fff;
            box-shadow: 0 8px 32px rgba(102,126,234,0.08);
            padding: 1.5rem 1.2rem;
        }
        .card-header.bg-primary {
            background: #2196f3 !important;
            color: #fff !important;
            border-radius: 16px 16px 0 0 !important;
            font-weight: 700;
            font-size: 1.2rem;
        }
        .btn-primary, .btn-outline-primary:hover {
            background: #2196f3 !important;
            border-color: #2196f3 !important;
            color: #fff !important;
        }
        .btn-outline-primary {
            color: #2196f3 !important;
            border-color: #2196f3 !important;
            background: #fff !important;
        }
        .btn-outline-primary:focus, .btn-outline-primary:active {
            background: #2196f3 !important;
            color: #fff !important;
        }
        .table {
            background: #fff;
            border-radius: 12px;
            overflow: hidden;
        }
        .table th, .table td {
            vertical-align: middle;
        }
        .modal-content {
            border-radius: 16px;
        }
        .modal-header {
            border-bottom: 1px solid #f0f0f0;
        }
        .modal-title {
            color: #2196f3;
            font-weight: 700;
        }
        .form-label {
            font-weight: 600;
            color: #2c3e50;
        }
        .form-control, .form-select {
            border-radius: 10px;
        }
        .btn-close {
            margin: 0;
        }
        .footer {
            background: #2c3e50;
            color: #fff;
            padding: 2.5rem 0 1rem;
            margin-top: 3rem;
        }
        .footer-title {
            font-size: 1.2rem;
            font-weight: 700;
        }
        .footer-bottom {
            border-top: 1px solid rgba(255,255,255,0.1);
            padding-top: 1rem;
            text-align: center;
            opacity: 0.7;
        }

        .mobile-nav-buttons {
            display: flex !important;
            align-items: center;
            gap: 0.5rem;
            z-index: 1000;
        }

        .mobile-nav-buttons .btn {
            width: 40px !important;
            height: 40px !important;
            border-radius: 50% !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            padding: 0 !important;
            border-width: 2px !important;
            transition: all 0.3s ease;
            color: #fff !important;
            border-color: rgba(255, 255, 255, 0.8) !important;
            margin: 0 !important;
        }

        .mobile-nav-buttons .btn i {
            font-size: 16px;
        }

        .mobile-nav-buttons .btn-outline-light {
            background-color: rgba(0, 123, 255, 0.2) !important;
            border-color: #007bff !important;
            color: #007bff !important;
        }

        .mobile-nav-buttons .btn-outline-light:hover {
            background-color: rgba(0, 123, 255, 0.3) !important;
            border-color: #0056b3 !important;
            color: #0056b3 !important;
        }

        .mobile-nav-buttons .btn-outline-danger {
            background-color: rgba(220, 53, 69, 0.2) !important;
            border-color: #dc3545 !important;
            color: #dc3545 !important;
        }

        .mobile-nav-buttons .btn-outline-danger:hover {
            background-color: rgba(220, 53, 69, 0.3) !important;
            border-color: #c82333 !important;
            color: #c82333 !important;
        }

        @media (max-width: 991px) {
            .subjects-header {margin-top: 1.5rem;}
            .navbar-brand {
                font-size: 1rem;
                max-width: none;
            }
        }

        @media (max-width: 768px) {
            body {
                -webkit-text-size-adjust: 100%;
                -ms-text-size-adjust: 100%;
                touch-action: pan-y;
                overflow-y: auto;
                -webkit-overflow-scrolling: touch;
            }

            .brand-text {
                max-width: calc(100vw - 140px);
            }

            .container {
                padding-left: 15px;
                padding-right: 15px;
            }

            .navbar .container {
                display: flex;
                align-items: center;
                justify-content: space-between;
                position: relative;
                gap: 0.5rem;
            }

            .navbar-brand {
                order: 1;
                flex: 1;
                margin-right: 0.5rem;
                font-size: 0.9rem;
                max-width: none;
                min-width: 0;
            }

            .mobile-nav-buttons {
                order: 2;
                flex-shrink: 0;
                display: flex !important;
                visibility: visible !important;
                opacity: 1 !important;
            }

            .navbar-toggler {
                order: 3;
                flex-shrink: 0;
                margin-left: 0.5rem;
                border: none;
                padding: 0.25rem 0.5rem;
                font-size: 1.1rem;
            }

            .navbar-toggler:focus {
                box-shadow: none;
            }

            .navbar-nav {
                text-align: center;
                padding-top: 1rem;
            }

            .nav-link {
                padding: 0.8rem 1rem !important;
                border-bottom: 1px solid rgba(255,255,255,0.1);
            }

            .dropdown-menu {
                border: none;
                box-shadow: 0 4px 15px rgba(0,0,0,0.1);
                border-radius: 8px;
                min-width: 200px;
                padding: 0.5rem 0;
                margin-top: 0.5rem;
            }

            .dropdown-item {
                padding: 0.8rem 1.2rem;
                font-size: 0.95rem;
                color: #333;
                transition: all 0.2s ease;
            }

            .dropdown-item:hover {
                background-color: #f8f9fa;
                color: #007bff;
            }

            .dropdown-item i {
                margin-left: 0.5rem;
                width: 16px;
                text-align: center;
            }
        }

        @media (max-width: 576px) {
            .container {
                padding-left: 8px;
                padding-right: 8px;
            }

            .navbar .container {
                gap: 0.3rem;
            }

            .navbar-brand {
                font-size: 0.8rem;
                max-width: none;
                flex: 1;
                margin-right: 0.3rem;
            }

            .brand-text {
                max-width: calc(100vw - 120px);
            }

            .mobile-nav-buttons {
                gap: 0.2rem;
                display: flex !important;
                visibility: visible !important;
            }

            .mobile-nav-buttons .btn {
                width: 32px;
                height: 32px;
                display: flex !important;
                visibility: visible !important;
            }

            .mobile-nav-buttons .btn i {
                font-size: 12px;
            }

            .navbar-toggler {
                padding: 0.2rem 0.4rem;
                font-size: 1rem;
            }

            .dropdown-menu {
                position: fixed !important;
                top: 60px !important;
                left: 10px !important;
                right: 10px !important;
                width: auto !important;
                margin: 0 !important;
                border-radius: 12px;
                box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            }

            .dropdown-item {
                padding: 1rem 1.5rem;
                font-size: 1rem;
                text-align: center;
            }
        }

        @media screen and (max-width: 991px) {
            .d-lg-none {
                display: flex !important;
            }

            .mobile-nav-buttons,
            .mobile-nav-buttons .btn {
                display: flex !important;
                visibility: visible !important;
                opacity: 1 !important;
            }
        }

        input, select, textarea {
            font-size: 16px !important;
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
        }

        .nav-link, .btn, .card {
            -webkit-tap-highlight-color: transparent;
            touch-action: manipulation;
        }

        html, body {
            height: 100%;
            -webkit-overflow-scrolling: touch;
        }

        .container-fluid, .container {
            touch-action: pan-y;
        }

        .row {
            margin-left: 0;
            margin-right: 0;
        }

        .col-md-3, .col-md-4, .col-md-6, .col-6, .col-12 {
            padding-left: 0.5rem;
            padding-right: 0.5rem;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
        <div class="container">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-graduation-cap me-2"></i>
                <span class="brand-text">جامعة المنيا - قسم الـ Forensic</span>
            </a>

            <!-- أزرار منفصلة للموبايل -->
            <div class="mobile-nav-buttons d-lg-none">
                <a href="profile.php" class="btn btn-outline-light btn-sm me-2">
                    <i class="fas fa-user"></i>
                </a>
                <a href="../logout.php" class="btn btn-outline-danger btn-sm me-2">
                    <i class="fas fa-sign-out-alt"></i>
                </a>
            </div>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">لوحة التحكم</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="subjects.php">المواد</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="exams.php">الامتحانات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="results.php">النتائج</a>
                    </li>
                </ul>

                <!-- قائمة المستخدم للديسكتوب فقط -->
                <ul class="navbar-nav d-none d-lg-flex">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <?php echo htmlspecialchars($_SESSION['full_name'] ?? $_SESSION['username'] ?? 'المستخدم'); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="profile.php">
                                <i class="fas fa-user me-2"></i>الملف الشخصي
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php">
                                <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    <div class="container" style="margin-top: 80px;">
        <div class="subjects-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="subjects-title">
                        <i class="fas fa-book text-primary me-2"></i>
                        إدارة المواد
                    </div>
                    <div class="subjects-subtitle">إدارة مواد قسم الـ Forensic - جامعة المنيا</div>
                </div>
                <div>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addSubjectModal">
                        <i class="fas fa-plus me-2"></i>
                        إضافة مادة جديدة
                    </button>
                </div>
            </div>
        </div>

        <!-- Messages -->
        <?php if ($message): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo $message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?php echo $error; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Subjects Table -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    قائمة المواد
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($subjects)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-book text-muted" style="font-size: 3rem;"></i>
                        <p class="text-muted mt-3">لا توجد مواد مضافة بعد</p>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addSubjectModal">
                            <i class="fas fa-plus me-2"></i>
                            إضافة أول مادة
                        </button>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover" id="subjectsTable">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>اسم المادة</th>
                                    <th>عدد المحاضرات</th>
                                    <th>عدد الأسئلة</th>
                                    <th>عدد الامتحانات</th>
                                    <th>الحالة</th>
                                    <th>أنشئ بواسطة</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($subjects as $index => $subject): ?>
                                    <tr>
                                        <td><?php echo $index + 1; ?></td>
                                        <td>
                                            <strong><?php echo htmlspecialchars($subject['name']); ?></strong>
                                        </td>
                                        <td>
                                            <span class="badge bg-success"><?php echo $subject['lectures_count']; ?></span>
                                        </td>
                                        <td>
                                            <span class="badge bg-info"><?php echo $subject['questions_count']; ?></span>
                                        </td>
                                        <td>
                                            <span class="badge bg-warning"><?php echo $subject['exams_count']; ?></span>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?php echo $subject['status'] === 'active' ? 'success' : 'danger'; ?>">
                                                <?php echo $subject['status'] === 'active' ? 'نشط' : 'غير نشط'; ?>
                                            </span>
                                        </td>
                                        <td><?php echo htmlspecialchars($subject['created_by_name']); ?></td>
                                        <td><?php echo date('Y-m-d', strtotime($subject['created_at'])); ?></td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button class="btn btn-sm btn-outline-primary"
                                                        onclick="editSubject(<?php echo htmlspecialchars(json_encode($subject)); ?>)"
                                                        title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <a href="lectures.php?subject_id=<?php echo $subject['id']; ?>"
                                                   class="btn btn-sm btn-outline-info" title="المحاضرات">
                                                    <i class="fas fa-chalkboard-teacher"></i>
                                                </a>
                                                <?php if ($subject['exams_count'] == 0): ?>
                                                    <button class="btn btn-sm btn-outline-danger"
                                                            onclick="deleteSubject(<?php echo $subject['id']; ?>, '<?php echo htmlspecialchars($subject['name']); ?>')"
                                                            title="حذف">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Add Subject Modal -->
    <div class="modal fade" id="addSubjectModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-plus me-2"></i>
                        إضافة مادة جديدة
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" class="needs-validation" novalidate>
                    <div class="modal-body">
                        <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                        <input type="hidden" name="action" value="add">

                        <div class="mb-3">
                            <label for="name" class="form-label">اسم المادة *</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                            <div class="invalid-feedback">يرجى إدخال اسم المادة</div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            حفظ
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Subject Modal -->
    <div class="modal fade" id="editSubjectModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-edit me-2"></i>
                        تعديل المادة
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" class="needs-validation" novalidate>
                    <div class="modal-body">
                        <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                        <input type="hidden" name="action" value="edit">
                        <input type="hidden" name="id" id="edit_id">

                        <div class="mb-3">
                            <label for="edit_name" class="form-label">اسم المادة *</label>
                            <input type="text" class="form-control" id="edit_name" name="name" required>
                            <div class="invalid-feedback">يرجى إدخال اسم المادة</div>
                        </div>

                        <div class="mb-3">
                            <label for="edit_status" class="form-label">الحالة</label>
                            <select class="form-select" id="edit_status" name="status">
                                <option value="active">نشط</option>
                                <option value="inactive">غير نشط</option>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            حفظ التغييرات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteSubjectModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title text-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        تأكيد الحذف
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>هل أنت متأكد من حذف المادة: <strong id="deleteSubjectName"></strong>؟</p>
                    <p class="text-danger"><small>هذا الإجراء لا يمكن التراجع عنه.</small></p>
                </div>
                <form method="POST">
                    <div class="modal-footer">
                        <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                        <input type="hidden" name="action" value="delete">
                        <input type="hidden" name="id" id="deleteSubjectId">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash me-2"></i>
                            حذف
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    </div>
    <footer class="footer">
        <div class="container">
            <div class="footer-title">جامعة المنيا - قسم الـ Forensic</div>
            <div class="footer-bottom">
                &copy; 2024 جميع الحقوق محفوظة - جامعة المنيا - قسم الـ Forensic
            </div>
        </div>
    </footer>
    <script src="https:
    <script src="../assets/js/main.js"></script>
    <script>

        function editSubject(subject) {
            document.getElementById('edit_id').value = subject.id;
            document.getElementById('edit_name').value = subject.name;
            document.getElementById('edit_status').value = subject.status;

            new bootstrap.Modal(document.getElementById('editSubjectModal')).show();
        }

        function deleteSubject(id, name) {
            document.getElementById('deleteSubjectId').value = id;
            document.getElementById('deleteSubjectName').textContent = name;

            new bootstrap.Modal(document.getElementById('deleteSubjectModal')).show();
        }

        function searchSubjects() {
            const input = document.getElementById('searchInput');
            const filter = input.value.toUpperCase();
            const table = document.getElementById('subjectsTable');
            const rows = table.getElementsByTagName('tr');

            for (let i = 1; i < rows.length; i++) {
                const cells = rows[i].getElementsByTagName('td');
                let found = false;

                for (let j = 0; j < cells.length; j++) {
                    if (cells[j].textContent.toUpperCase().indexOf(filter) > -1) {
                        found = true;
                        break;
                    }
                }

                rows[i].style.display = found ? '' : 'none';
            }
        }

        document.addEventListener('DOMContentLoaded', function() {

            document.addEventListener('gesturestart', function (e) {
                e.preventDefault();
            });

            document.addEventListener('gesturechange', function (e) {
                e.preventDefault();
            });

            document.addEventListener('gestureend', function (e) {
                e.preventDefault();
            });

            let lastTouchEnd = 0;
            document.addEventListener('touchend', function (event) {
                const now = (new Date()).getTime();
                if (now - lastTouchEnd <= 300) {
                    event.preventDefault();
                }
                lastTouchEnd = now;
            }, false);

            document.addEventListener('touchmove', function (event) {
                if (event.touches.length > 1) {
                    event.preventDefault();
                }
            }, { passive: false });

            function setVH() {
                let vh = window.innerHeight * 0.01;
                document.documentElement.style.setProperty('--vh', `${vh}px`);
            }

            setVH();
            window.addEventListener('resize', setVH);
            window.addEventListener('orientationchange', function() {
                setTimeout(setVH, 100);
            });

            const navLinks = document.querySelectorAll('.navbar-nav .nav-link');
            const navbarCollapse = document.querySelector('.navbar-collapse');

            navLinks.forEach(link => {
                link.addEventListener('click', () => {
                    if (window.innerWidth < 992) {
                        const bsCollapse = new bootstrap.Collapse(navbarCollapse, {
                            toggle: false
                        });
                        bsCollapse.hide();
                    }
                });
            });

            const tables = document.querySelectorAll('.table');
            tables.forEach(table => {
                if (!table.parentElement.classList.contains('table-responsive')) {
                    const wrapper = document.createElement('div');
                    wrapper.className = 'table-responsive';
                    table.parentNode.insertBefore(wrapper, table);
                    wrapper.appendChild(table);
                }
            });

            const mobileButtons = document.querySelectorAll('.mobile-nav-buttons .btn');

            mobileButtons.forEach(button => {

                button.style.display = 'flex';
                button.style.visibility = 'visible';
                button.style.opacity = '1';

                button.addEventListener('touchstart', function() {
                    this.style.transform = 'scale(0.95)';
                });

                button.addEventListener('touchend', function() {
                    this.style.transform = 'scale(1)';
                });

                button.addEventListener('touchcancel', function() {
                    this.style.transform = 'scale(1)';
                });
            });

            const mobileButtonsContainer = document.querySelector('.mobile-nav-buttons');
            if (mobileButtonsContainer) {
                mobileButtonsContainer.style.display = 'flex';
                mobileButtonsContainer.style.visibility = 'visible';
                mobileButtonsContainer.style.opacity = '1';
            }

            const logoutButton = document.querySelector('.mobile-nav-buttons .btn-outline-danger');
            if (logoutButton) {
                logoutButton.addEventListener('click', function(e) {
                    if (!confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                        e.preventDefault();
                    }
                });
            }

            if (/iPad|iPhone|iPod/.test(navigator.userAgent)) {
                document.body.style.webkitOverflowScrolling = 'touch';
                document.body.style.overflowY = 'auto';
            }
        });
    </script>

    <!-- Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>