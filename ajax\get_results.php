<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit();
}

$student_id = isset($_GET['student_id']) ? sanitize($_GET['student_id']) : '';

if (empty($student_id)) {
    echo json_encode(['success' => false, 'message' => 'رقم الطالب مطلوب']);
    exit();
}

try {

    $stmt = $pdo->prepare("
        SELECT er.*, e.title as exam_title, e.exam_type, s.name as subject_name 
        FROM exam_results er 
        LEFT JOIN exams e ON er.exam_id = e.id 
        LEFT JOIN subjects s ON e.subject_id = s.id 
        WHERE er.student_id = ? 
        ORDER BY er.submitted_at DESC
    ");
    $stmt->execute([$student_id]);
    $results = $stmt->fetchAll();

    if (empty($results)) {
        echo json_encode([
            'success' => false,
            'message' => 'لا توجد نتائج لهذا الطالب'
        ]);
        exit();
    }

    $total_exams = count($results);
    $passed_exams = 0;
    $total_percentage = 0;
    $total_attempts = 0;

    foreach ($results as $result) {
        $total_attempts++;
    }

    $average_percentage = $total_attempts > 0 ? round($total_percentage / $total_attempts, 2) : 0;
    $success_rate = $total_exams > 0 ? round(($passed_exams / $total_exams) * 100, 2) : 0;

    echo json_encode([
        'success' => true,
        'student_name' => $results[0]['student_name'],
        'student_id' => $student_id,
        'stats' => [
            'total_exams' => $total_exams,
            'passed_exams' => $passed_exams,
            'average_percentage' => $average_percentage,
            'success_rate' => $success_rate
        ],
        'results' => $results
    ]);

} catch (PDOException $e) {
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في قاعدة البيانات'
    ]);
}
?>