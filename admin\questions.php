<?php
// إعدادات الجلسة الآمنة
ini_set('session.cookie_httponly', 1);
ini_set('session.cookie_secure', isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 1 : 0);
ini_set('session.use_strict_mode', 1);

session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// تطبيق headers الأمنية
setSecurityHeaders();

// فحص الصلاحيات والأمان
preventDirectAccess(['admin', 'assistant']);

$message = '';
$error = '';

$lecture_id = isset($_GET['lecture_id']) ? (int)$_GET['lecture_id'] : 0;
if (!$lecture_id) {
    header('Location: subjects.php');
    exit();
}

try {
    $stmt = $pdo->prepare("SELECT l.*, s.name as subject_name FROM lectures l 
                          JOIN subjects s ON l.subject_id = s.id 
                          WHERE l.id = ?");
    $stmt->execute([$lecture_id]);
    $lecture = $stmt->fetch();

    if (!$lecture) {
        header('Location: subjects.php');
        exit();
    }
} catch (PDOException $e) {
    $error = 'خطأ في تحميل بيانات المحاضرة';
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    if (!verifyCSRFToken($_POST['csrf_token'])) {
        $error = 'خطأ في التحقق من الأمان';
    } else {
        if ($_POST['action'] === 'add') {
            $question_text = sanitize($_POST['question_text']);
            $option_a = sanitize($_POST['option_a']);
            $option_b = sanitize($_POST['option_b']);
            $option_c = sanitize($_POST['option_c']);
            $option_d = sanitize($_POST['option_d']);
            $correct_answer = $_POST['correct_answer'];
            $points = (int)$_POST['points'];
            $difficulty = $_POST['difficulty'];

            if (empty($question_text) || empty($option_a) || empty($option_b) || empty($option_c) || empty($option_d)) {
                $error = 'يرجى ملء جميع الحقول المطلوبة';
            } else {
                try {
                    $stmt = $pdo->prepare("INSERT INTO questions (lecture_id, question_text, option_a, option_b, option_c, option_d, correct_answer, points, difficulty, created_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                    if ($stmt->execute([$lecture_id, $question_text, $option_a, $option_b, $option_c, $option_d, $correct_answer, $points, $difficulty, $_SESSION['user_id']])) {
                        $message = 'تم إضافة السؤال بنجاح';
                        logActivity($_SESSION['user_id'], 'add_question', "إضافة سؤال جديد للمحاضرة: {$lecture['title']}");
                    } else {
                        $error = 'خطأ في إضافة السؤال';
                    }
                } catch (PDOException $e) {
                    $error = 'خطأ في قاعدة البيانات';
                }
            }
        } elseif ($_POST['action'] === 'edit') {
            $id = (int)$_POST['id'];
            $question_text = sanitize($_POST['question_text']);
            $option_a = sanitize($_POST['option_a']);
            $option_b = sanitize($_POST['option_b']);
            $option_c = sanitize($_POST['option_c']);
            $option_d = sanitize($_POST['option_d']);
            $correct_answer = $_POST['correct_answer'];
            $points = (int)$_POST['points'];
            $difficulty = $_POST['difficulty'];
            $status = $_POST['status'];

            if (empty($question_text) || empty($option_a) || empty($option_b) || empty($option_c) || empty($option_d)) {
                $error = 'يرجى ملء جميع الحقول المطلوبة';
            } else {
                try {
                    $stmt = $pdo->prepare("UPDATE questions SET question_text = ?, option_a = ?, option_b = ?, option_c = ?, option_d = ?, correct_answer = ?, points = ?, difficulty = ?, status = ? WHERE id = ? AND lecture_id = ?");
                    if ($stmt->execute([$question_text, $option_a, $option_b, $option_c, $option_d, $correct_answer, $points, $difficulty, $status, $id, $lecture_id])) {
                        $message = 'تم تحديث السؤال بنجاح';
                        logActivity($_SESSION['user_id'], 'edit_question', "تحديث سؤال: $id");
                    } else {
                        $error = 'خطأ في تحديث السؤال';
                    }
                } catch (PDOException $e) {
                    $error = 'خطأ في قاعدة البيانات';
                }
            }
        } elseif ($_POST['action'] === 'delete') {
            $id = (int)$_POST['id'];

            try {
                $stmt = $pdo->prepare("DELETE FROM questions WHERE id = ? AND lecture_id = ?");
                if ($stmt->execute([$id, $lecture_id])) {
                    $message = 'تم حذف السؤال بنجاح';
                    logActivity($_SESSION['user_id'], 'delete_question', "حذف سؤال: $id");
                } else {
                    $error = 'خطأ في حذف السؤال';
                }
            } catch (PDOException $e) {
                $error = 'خطأ في قاعدة البيانات';
            }
        }
    }
}

try {
    $stmt = $pdo->prepare("SELECT q.*, u.full_name as created_by_name
                          FROM questions q 
                          LEFT JOIN users u ON q.created_by = u.id 
                          WHERE q.lecture_id = ?
                          ORDER BY q.id ASC");
    $stmt->execute([$lecture_id]);
    $questions = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = 'خطأ في تحميل البيانات';
}

$csrf_token = generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>إدارة الأسئلة - <?php echo htmlspecialchars($lecture['title']); ?></title>
    <link <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;500;600&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="../assets/css/mobile.css" rel="stylesheet">
    <style>
        body {
            background: #183046;
            min-height: 100vh;
            font-family: 'Cairo', sans-serif;
            overflow-x: hidden;
            overflow-y: auto;
        }
        .navbar {
            background: #fff !important;
            box-shadow: 0 2px 10px rgba(0,0,0,0.07);
            border-bottom: 1px solid #e3e6f0;
        }
        .navbar-brand {
            font-weight: 700;
            font-size: 1.2rem;
            color: #2c3e50 !important;
            line-height: 1.2;
            display: flex;
            align-items: center;
        }

        .brand-text {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .nav-link, .navbar-nav .nav-link {
            color: #2c3e50 !important;
            font-weight: 600;
            border-radius: 25px;
            margin: 0 0.3rem;
        }
        .nav-link.active, .nav-link:hover {
            background: #2196f3;
            color: #fff !important;
        }
        .questions-header {
            margin-top: 2.5rem;
            margin-bottom: 2.5rem;
            text-align: right;
        }
        .questions-title {
            font-size: 2.3rem;
            font-weight: 900;
            color: #fff;
            margin-bottom: 0.5rem;
        }
        .questions-subtitle {
            color: #e0e0e0;
            font-size: 1.1rem;
            font-weight: 400;
        }
        .modern-card {
            background: #fff;
            border-radius: 16px;
            box-shadow: 0 4px 24px rgba(102,126,234,0.08);
            padding: 1.5rem 1.2rem;
            margin-bottom: 2rem;
        }
        .card.border-0.shadow-sm {
            border-radius: 18px;
            background: #fff;
            box-shadow: 0 8px 32px rgba(102,126,234,0.08);
            padding: 1.5rem 1.2rem;
        }
        .card-header.bg-primary {
            background: #2196f3 !important;
            color: #fff !important;
            border-radius: 16px 16px 0 0 !important;
            font-weight: 700;
            font-size: 1.2rem;
        }
        .btn-primary, .btn-outline-primary:hover {
            background: #2196f3 !important;
            border-color: #2196f3 !important;
            color: #fff !important;
        }
        .btn-outline-primary {
            color: #2196f3 !important;
            border-color: #2196f3 !important;
            background: #fff !important;
        }
        .btn-outline-primary:focus, .btn-outline-primary:active {
            background: #2196f3 !important;
            color: #fff !important;
        }
        .table {
            background: #fff;
            border-radius: 12px;
            overflow: hidden;
        }
        .table th, .table td {
            vertical-align: middle;
        }
        .modal-content {
            border-radius: 16px;
        }
        .modal-header {
            border-bottom: 1px solid #f0f0f0;
        }
        .modal-title {
            color: #2196f3;
            font-weight: 700;
        }
        .form-label {
            font-weight: 600;
            color: #2c3e50;
        }
        .form-control, .form-select {
            border-radius: 10px;
        }
        .btn-close {
            margin: 0;
        }
        .footer {
            background: #2c3e50;
            color: #fff;
            padding: 2.5rem 0 1rem;
            margin-top: 3rem;
        }
        .footer-title {
            font-size: 1.2rem;
            font-weight: 700;
        }
        .footer-bottom {
            border-top: 1px solid rgba(255,255,255,0.1);
            padding-top: 1rem;
            text-align: center;
            opacity: 0.7;
        }
        .question-card {
            border: 1px solid #e3e6f0;
            border-radius: 12px;
            margin-bottom: 1.5rem;
            background: #fff;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }
        .question-header {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 12px 12px 0 0;
            border-bottom: 1px solid #e3e6f0;
        }
        .question-body {
            padding: 1.5rem;
        }
        .question-text {
            font-size: 1.1rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 1rem;
            line-height: 1.6;
        }
        .options-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .option-item {
            padding: 0.8rem 1rem;
            margin-bottom: 0.5rem;
            border: 1px solid #e3e6f0;
            border-radius: 8px;
            background: #f8f9fa;
            position: relative;
        }
        .option-item.correct {
            background: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        .option-label {
            font-weight: 600;
            margin-right: 0.5rem;
            color: #2196f3;
        }
        .correct-answer {
            background: #d4edda;
            color: #155724;
            padding: 0.2rem 0.5rem;
            border-radius: 4px;
            font-weight: 600;
        }
        .question-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid #e3e6f0;
        }
        .question-actions {
            display: flex;
            gap: 0.5rem;
        }
        @media (max-width: 991px) {
            .questions-header {margin-top: 1.5rem;}
        }

        @media (max-width: 768px) {
            .questions-header .d-flex {
                flex-direction: column;
                align-items: flex-start !important;
                gap: 1rem;
            }

            .questions-header .d-flex > div:last-child {
                width: 100%;
                display: flex;
                flex-direction: column;
                gap: 0.5rem;
            }

            .questions-header .btn {
                width: 100%;
                justify-content: center;
                padding: 0.75rem 1rem;
                font-size: 0.9rem;
            }

            .questions-header .btn i {
                margin-left: 0.5rem;
            }

            .questions-title {
                font-size: 1.5rem;
                margin-bottom: 0.5rem;
            }

            .questions-subtitle {
                font-size: 0.9rem;
                margin-bottom: 1rem;
            }
        }

        @media (max-width: 576px) {
            .questions-header .btn {
                font-size: 0.85rem;
                padding: 0.6rem 0.8rem;
            }

            .questions-title {
                font-size: 1.3rem;
            }

            .questions-subtitle {
                font-size: 0.8rem;
            }
        }

        @media (max-width: 768px) {
            .table-responsive {
                border: none;
                margin-bottom: 1rem;
            }

            .table {
                font-size: 0.85rem;
            }

            .table th,
            .table td {
                padding: 0.5rem 0.3rem;
                white-space: nowrap;
            }

            .table .btn {
                padding: 0.25rem 0.5rem;
                font-size: 0.75rem;
                margin: 0.1rem;
            }

            .table .btn i {
                font-size: 0.7rem;
            }

            .table .btn-group {
                display: flex;
                flex-direction: column;
                gap: 0.2rem;
            }

            .table .btn-group .btn {
                width: 100%;
                margin: 0;
            }
        }

        @media (max-width: 576px) {
            .table {
                font-size: 0.8rem;
            }

            .table th,
            .table td {
                padding: 0.4rem 0.2rem;
            }

            .table .btn {
                padding: 0.2rem 0.4rem;
                font-size: 0.7rem;
            }
        }

        @media (max-width: 768px) {
            .card {
                margin-bottom: 1rem;
                border-radius: 12px;
            }

            .card-header {
                padding: 1rem;
                font-size: 1rem;
            }

            .card-body {
                padding: 1rem;
            }
        }

        @media (max-width: 576px) {
            .card-header {
                padding: 0.8rem;
                font-size: 0.95rem;
            }

            .card-body {
                padding: 0.8rem;
            }
        }

        @media (max-width: 768px) {
            .modal-dialog {
                margin: 0.5rem;
                max-width: calc(100% - 1rem);
            }

            .modal-content {
                border-radius: 12px;
            }

            .modal-header {
                padding: 1rem;
                border-bottom: 1px solid #dee2e6;
            }

            .modal-body {
                padding: 1rem;
            }

            .modal-footer {
                padding: 1rem;
                border-top: 1px solid #dee2e6;
            }

            .modal-footer .btn {
                width: 100%;
                margin-bottom: 0.5rem;
            }

            .form-control,
            .form-select {
                font-size: 16px !important;
                padding: 0.75rem;
            }

            .form-label {
                font-weight: 600;
                margin-bottom: 0.5rem;
            }
        }

        @media (max-width: 576px) {
            .modal-dialog {
                margin: 0.25rem;
                max-width: calc(100% - 0.5rem);
            }

            .modal-header,
            .modal-body,
            .modal-footer {
                padding: 0.8rem;
            }

            .modal-title {
                font-size: 1.1rem;
            }
        }
    </style>
</head>
<body>
    <?php include '../includes/mobile_navbar.php'; ?>
    <div class="container" style="margin-top: 80px;">
        <div class="questions-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="questions-title">
                        <i class="fas fa-question-circle text-primary me-2"></i>
                        إدارة الأسئلة
                    </div>
                    <div class="questions-subtitle">
                        <?php echo htmlspecialchars($lecture['title']); ?> - <?php echo htmlspecialchars($lecture['subject_name']); ?>
                        <span class="badge bg-info ms-2"><?php echo count($questions); ?> سؤال</span>
                    </div>
                </div>
                <div>
                    <a href="lectures.php?subject_id=<?php echo $lecture['subject_id']; ?>" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للمحاضرات
                    </a>
                    <a href="bulk_questions.php?lecture_id=<?php echo $lecture_id; ?>" class="btn btn-outline-success me-2">
                        <i class="fas fa-upload me-2"></i>
                        إضافة أسئلة متعددة
                    </a>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addQuestionModal">
                        <i class="fas fa-plus me-2"></i>
                        إضافة سؤال جديد
                    </button>
                </div>
            </div>
        </div>

        <!-- Messages -->
        <?php if ($message): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo $message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?php echo $error; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Questions Table -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    قائمة الأسئلة
                    <span class="badge bg-light text-dark ms-2"><?php echo count($questions); ?> سؤال</span>
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($questions)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-question-circle text-muted" style="font-size: 3rem;"></i>
                        <p class="text-muted mt-3">لا توجد أسئلة مضافة بعد</p>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addQuestionModal">
                            <i class="fas fa-plus me-2"></i>
                            إضافة أول سؤال
                        </button>
                    </div>
                <?php else: ?>
                    <?php foreach ($questions as $index => $question): ?>
                        <div class="question-card">
                            <div class="question-header">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0">
                                        <i class="fas fa-question-circle text-primary me-2"></i>
                                        السؤال رقم <?php echo $index + 1; ?>
                                    </h6>
                                    <div class="d-flex gap-2">
                                        <?php 
                                        $difficulty_colors = ['easy' => 'success', 'medium' => 'warning', 'hard' => 'danger'];
                                        $difficulty_text = ['easy' => 'سهل', 'medium' => 'متوسط', 'hard' => 'صعب'];
                                        ?>
                                        <span class="badge bg-<?php echo $difficulty_colors[$question['difficulty']]; ?>">
                                            <?php echo $difficulty_text[$question['difficulty']]; ?>
                                        </span>
                                        <span class="badge bg-info"><?php echo $question['points']; ?> نقاط</span>
                                        <span class="badge bg-<?php echo $question['status'] === 'active' ? 'success' : 'danger'; ?>">
                                            <?php echo $question['status'] === 'active' ? 'نشط' : 'غير نشط'; ?>
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="question-body">
                                <div class="question-text">
                                    <?php echo nl2br(htmlspecialchars($question['question_text'])); ?>
                                </div>

                                <ul class="options-list">
                                    <?php 
                                    $options = [
                                        'a' => $question['option_a'],
                                        'b' => $question['option_b'],
                                        'c' => $question['option_c'],
                                        'd' => $question['option_d']
                                    ];
                                    $answers = ['a' => 'أ', 'b' => 'ب', 'c' => 'ج', 'd' => 'د'];
                                    ?>
                                    <?php foreach ($options as $key => $option): ?>
                                        <li class="option-item <?php echo $key === $question['correct_answer'] ? 'correct' : ''; ?>">
                                            <span class="option-label"><?php echo $answers[$key]; ?>.</span>
                                            <?php echo htmlspecialchars($option); ?>
                                            <?php if ($key === $question['correct_answer']): ?>
                                                <i class="fas fa-check-circle text-success ms-2"></i>
                                            <?php endif; ?>
                                        </li>
                                    <?php endforeach; ?>
                                </ul>

                                <div class="question-meta">
                                    <div class="text-muted">
                                        <small>
                                            <i class="fas fa-user me-1"></i>
                                            <?php echo htmlspecialchars($question['created_by_name']); ?>
                                            <i class="fas fa-calendar ms-3 me-1"></i>
                                            <?php echo date('Y-m-d', strtotime($question['created_at'])); ?>
                                        </small>
                                    </div>
                                    <div class="question-actions">
                                        <button class="btn btn-sm btn-outline-primary"
                                                onclick="editQuestion(<?php echo htmlspecialchars(json_encode($question), ENT_QUOTES, 'UTF-8'); ?>)"
                                                title="تعديل"
                                                data-question-id="<?php echo $question['id']; ?>">
                                            <i class="fas fa-edit me-1"></i>
                                            تعديل
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger" 
                                                onclick="deleteQuestion(<?php echo $question['id']; ?>, '<?php echo htmlspecialchars(substr($question['question_text'], 0, 30)); ?>')"
                                                title="حذف">
                                            <i class="fas fa-trash me-1"></i>
                                            حذف
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Add Question Modal -->
    <div class="modal fade" id="addQuestionModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-plus me-2"></i>
                        إضافة سؤال جديد
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" class="needs-validation" novalidate>
                    <div class="modal-body">
                        <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                        <input type="hidden" name="action" value="add">

                        <div class="mb-3">
                            <label for="question_text" class="form-label">نص السؤال *</label>
                            <textarea class="form-control" id="question_text" name="question_text" rows="3" required></textarea>
                            <div class="invalid-feedback">يرجى إدخال نص السؤال</div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="option_a" class="form-label">الخيار أ *</label>
                                    <input type="text" class="form-control" id="option_a" name="option_a" required>
                                    <div class="invalid-feedback">يرجى إدخال الخيار أ</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="option_b" class="form-label">الخيار ب *</label>
                                    <input type="text" class="form-control" id="option_b" name="option_b" required>
                                    <div class="invalid-feedback">يرجى إدخال الخيار ب</div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="option_c" class="form-label">الخيار ج *</label>
                                    <input type="text" class="form-control" id="option_c" name="option_c" required>
                                    <div class="invalid-feedback">يرجى إدخال الخيار ج</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="option_d" class="form-label">الخيار د *</label>
                                    <input type="text" class="form-control" id="option_d" name="option_d" required>
                                    <div class="invalid-feedback">يرجى إدخال الخيار د</div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="correct_answer" class="form-label">الإجابة الصحيحة *</label>
                                    <select class="form-select" id="correct_answer" name="correct_answer" required>
                                        <option value="">اختر الإجابة</option>
                                        <option value="a">أ</option>
                                        <option value="b">ب</option>
                                        <option value="c">ج</option>
                                        <option value="d">د</option>
                                    </select>
                                    <div class="invalid-feedback">يرجى اختيار الإجابة الصحيحة</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="points" class="form-label">النقاط</label>
                                    <input type="number" class="form-control" id="points" name="points" value="1" min="1" max="10">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="difficulty" class="form-label">مستوى الصعوبة</label>
                                    <select class="form-select" id="difficulty" name="difficulty">
                                        <option value="easy">سهل</option>
                                        <option value="medium" selected>متوسط</option>
                                        <option value="hard">صعب</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            حفظ
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Question Modal -->
    <div class="modal fade" id="editQuestionModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-edit me-2"></i>
                        تعديل السؤال
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" class="needs-validation" novalidate>
                    <div class="modal-body">
                        <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                        <input type="hidden" name="action" value="edit">
                        <input type="hidden" name="id" id="edit_id">

                        <div class="mb-3">
                            <label for="edit_question_text" class="form-label">نص السؤال *</label>
                            <textarea class="form-control" id="edit_question_text" name="question_text" rows="3" required></textarea>
                            <div class="invalid-feedback">يرجى إدخال نص السؤال</div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="edit_option_a" class="form-label">الخيار أ *</label>
                                    <input type="text" class="form-control" id="edit_option_a" name="option_a" required>
                                    <div class="invalid-feedback">يرجى إدخال الخيار أ</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="edit_option_b" class="form-label">الخيار ب *</label>
                                    <input type="text" class="form-control" id="edit_option_b" name="option_b" required>
                                    <div class="invalid-feedback">يرجى إدخال الخيار ب</div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="edit_option_c" class="form-label">الخيار ج *</label>
                                    <input type="text" class="form-control" id="edit_option_c" name="option_c" required>
                                    <div class="invalid-feedback">يرجى إدخال الخيار ج</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="edit_option_d" class="form-label">الخيار د *</label>
                                    <input type="text" class="form-control" id="edit_option_d" name="option_d" required>
                                    <div class="invalid-feedback">يرجى إدخال الخيار د</div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="edit_correct_answer" class="form-label">الإجابة الصحيحة *</label>
                                    <select class="form-select" id="edit_correct_answer" name="correct_answer" required>
                                        <option value="a">أ</option>
                                        <option value="b">ب</option>
                                        <option value="c">ج</option>
                                        <option value="d">د</option>
                                    </select>
                                    <div class="invalid-feedback">يرجى اختيار الإجابة الصحيحة</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="edit_points" class="form-label">النقاط</label>
                                    <input type="number" class="form-control" id="edit_points" name="points" min="1" max="10">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="edit_difficulty" class="form-label">مستوى الصعوبة</label>
                                    <select class="form-select" id="edit_difficulty" name="difficulty">
                                        <option value="easy">سهل</option>
                                        <option value="medium">متوسط</option>
                                        <option value="hard">صعب</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="edit_status" class="form-label">الحالة</label>
                                    <select class="form-select" id="edit_status" name="status">
                                        <option value="active">نشط</option>
                                        <option value="inactive">غير نشط</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            حفظ التغييرات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <footer class="footer">
        <div class="container">
            <div class="footer-title">جامعة المنيا - قسم الـ Forensic</div>
            <div class="footer-bottom">
                &copy; 2024 جميع الحقوق محفوظة - جامعة المنيا - قسم الـ Forensic
            </div>
        </div>
    </footer>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/main.js"></script>
    <script>
        // التأكد من تحميل Bootstrap
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof bootstrap === 'undefined') {
                console.error('Bootstrap is not loaded!');
                alert('خطأ: لم يتم تحميل Bootstrap بشكل صحيح');
            } else {
                console.log('Bootstrap loaded successfully');
            }

            // إضافة event listeners لأزرار التعديل
            const editButtons = document.querySelectorAll('[data-question-id]');
            editButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    const questionId = this.getAttribute('data-question-id');
                    console.log('Edit button clicked for question:', questionId);
                });
            });
        });

        function editQuestion(question) {
            console.log('editQuestion called with:', question);

            try {
                // التحقق من وجود Bootstrap
                if (typeof bootstrap === 'undefined') {
                    throw new Error('Bootstrap is not loaded');
                }

                // التحقق من وجود العناصر المطلوبة
                const modal = document.getElementById('editQuestionModal');
                if (!modal) {
                    throw new Error('Edit modal not found');
                }

                // ملء البيانات في النموذج
                const elements = {
                    'edit_id': question.id,
                    'edit_question_text': question.question_text,
                    'edit_option_a': question.option_a,
                    'edit_option_b': question.option_b,
                    'edit_option_c': question.option_c,
                    'edit_option_d': question.option_d,
                    'edit_correct_answer': question.correct_answer,
                    'edit_points': question.points || 1,
                    'edit_difficulty': question.difficulty || 'medium',
                    'edit_status': question.status || 'active'
                };

                for (const [id, value] of Object.entries(elements)) {
                    const element = document.getElementById(id);
                    if (element) {
                        element.value = value;
                    } else {
                        console.warn(`Element with id '${id}' not found`);
                    }
                }

                // فتح النافذة المنبثقة
                const bootstrapModal = new bootstrap.Modal(modal);
                bootstrapModal.show();

                console.log('Edit modal opened successfully for question:', question.id);
            } catch (error) {
                console.error('Error in editQuestion:', error);
                alert('حدث خطأ في فتح نافذة التعديل: ' + error.message);
            }
        }

        function deleteQuestion(id, title) {
            if (confirm(`هل أنت متأكد من حذف السؤال: ${title}؟`)) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                    <input type="hidden" name="action" value="delete">
                    <input type="hidden" name="id" value="${id}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }
    </script>
    <script src="../assets/js/mobile.js"></script>
</body>
</html>