<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

if (!isLoggedIn()) {
    header('Location: ../index.php');
    exit();
}

if (!isAdmin() && !isAssistant()) {
    header('Location: ../index.php');
    exit();
}

$message = '';
$error = '';

try {
    $stmt = $pdo->prepare("UPDATE exams SET status = 'completed' WHERE status = 'active' AND end_time < NOW()");
    $stmt->execute();
    $updated_count = $stmt->rowCount();
    if ($updated_count > 0) {
        logActivity($_SESSION['user_id'], 'auto_complete_exams', "تم تحديث حالة {$updated_count} امتحان إلى مكتمل تلقائياً");

        if (isset($_POST['update_status_only'])) {
            echo json_encode(['success' => true, 'updated_count' => $updated_count]);
            exit();
        }
    }
} catch (PDOException $e) {
    error_log('Error auto-updating exam status: ' . $e->getMessage());

    if (isset($_POST['update_status_only'])) {
        echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        exit();
    }
}

if (isset($_POST['update_status_only'])) {
    echo json_encode(['success' => true, 'updated_count' => 0]);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    if (!verifyCSRFToken($_POST['csrf_token'])) {
        $error = 'خطأ في التحقق من الأمان';
    } else {
        if ($_POST['action'] === 'add') {
            $title = sanitize($_POST['title']);
            $subject_ids = isset($_POST['subject_ids']) ? $_POST['subject_ids'] : [];
            $exam_type = $_POST['exam_type'];
            $description = sanitize($_POST['description']);
            $start_time = $_POST['start_time'];
            $end_time = $_POST['end_time'];
            $duration_minutes = (int)$_POST['duration_minutes'];
            $max_attempts = (int)$_POST['max_attempts'];
            $questions_count = isset($_POST['questions_count']) ? (int)$_POST['questions_count'] : 10;
            $randomize_questions = isset($_POST['randomize_questions']) ? 1 : 0;
            $show_results_immediately = isset($_POST['show_results_immediately']) ? 1 : 0;
            $show_results_after_end = isset($_POST['show_results_after_end']) ? 1 : 0;
            $passing_percentage = 50.00;

            if (empty($title) || empty($subject_ids)) {
                $error = 'يرجى ملء جميع الحقول المطلوبة';
            } elseif (empty($start_time) || empty($end_time)) {
                $error = 'يرجى إدخال وقت البداية والنهاية';
            } elseif (empty($duration_minutes) || $duration_minutes < 15) {
                $error = 'يرجى إدخال مدة الامتحان (15 دقيقة على الأقل)';
            } elseif (strtotime($start_time) >= strtotime($end_time)) {
                $error = 'وقت البداية يجب أن يكون قبل وقت النهاية';
            } else {
                try {
                    $pdo->beginTransaction();

                    $exam_link = md5($title . uniqid() . time());

                    $stmt = $pdo->prepare("INSERT INTO exams (title, subject_id, exam_type, description, start_time, end_time, duration_minutes, max_attempts, questions_count, randomize_questions, show_results_immediately, show_results_after_end, passing_percentage, exam_link, created_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                    if ($stmt->execute([$title, $subject_ids[0], $exam_type, $description, $start_time, $end_time, $duration_minutes, $max_attempts, $questions_count, $randomize_questions, $show_results_immediately, $show_results_after_end, $passing_percentage, $exam_link, $_SESSION['user_id']])) {
                        $exam_id = $pdo->lastInsertId();

                        if (isset($_POST['lectures']) && is_array($_POST['lectures'])) {
                            $stmt = $pdo->prepare("INSERT INTO exam_lectures (exam_id, lecture_id) VALUES (?, ?)");
                            foreach ($_POST['lectures'] as $lecture_id) {
                                $stmt->execute([$exam_id, $lecture_id]);
                            }
                        }

                        $pdo->commit();
                        $message = 'تم إنشاء الامتحان بنجاح';
                        logActivity($_SESSION['user_id'], 'add_exam', "إضافة امتحان جديد: $title");
                    } else {
                        $pdo->rollBack();
                        $error = 'خطأ في إنشاء الامتحان';
                    }

                } catch (PDOException $e) {
                    $pdo->rollBack();
                    $error = 'خطأ في قاعدة البيانات: ' . $e->getMessage();
                    error_log('Exam creation error: ' . $e->getMessage());
                }
            }
        } elseif ($_POST['action'] === 'edit') {
            $id = (int)$_POST['id'];
            $title = sanitize($_POST['title']);
            $subject_ids = isset($_POST['subject_ids']) ? $_POST['subject_ids'] : [];
            $exam_type = $_POST['exam_type'];
            $description = sanitize($_POST['description']);
            $start_time = $_POST['start_time'];
            $end_time = $_POST['end_time'];
            $duration_minutes = (int)$_POST['duration_minutes'];
            $max_attempts = (int)$_POST['max_attempts'];
            $questions_count = isset($_POST['questions_count']) ? (int)$_POST['questions_count'] : 10;
            $randomize_questions = isset($_POST['randomize_questions']) ? 1 : 0;
            $show_results_immediately = isset($_POST['show_results_immediately']) ? 1 : 0;
            $show_results_after_end = isset($_POST['show_results_after_end']) ? 1 : 0;
            $passing_percentage = 50.00;
            $status = $_POST['status'];

            if (empty($title)) {
                $error = 'يرجى إدخال عنوان الامتحان';
            } elseif (empty($subject_ids)) {
                $error = 'يرجى اختيار مادة واحدة على الأقل';
            } elseif (empty($start_time) || empty($end_time)) {
                $error = 'يرجى إدخال وقت البداية والنهاية';
            } elseif (empty($duration_minutes) || $duration_minutes < 15) {
                $error = 'يرجى إدخال مدة الامتحان (15 دقيقة على الأقل)';
            } elseif (strtotime($start_time) >= strtotime($end_time)) {
                $error = 'وقت البداية يجب أن يكون قبل وقت النهاية';
            } else {
                try {
                    $pdo->beginTransaction();

                    $stmt = $pdo->prepare("UPDATE exams SET title = ?, exam_type = ?, description = ?, start_time = ?, end_time = ?, duration_minutes = ?, max_attempts = ?, questions_count = ?, randomize_questions = ?, show_results_immediately = ?, show_results_after_end = ?, passing_percentage = ?, status = ? WHERE id = ?");
                    if ($stmt->execute([$title, $exam_type, $description, $start_time, $end_time, $duration_minutes, $max_attempts, $questions_count, $randomize_questions, $show_results_immediately, $show_results_after_end, $passing_percentage, $status, $id])) {

                        $stmt = $pdo->prepare("DELETE FROM exam_lectures WHERE exam_id = ?");
                        $stmt->execute([$id]);

                        if (isset($_POST['lectures']) && is_array($_POST['lectures'])) {
                            $stmt = $pdo->prepare("INSERT INTO exam_lectures (exam_id, lecture_id) VALUES (?, ?)");
                            foreach ($_POST['lectures'] as $lecture_id) {
                                $stmt->execute([$id, $lecture_id]);
                            }
                        }

                        $pdo->commit();
                        $message = 'تم تحديث الامتحان بنجاح';
                        logActivity($_SESSION['user_id'], 'edit_exam', "تحديث امتحان: $title");
                    } else {
                        $pdo->rollBack();
                        $error = 'خطأ في تحديث الامتحان';
                    }
                } catch (PDOException $e) {
                    $pdo->rollBack();
                    $error = 'خطأ في قاعدة البيانات: ' . $e->getMessage();
                    error_log('Exam update error: ' . $e->getMessage());
                }
            }
        } elseif ($_POST['action'] === 'delete') {
            $id = (int)$_POST['id'];

            try {
                $stmt = $pdo->prepare("DELETE FROM exams WHERE id = ?");
                if ($stmt->execute([$id])) {
                    $message = 'تم حذف الامتحان بنجاح';
                    logActivity($_SESSION['user_id'], 'delete_exam', "حذف امتحان برقم: $id");
                } else {
                    $error = 'خطأ في حذف الامتحان';
                }
            } catch (PDOException $e) {
                $error = 'خطأ في قاعدة البيانات';
            }
        }
    }
}

function generateExamLink($exam_id) {
    $characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()_+-=[]{}|;:,.<>?';
    $length = 32;
    $link = '';

    for ($i = 0; $i < $length; $i++) {
        $link .= $characters[rand(0, strlen($characters) - 1)];
    }

    return $link . '_' . $exam_id;
}

try {
    $stmt = $pdo->query("SELECT id, title, created_at FROM exams WHERE exam_link IS NULL OR exam_link = ''");
    $exams_without_link = $stmt->fetchAll();

    if (!empty($exams_without_link)) {
        $update_stmt = $pdo->prepare("UPDATE exams SET exam_link = ? WHERE id = ?");
        foreach ($exams_without_link as $exam) {
            $exam_link = md5($exam['title'] . $exam['created_at'] . $exam['id']);
            $update_stmt->execute([$exam_link, $exam['id']]);
        }
    }
} catch (Exception $e) {
    error_log("Error updating exam links: " . $e->getMessage());
}

try {
    $stmt = $pdo->query("SELECT e.*, u.full_name as created_by_name,
                        (SELECT COUNT(*) FROM exam_results WHERE exam_id = e.id) as students_count,
                        (SELECT COUNT(*) FROM exam_questions WHERE exam_id = e.id) as actual_questions_count,
                        (SELECT GROUP_CONCAT(DISTINCT s.name SEPARATOR ', ')
                         FROM exam_lectures el
                         JOIN lectures l ON el.lecture_id = l.id
                         JOIN subjects s ON l.subject_id = s.id
                         WHERE el.exam_id = e.id) as subjects_list,
                        e.exam_link
                        FROM exams e
                        LEFT JOIN users u ON e.created_by = u.id
                        ORDER BY e.created_at DESC");
    $exams = $stmt->fetchAll();

    $stmt = $pdo->query("SELECT * FROM subjects WHERE status = 'active' ORDER BY name");
    $subjects = $stmt->fetchAll();

} catch (PDOException $e) {
    $error = 'خطأ في تحميل البيانات';
}

$csrf_token = generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>إدارة الامتحانات - نظام الامتحانات الإلكترونية</title>
    <link <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;500;600&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="../assets/css/mobile.css" rel="stylesheet">
    <style>
        body {
            background: #183046;
            min-height: 100vh;
            font-family: 'Cairo', sans-serif;
            overflow-x: hidden;
            overflow-y: auto;
        }
        .navbar {
            background: #fff !important;
            box-shadow: 0 2px 10px rgba(0,0,0,0.07);
            border-bottom: 1px solid #e3e6f0;
        }
        .navbar-brand {
            font-weight: 700;
            font-size: 1.2rem;
            color: #2c3e50 !important;
            line-height: 1.2;
            display: flex;
            align-items: center;
        }

        .brand-text {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .nav-link, .navbar-nav .nav-link {
            color: #2c3e50 !important;
            font-weight: 600;
            border-radius: 25px;
            margin: 0 0.3rem;
        }
        .nav-link.active, .nav-link:hover {
            background: #2196f3;
            color: #fff !important;
        }
        .exams-header {
            margin-top: 2.5rem;
            margin-bottom: 2.5rem;
            text-align: right;
        }
        .exams-title {
            font-size: 2.3rem;
            font-weight: 900;
            color: #fff;
            margin-bottom: 0.5rem;
        }
        .exams-subtitle {
            color: #e0e0e0;
            font-size: 1.1rem;
            font-weight: 400;
        }
        .modern-card {
            background: #fff;
            border-radius: 16px;
            box-shadow: 0 4px 24px rgba(102,126,234,0.08);
            padding: 1.5rem 1.2rem;
            margin-bottom: 2rem;
        }
        .modern-card .card-header {
            background: none;
            border: none;
            padding: 0 0 1rem 0;
            color: #2196f3;
            font-weight: 700;
            font-size: 1.2rem;
        }
        .modern-card .list-group-item {
            border: none;
            border-bottom: 1px solid #f0f0f0;
            padding: 0.7rem 0.2rem;
        }
        .modern-card .list-group-item:last-child {
            border-bottom: none;
        }
        .card.border-0.shadow-sm {
            border-radius: 18px;
            background: #fff;
            box-shadow: 0 8px 32px rgba(102,126,234,0.08);
            padding: 1.5rem 1.2rem;
        }
        .card-header.bg-primary {
            background: #2196f3 !important;
            color: #fff !important;
            border-radius: 16px 16px 0 0 !important;
            font-weight: 700;
            font-size: 1.2rem;
        }
        .btn-primary, .btn-outline-primary:hover {
            background: #2196f3 !important;
            border-color: #2196f3 !important;
            color: #fff !important;
        }
        .btn-outline-primary {
            color: #2196f3 !important;
            border-color: #2196f3 !important;
            background: #fff !important;
        }
        .btn-outline-primary:focus, .btn-outline-primary:active {
            background: #2196f3 !important;
            color: #fff !important;
        }
        .table {
            background: #fff;
            border-radius: 12px;
            overflow: hidden;
        }
        .table th, .table td {
            vertical-align: middle;
        }
        .modal-content {
            border-radius: 16px;
            border: none;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
        }
        .modal-header {
            border-bottom: 1px solid #f0f0f0;
            border-radius: 16px 16px 0 0;
        }
        .modal-title {
            color: #2196f3;
            font-weight: 700;
        }
        .form-label {
            font-weight: 600;
            color: #2c3e50;
        }
        .form-control, .form-select {
            border-radius: 10px;
            border: 1px solid #e3e6f0;
        }
        .form-control:focus, .form-select:focus {
            border-color: #2196f3;
            box-shadow: 0 0 0 0.2rem rgba(33, 150, 243, 0.25);
        }
        .btn-close {
            margin: 0;
        }
        .card {
            border: 1px solid #e3e6f0;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }
        .card-header {
            background: #f8f9fa;
            border-bottom: 1px solid #e3e6f0;
            border-radius: 12px 12px 0 0;
        }
        .form-check-input:checked {
            background-color: #2196f3;
            border-color: #2196f3;
        }
        .form-check-label {
            cursor: pointer;
        }
        .modal-xl {
            max-width: 90%;
        }
        .lecture-checkbox {
            border: 1px solid #e3e6f0;
            border-radius: 8px;
            padding: 0.5rem;
            margin-bottom: 0.5rem;
            transition: all 0.3s ease;
        }
        .lecture-checkbox:hover {
            background-color: #f8f9fa;
            border-color: #2196f3;
        }
        .lecture-checkbox .form-check-label {
            cursor: pointer;
            width: 100%;
        }
        .lecture-checkbox .form-check-input:checked + .form-check-label {
            color: #2196f3;
            font-weight: 600;
        }
        .footer {
            background: #2c3e50;
            color: #fff;
            padding: 2.5rem 0 1rem;
            margin-top: 3rem;
        }
        .footer-title {
            font-size: 1.2rem;
            font-weight: 700;
        }
        .footer-bottom {
            border-top: 1px solid rgba(255,255,255,0.1);
            padding-top: 1rem;
            text-align: center;
            opacity: 0.7;
        }
        @media (max-width: 991px) {
            .exams-header {margin-top: 1.5rem;}
        }
    </style>
</head>
<body>
    <?php include '../includes/mobile_navbar.php'; ?>
    <div class="container" style="margin-top: 80px;">
        <div class="exams-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="exams-title">
                        <i class="fas fa-file-alt text-primary me-2"></i>
                        إدارة الامتحانات
                    </div>
                    <div class="exams-subtitle">إدارة امتحانات قسم الـ Forensic - جامعة المنيا</div>
                </div>
                <div>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addExamModal">
                        <i class="fas fa-plus me-2"></i>
                        إنشاء امتحان جديد
                    </button>
                </div>
            </div>
        </div>

        <!-- Messages -->
        <?php if ($message): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo $message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?php echo $error; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Exams Table -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    قائمة الامتحانات
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($exams)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-file-alt text-muted" style="font-size: 3rem;"></i>
                        <p class="text-muted mt-3">لا توجد امتحانات منشأة بعد</p>
                        <p class="text-muted">اضغط على "إنشاء امتحان جديد" في الأعلى لبدء إنشاء الامتحانات</p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover" id="examsTable">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>عنوان الامتحان</th>
                                    <th>المواد</th>
                                    <th>النوع</th>
                                    <th>وقت البداية</th>
                                    <th>وقت النهاية</th>
                                    <th>المدة</th>
                                    <th>عدد الأسئلة</th>
                                    <th>ترتيب الأسئلة</th>
                                    <th>عدد الطلاب</th>
                                    <th>الحالة</th>
                                    <th>أنشئ بواسطة</th>
                                    <th>رابط الامتحان</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($exams as $index => $exam): ?>
                                    <tr>
                                        <td><?php echo $index + 1; ?></td>
                                        <td>
                                            <strong><?php echo htmlspecialchars($exam['title']); ?></strong>
                                            <?php if ($exam['description']): ?>
                                                <br><small class="text-muted"><?php echo htmlspecialchars(substr($exam['description'], 0, 50)) . (strlen($exam['description']) > 50 ? '...' : ''); ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($exam['subjects_list']): ?>
                                                <?php echo htmlspecialchars($exam['subjects_list']); ?>
                                            <?php else: ?>
                                                <span class="text-muted">لا توجد مواد</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?php echo $exam['exam_type'] === 'exam' ? 'primary' : 'info'; ?>">
                                                <?php echo $exam['exam_type'] === 'exam' ? 'امتحان' : 'كويز'; ?>
                                            </span>
                                        </td>
                                        <td><?php echo date('Y-m-d H:i', strtotime($exam['start_time'])); ?></td>
                                        <td><?php echo date('Y-m-d H:i', strtotime($exam['end_time'])); ?></td>
                                        <td><?php echo $exam['duration_minutes']; ?> دقيقة</td>
                                        <td>
                                            <span class="badge bg-info"><?php echo $exam['actual_questions_count']; ?></span>
                                            <br><small class="text-muted">مطلوب: <?php echo $exam['questions_count']; ?></small>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?php echo $exam['randomize_questions'] ? 'success' : 'secondary'; ?>">
                                                <i class="fas fa-<?php echo $exam['randomize_questions'] ? 'random' : 'sort'; ?> me-1"></i>
                                                <?php echo $exam['randomize_questions'] ? 'عشوائي' : 'منتظم'; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-success"><?php echo $exam['students_count']; ?></span>
                                        </td>
                                        <td>
                                            <?php
                                            $statusClass = '';
                                            $statusText = '';
                                            switch ($exam['status']) {
                                                case 'draft':
                                                    $statusClass = 'secondary';
                                                    $statusText = 'مسودة';
                                                    break;
                                                case 'active':
                                                    $statusClass = 'success';
                                                    $statusText = 'نشط';
                                                    break;
                                                case 'completed':
                                                    $statusClass = 'warning';
                                                    $statusText = 'مكتمل';
                                                    break;
                                                case 'cancelled':
                                                    $statusClass = 'danger';
                                                    $statusText = 'ملغي';
                                                    break;
                                            }
                                            ?>
                                            <span class="badge bg-<?php echo $statusClass; ?>"><?php echo $statusText; ?></span>
                                        </td>
                                        <td><?php echo htmlspecialchars($exam['created_by_name']); ?></td>
                                        <td>
                                            <div class="input-group input-group-sm">
                                                <input type="text" class="form-control form-control-sm"
                                                       value="<?php echo htmlspecialchars($exam['exam_link']); ?>"
                                                       readonly style="font-size: 0.75rem;">
                                                <button class="btn btn-outline-secondary btn-sm" type="button"
                                                        onclick="copyExamLink('<?php echo htmlspecialchars($exam['exam_link']); ?>')"
                                                        title="نسخ الرابط">
                                                    <i class="fas fa-copy"></i>
                                                </button>
                                            </div>
                                            <small class="text-muted">رابط الامتحان للطلاب</small>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button class="btn btn-sm btn-outline-primary"
                                                        onclick="editExam(<?php echo htmlspecialchars(json_encode($exam)); ?>)"
                                                        title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <a href="../exam_entry.php/<?php echo htmlspecialchars($exam['exam_link']); ?>"
                                                   class="btn btn-sm btn-outline-info" title="دخول الامتحان" target="_blank">
                                                    <i class="fas fa-external-link-alt"></i>
                                                </a>
                                                <a href="exam_results.php?exam_id=<?php echo $exam['id']; ?>"
                                                   class="btn btn-sm btn-outline-success" title="النتائج">
                                                    <i class="fas fa-chart-bar"></i>
                                                </a>
                                                <a href="exam_questions.php?exam_id=<?php echo $exam['id']; ?>"
                                                   class="btn btn-sm btn-outline-warning" title="الأسئلة">
                                                    <i class="fas fa-question-circle"></i>
                                                </a>
                                                <button class="btn btn-sm btn-outline-danger"
                                                        onclick="deleteExam(<?php echo $exam['id']; ?>, '<?php echo htmlspecialchars($exam['title']); ?>')"
                                                        title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Add Exam Modal -->
    <div class="modal fade" id="addExamModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title">
                        <i class="fas fa-plus me-2"></i>
                        إنشاء امتحان جديد
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" class="needs-validation" novalidate>
                    <div class="modal-body">
                        <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                        <input type="hidden" name="action" value="add">

                        <!-- المعلومات الأساسية -->
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">
                                    <i class="fas fa-info-circle text-primary me-2"></i>
                                    المعلومات الأساسية
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="mb-3">
                                            <label for="title" class="form-label fw-bold">عنوان الامتحان *</label>
                                            <input type="text" class="form-control form-control-lg" id="title" name="title" placeholder="أدخل عنوان الامتحان" required>
                                            <div class="invalid-feedback">يرجى إدخال عنوان الامتحان</div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="exam_type" class="form-label fw-bold">نوع الامتحان</label>
                                            <select class="form-select" id="exam_type" name="exam_type">
                                                <option value="exam">امتحان</option>
                                                <option value="quiz">كويز</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="subject_ids" class="form-label fw-bold">المواد المطلوبة *</label>
                                    <select class="form-select" id="subject_ids" name="subject_ids[]" multiple required style="height: 120px;">
                                        <?php foreach ($subjects as $subject): ?>
                                            <option value="<?php echo $subject['id']; ?>"><?php echo htmlspecialchars($subject['name']); ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                    <div class="form-text">
                                        <i class="fas fa-info-circle me-1"></i>
                                        اضغط Ctrl (أو Cmd على Mac) لاختيار مواد متعددة
                                    </div>
                                    <div class="invalid-feedback">يرجى اختيار مادة واحدة على الأقل</div>
                                </div>

                                <div class="mb-3">
                                    <label for="description" class="form-label fw-bold">وصف الامتحان</label>
                                    <textarea class="form-control" id="description" name="description" rows="3" placeholder="أدخل وصفاً مختصراً للامتحان"></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- إعدادات التوقيت -->
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">
                                    <i class="fas fa-clock text-primary me-2"></i>
                                    إعدادات التوقيت
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="start_time" class="form-label fw-bold">وقت البداية *</label>
                                            <input type="datetime-local" class="form-control" id="start_time" name="start_time" required>
                                            <div class="invalid-feedback">يرجى إدخال وقت البداية</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="end_time" class="form-label fw-bold">وقت النهاية *</label>
                                            <input type="datetime-local" class="form-control" id="end_time" name="end_time" required>
                                            <div class="invalid-feedback">يرجى إدخال وقت النهاية</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="duration_minutes" class="form-label fw-bold">مدة الامتحان (دقيقة) *</label>
                                            <input type="number" class="form-control" id="duration_minutes" name="duration_minutes" value="60" min="15" max="300" required>
                                            <div class="form-text">
                                                <i class="fas fa-info-circle me-1"></i>
                                                الحد الأدنى: 15 دقيقة - الحد الأقصى: 300 دقيقة
                                            </div>
                                            <div class="invalid-feedback">يرجى إدخال مدة الامتحان</div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="max_attempts" class="form-label fw-bold">عدد المحاولات الإضافية</label>
                                            <input type="number" class="form-control" id="max_attempts" name="max_attempts" value="1" min="0" max="10">
                                            <small class="form-text text-muted">0 = دخول واحد فقط، 1 = دخول مرتين، 2 = دخول 3 مرات</small>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="questions_count" class="form-label fw-bold">عدد الأسئلة المطلوبة</label>
                                            <input type="number" class="form-control" id="questions_count" name="questions_count" value="10" min="1" max="100" placeholder="أدخل عدد الأسئلة">
                                            <div class="form-text">
                                                <i class="fas fa-info-circle me-1"></i>
                                                سيتم اختيار هذا العدد من الأسئلة من المحاضرات المختارة
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- خيارات إضافية -->
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">
                                    <i class="fas fa-cog text-primary me-2"></i>
                                    خيارات إضافية
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="randomize_questions" name="randomize_questions">
                                            <label class="form-check-label fw-bold" for="randomize_questions">
                                                <i class="fas fa-random me-1"></i>
                                                ترتيب الأسئلة عشوائياً
                                            </label>
                                            <div class="form-text">
                                                <i class="fas fa-info-circle me-1"></i>
                                                إذا تم التحديد: سيتم عرض الأسئلة بترتيب عشوائي للطلاب
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="show_results_immediately" name="show_results_immediately">
                                            <label class="form-check-label fw-bold" for="show_results_immediately">
                                                <i class="fas fa-eye me-1"></i>
                                                إظهار النتيجة فور الإرسال
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="show_results_after_end" name="show_results_after_end" checked>
                                            <label class="form-check-label fw-bold" for="show_results_after_end">
                                                <i class="fas fa-chart-bar me-1"></i>
                                                إظهار النتيجة بعد انتهاء الامتحان
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- المحاضرات المطلوبة -->
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">
                                    <i class="fas fa-book text-primary me-2"></i>
                                    المحاضرات المطلوبة
                                </h6>
                            </div>
                            <div class="card-body">
                                <div id="lecturesActions" style="display: none;" class="mb-3">
                                    <button type="button" class="btn btn-sm btn-outline-primary me-2" onclick="selectAllLectures()">
                                        <i class="fas fa-check-square me-1"></i>
                                        اختيار الكل
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="deselectAllLectures()">
                                        <i class="fas fa-square me-1"></i>
                                        إلغاء الكل
                                    </button>
                                </div>
                                <div id="lecturesContainer">
                                    <div class="text-center py-3">
                                        <i class="fas fa-info-circle text-muted" style="font-size: 2rem;"></i>
                                        <p class="text-muted mt-2">اختر المواد أولاً لعرض المحاضرات المتاحة</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer bg-light">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-2"></i>
                            إلغاء
                        </button>
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-save me-2"></i>
                            إنشاء الامتحان
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Exam Modal -->
    <div class="modal fade" id="editExamModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title">
                        <i class="fas fa-edit me-2"></i>
                        تعديل الامتحان
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" class="needs-validation" novalidate>
                    <div class="modal-body">
                        <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                        <input type="hidden" name="action" value="edit">
                        <input type="hidden" name="id" id="edit_id">

                        <!-- المعلومات الأساسية -->
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">
                                    <i class="fas fa-info-circle text-primary me-2"></i>
                                    المعلومات الأساسية
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="mb-3">
                                            <label for="edit_title" class="form-label fw-bold">عنوان الامتحان *</label>
                                            <input type="text" class="form-control form-control-lg" id="edit_title" name="title" placeholder="أدخل عنوان الامتحان" required>
                                            <div class="invalid-feedback">يرجى إدخال عنوان الامتحان</div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="edit_exam_type" class="form-label fw-bold">نوع الامتحان</label>
                                            <select class="form-select" id="edit_exam_type" name="exam_type">
                                                <option value="exam">امتحان</option>
                                                <option value="quiz">كويز</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="edit_description" class="form-label fw-bold">وصف الامتحان</label>
                                    <textarea class="form-control" id="edit_description" name="description" rows="3" placeholder="أدخل وصفاً مختصراً للامتحان"></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- إعدادات التوقيت -->
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">
                                    <i class="fas fa-clock text-primary me-2"></i>
                                    إعدادات التوقيت
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="edit_start_time" class="form-label fw-bold">وقت البداية *</label>
                                            <input type="datetime-local" class="form-control" id="edit_start_time" name="start_time" required>
                                            <div class="invalid-feedback">يرجى إدخال وقت البداية</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="edit_end_time" class="form-label fw-bold">وقت النهاية *</label>
                                            <input type="datetime-local" class="form-control" id="edit_end_time" name="end_time" required>
                                            <div class="invalid-feedback">يرجى إدخال وقت النهاية</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="edit_duration_minutes" class="form-label fw-bold">مدة الامتحان (دقيقة) *</label>
                                            <input type="number" class="form-control" id="edit_duration_minutes" name="duration_minutes" value="60" min="15" max="300" required>
                                            <div class="form-text">
                                                <i class="fas fa-info-circle me-1"></i>
                                                الحد الأدنى: 15 دقيقة - الحد الأقصى: 300 دقيقة
                                            </div>
                                            <div class="invalid-feedback">يرجى إدخال مدة الامتحان</div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="edit_max_attempts" class="form-label fw-bold">عدد المحاولات الإضافية</label>
                                            <input type="number" class="form-control" id="edit_max_attempts" name="max_attempts" value="1" min="0" max="10">
                                            <small class="form-text text-muted">0 = دخول واحد فقط، 1 = دخول مرتين، 2 = دخول 3 مرات</small>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="edit_questions_count" class="form-label fw-bold">عدد الأسئلة المطلوبة</label>
                                            <input type="number" class="form-control" id="edit_questions_count" name="questions_count" value="10" min="1" max="100" placeholder="أدخل عدد الأسئلة">
                                            <div class="form-text">
                                                <i class="fas fa-info-circle me-1"></i>
                                                سيتم اختيار هذا العدد من الأسئلة من المحاضرات المختارة
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- خيارات إضافية -->
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">
                                    <i class="fas fa-cog text-primary me-2"></i>
                                    خيارات إضافية
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="edit_randomize_questions" name="randomize_questions">
                                            <label class="form-check-label fw-bold" for="edit_randomize_questions">
                                                <i class="fas fa-random me-1"></i>
                                                ترتيب الأسئلة عشوائياً
                                            </label>
                                            <div class="form-text">
                                                <i class="fas fa-info-circle me-1"></i>
                                                إذا تم التحديد: سيتم عرض الأسئلة بترتيب عشوائي للطلاب
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="edit_show_results_immediately" name="show_results_immediately">
                                            <label class="form-check-label fw-bold" for="edit_show_results_immediately">
                                                <i class="fas fa-eye me-1"></i>
                                                إظهار النتيجة فور الإرسال
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="edit_show_results_after_end" name="show_results_after_end" checked>
                                            <label class="form-check-label fw-bold" for="edit_show_results_after_end">
                                                <i class="fas fa-chart-bar me-1"></i>
                                                إظهار النتيجة بعد انتهاء الامتحان
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="edit_status" class="form-label fw-bold">حالة الامتحان</label>
                            <select class="form-select" id="edit_status" name="status">
                                <option value="draft">مسودة</option>
                                <option value="active">نشط</option>
                                <option value="completed">مكتمل</option>
                                <option value="cancelled">ملغي</option>
                            </select>
                        </div>

                        <!-- اختيار المواد والمحاضرات -->
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">
                                    <i class="fas fa-book text-primary me-2"></i>
                                    المواد والمحاضرات
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="edit_subject_ids" class="form-label fw-bold">المواد المطلوبة *</label>
                                    <select class="form-select" id="edit_subject_ids" name="subject_ids[]" multiple required style="height: 120px;">
                                        <?php foreach ($subjects as $subject): ?>
                                            <option value="<?php echo $subject['id']; ?>"><?php echo htmlspecialchars($subject['name']); ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                    <div class="form-text">
                                        <i class="fas fa-info-circle me-1"></i>
                                        اضغط Ctrl (أو Cmd على Mac) لاختيار مواد متعددة
                                    </div>
                                    <div class="invalid-feedback">يرجى اختيار مادة واحدة على الأقل</div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label fw-bold">المحاضرات المختارة</label>
                                    <div id="editLecturesContainer">
                                        <div class="text-center py-3">
                                            <i class="fas fa-info-circle text-muted" style="font-size: 2rem;"></i>
                                            <p class="text-muted mt-2">سيتم تحميل المحاضرات المرتبطة بالامتحان تلقائياً</p>
                                        </div>
                                    </div>
                                    <div id="editLecturesActions" style="display: none;">
                                        <button type="button" class="btn btn-sm btn-outline-primary me-2" onclick="selectAllEditLectures()">
                                            <i class="fas fa-check-double me-1"></i>
                                            اختيار الكل
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="deselectAllEditLectures()">
                                            <i class="fas fa-times me-1"></i>
                                            إلغاء الكل
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer bg-light">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-2"></i>
                            إلغاء
                        </button>
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-save me-2"></i>
                            حفظ التغييرات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    </div>
    <footer class="footer">
        <div class="container">
            <div class="footer-title">جامعة المنيا - قسم الـ Forensic</div>
            <div class="footer-bottom">
                &copy; 2024 جميع الحقوق محفوظة - جامعة المنيا - قسم الـ Forensic
            </div>
        </div>
    </footer>
    <script src="https:
    <script src="../assets/js/main.js"></script>
    <script>

        document.getElementById('subject_ids').addEventListener('change', function() {
            const selectedSubjects = Array.from(this.selectedOptions).map(option => option.value);
            const container = document.getElementById('lecturesContainer');

            if (selectedSubjects.length > 0) {

                Promise.all(selectedSubjects.map(subjectId =>
                    fetch(`../ajax/get_lectures.php?subject_id=${subjectId}`)
                        .then(response => response.json())
                )).then(results => {
                    let allLectures = [];
                    results.forEach(data => {
                        if (data.success) {
                            allLectures = allLectures.concat(data.lectures);
                        }
                    });

                    if (allLectures.length > 0) {
                        let html = '<div class="row">';
                        allLectures.forEach(lecture => {
                                                    html += `
                            <div class="col-md-6 mb-2">
                                <div class="form-check lecture-checkbox">
                                    <input class="form-check-input" type="checkbox" name="lectures[]" value="${lecture.id}" id="lecture_${lecture.id}">
                                    <label class="form-check-label" for="lecture_${lecture.id}">
                                        <strong>${lecture.subject_name}</strong><br>
                                        <small class="text-muted">المحاضرة ${lecture.lecture_number}: ${lecture.title}</small><br>
                                        <span class="badge bg-info text-white">${lecture.questions_count} سؤال</span>
                                    </label>
                                </div>
                            </div>
                        `;
                        });
                        html += '</div>';
                        container.innerHTML = html;
                        document.getElementById('lecturesActions').style.display = 'block';
                    } else {
                        container.innerHTML = '<p class="text-danger">لا توجد محاضرات للمواد المختارة</p>';
                        document.getElementById('lecturesActions').style.display = 'none';
                    }
                }).catch(error => {
                    container.innerHTML = '<p class="text-danger">خطأ في تحميل المحاضرات</p>';
                    document.getElementById('lecturesActions').style.display = 'none';
                });
            } else {
                container.innerHTML = '<div class="text-center py-3"><i class="fas fa-info-circle text-muted" style="font-size: 2rem;"></i><p class="text-muted mt-2">اختر المواد أولاً لعرض المحاضرات المتاحة</p></div>';
                document.getElementById('lecturesActions').style.display = 'none';
            }
        });

        function selectAllLectures() {
            const checkboxes = document.querySelectorAll('input[name="lectures[]"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = true;
            });
        }

        function deselectAllLectures() {
            const checkboxes = document.querySelectorAll('input[name="lectures[]"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
        }

        function editExam(exam) {

            document.getElementById('edit_id').value = exam.id;
            document.getElementById('edit_title').value = exam.title;
            document.getElementById('edit_exam_type').value = exam.exam_type;
            document.getElementById('edit_description').value = exam.description;
            document.getElementById('edit_start_time').value = exam.start_time.replace(' ', 'T');
            document.getElementById('edit_end_time').value = exam.end_time.replace(' ', 'T');
            document.getElementById('edit_duration_minutes').value = exam.duration_minutes;
            document.getElementById('edit_max_attempts').value = exam.max_attempts;
            document.getElementById('edit_questions_count').value = exam.questions_count || 10;
            document.getElementById('edit_status').value = exam.status;

            document.getElementById('edit_randomize_questions').checked = exam.randomize_questions == 1;
            document.getElementById('edit_show_results_immediately').checked = exam.show_results_immediately == 1;
            document.getElementById('edit_show_results_after_end').checked = exam.show_results_after_end == 1;

            loadExamLectures(exam.id);

            new bootstrap.Modal(document.getElementById('editExamModal')).show();
        }

        function loadExamLectures(examId) {
            fetch(`../ajax/get_exam_lectures.php?exam_id=${examId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.lectures.length > 0) {

                        const subjectIds = [...new Set(data.lectures.map(l => l.subject_id))];

                        const subjectSelect = document.getElementById('edit_subject_ids');
                        if (subjectSelect) {
                            Array.from(subjectSelect.options).forEach(option => {
                                option.selected = subjectIds.includes(parseInt(option.value));
                            });
                        }

                        loadLecturesForEdit(data.lectures);
                    }
                })
                .catch(error => {
                    console.error('خطأ في تحميل محاضرات الامتحان:', error);
                });
        }

        function loadLecturesForEdit(examLectures) {
            const container = document.getElementById('editLecturesContainer');
            if (!container) return;

            if (examLectures.length > 0) {
                let html = '<div class="row">';
                examLectures.forEach(lecture => {
                    html += `
                        <div class="col-md-6 mb-2">
                            <div class="form-check lecture-checkbox">
                                <input class="form-check-input" type="checkbox" name="lectures[]" value="${lecture.id}" id="edit_lecture_${lecture.id}" checked>
                                <label class="form-check-label" for="edit_lecture_${lecture.id}">
                                    <strong>${lecture.subject_name}</strong><br>
                                    <small class="text-muted">المحاضرة ${lecture.lecture_number}: ${lecture.title}</small><br>
                                    <span class="badge bg-info text-white">${lecture.questions_count} سؤال</span>
                                </label>
                            </div>
                        </div>
                    `;
                });
                html += '</div>';
                container.innerHTML = html;

                const actionsDiv = document.getElementById('editLecturesActions');
                if (actionsDiv) actionsDiv.style.display = 'block';
            } else {
                container.innerHTML = '<p class="text-muted">لا توجد محاضرات مرتبطة بالامتحان</p>';
                const actionsDiv = document.getElementById('editLecturesActions');
                if (actionsDiv) actionsDiv.style.display = 'none';
            }
        }

        function selectAllEditLectures() {
            const checkboxes = document.querySelectorAll('input[name="lectures[]"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = true;
            });
        }

        function deselectAllEditLectures() {
            const checkboxes = document.querySelectorAll('input[name="lectures[]"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
        }

        document.addEventListener('DOMContentLoaded', function() {
            const editSubjectSelect = document.getElementById('edit_subject_ids');
            if (editSubjectSelect) {
                editSubjectSelect.addEventListener('change', function() {
                    const selectedSubjects = Array.from(this.selectedOptions).map(option => option.value);
                    const container = document.getElementById('editLecturesContainer');

                    if (selectedSubjects.length > 0) {

                        Promise.all(selectedSubjects.map(subjectId =>
                            fetch(`../ajax/get_lectures.php?subject_id=${subjectId}`)
                                .then(response => response.json())
                        )).then(results => {
                            let allLectures = [];
                            results.forEach(data => {
                                if (data.success) {
                                    allLectures = allLectures.concat(data.lectures);
                                }
                            });

                            if (allLectures.length > 0) {
                                let html = '<div class="row">';
                                allLectures.forEach(lecture => {
                                    html += `
                                        <div class="col-md-6 mb-2">
                                            <div class="form-check lecture-checkbox">
                                                <input class="form-check-input" type="checkbox" name="lectures[]" value="${lecture.id}" id="edit_lecture_${lecture.id}">
                                                <label class="form-check-label" for="edit_lecture_${lecture.id}">
                                                    <strong>${lecture.subject_name}</strong><br>
                                                    <small class="text-muted">المحاضرة ${lecture.lecture_number}: ${lecture.title}</small><br>
                                                    <span class="badge bg-info text-white">${lecture.questions_count} سؤال</span>
                                                </label>
                                            </div>
                                        </div>
                                    `;
                                });
                                html += '</div>';
                                container.innerHTML = html;
                                document.getElementById('editLecturesActions').style.display = 'block';
                            } else {
                                container.innerHTML = '<p class="text-danger">لا توجد محاضرات للمواد المختارة</p>';
                                document.getElementById('editLecturesActions').style.display = 'none';
                            }
                        }).catch(error => {
                            container.innerHTML = '<p class="text-danger">خطأ في تحميل المحاضرات</p>';
                            document.getElementById('editLecturesActions').style.display = 'none';
                        });
                    } else {
                        container.innerHTML = '<div class="text-center py-3"><i class="fas fa-info-circle text-muted" style="font-size: 2rem;"></i><p class="text-muted mt-2">اختر المواد أولاً لعرض المحاضرات المتاحة</p></div>';
                        document.getElementById('editLecturesActions').style.display = 'none';
                    }
                });
            }
        });

        function deleteExam(id, title) {
            if (confirm(`هل أنت متأكد من حذف الامتحان: ${title}؟`)) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                    <input type="hidden" name="action" value="delete">
                    <input type="hidden" name="id" value="${id}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        function copyExamLink(link) {

            const cleanLink = link.replace(/^exam_\d+_/, '');
            const fullUrl = window.location.origin + '/forensic/exam_entry.php/' + cleanLink;
            const input = document.createElement('input');
            input.value = fullUrl;
            document.body.appendChild(input);
            input.select();
            document.execCommand('copy');
            document.body.removeChild(input);
            alert('تم نسخ رابط الامتحان بنجاح!\n\nالرابط: ' + fullUrl);
        }

        function updateExamStatuses() {

            fetch('update_exam_status.php', {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.updated_count > 0) {

                    showStatusUpdateNotification(data.updated_count);

                    setTimeout(() => {
                        location.reload();
                    }, 2000);
                }

            })
            .catch(error => {
                console.log('خطأ في تحديث حالة الامتحانات:', error);
            });
        }

        setInterval(updateExamStatuses, 60000);

        function showStatusUpdateNotification(count) {
            const notification = document.createElement('div');
            notification.className = 'alert alert-info alert-dismissible fade show position-fixed';
            notification.style.cssText = 'top: 100px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                <i class="fas fa-sync-alt me-2"></i>
                تم تحديث حالة ${count} امتحان تلقائياً
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 5000);
        }

        document.addEventListener('DOMContentLoaded', function() {
            updateExamVisualStatus();

            setInterval(updateExamVisualStatus, 30000);
        });

        function updateExamVisualStatus() {
            const examRows = document.querySelectorAll('#examsTable tbody tr');
            examRows.forEach(row => {
                const statusCell = row.cells[10];
                const endTimeCell = row.cells[5];

                if (statusCell && endTimeCell) {
                    const endTimeText = endTimeCell.textContent.trim();
                    const endTime = new Date(endTimeText.replace(' ', 'T'));
                    const now = new Date();

                    if (now > endTime && statusCell.textContent.includes('نشط')) {
                        statusCell.innerHTML = `
                            <span class="badge bg-warning">
                                <i class="fas fa-clock me-1"></i>
                                انتهى - في انتظار التحديث
                            </span>
                        `;
                        row.style.opacity = '0.7';
                        row.style.backgroundColor = '#fff3cd';

                        statusCell.style.animation = 'pulse 2s infinite';
                    }

                    if (statusCell.textContent.includes('مكتمل')) {
                        row.style.opacity = '1';
                        row.style.backgroundColor = '';
                        statusCell.style.animation = '';
                    }
                }
            });
        }

        const style = document.createElement('style');
        style.textContent = `
            @keyframes pulse {
                0% { opacity: 1; }
                50% { opacity: 0.7; }
                100% { opacity: 1; }
            }
        `;
        document.head.appendChild(style);
    </script>
    <script src="../assets/js/mobile.js"></script>
    <!-- Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>