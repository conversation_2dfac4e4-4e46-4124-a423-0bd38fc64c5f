<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit();
}

if (!isset($_POST['exam_id']) || !isset($_POST['student_id']) || !isset($_POST['student_name'])) {
    echo json_encode(['success' => false, 'message' => 'بيانات غير مكتملة']);
    exit();
}

$exam_id = (int)$_POST['exam_id'];
$student_id = sanitize($_POST['student_id']);
$student_name = sanitize($_POST['student_name']);
$section_number = isset($_POST['section_number']) ? sanitize($_POST['section_number']) : '';

try {

    $result = calculateResult($exam_id, $student_id, isset($_POST['attempt_id']) ? (int)$_POST['attempt_id'] : null);

    $stmt = $pdo->prepare("SELECT * FROM exams WHERE id = ?");
    $stmt->execute([$exam_id]);
    $exam = $stmt->fetch();

    if (!$exam) {
        echo json_encode(['success' => false, 'message' => 'الامتحان غير موجود']);
        exit();
    }

} catch (PDOException $e) {
    echo json_encode(['success' => false, 'message' => 'خطأ في قاعدة البيانات']);
}
?>