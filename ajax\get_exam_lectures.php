<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');

require_once '../config/database.php';

$exam_id = isset($_GET['exam_id']) ? (int)$_GET['exam_id'] : 0;

if (!$exam_id) {
    echo json_encode(['success' => false, 'message' => 'معرف الامتحان مطلوب']);
    exit();
}

try {
    $stmt = $pdo->prepare("SELECT l.*, s.name as subject_name, s.id as subject_id,
                          (SELECT COUNT(*) FROM questions WHERE lecture_id = l.id AND status = 'active') as questions_count
                          FROM lectures l 
                          JOIN subjects s ON l.subject_id = s.id
                          JOIN exam_lectures el ON l.id = el.lecture_id
                          WHERE el.exam_id = ? AND l.status = 'active'
                          ORDER BY s.name, l.lecture_number");
    $stmt->execute([$exam_id]);
    $lectures = $stmt->fetchAll();

    echo json_encode([
        'success' => true,
        'lectures' => $lectures
    ]);

} catch (PDOException $e) {
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في قاعدة البيانات'
    ]);
}
?>