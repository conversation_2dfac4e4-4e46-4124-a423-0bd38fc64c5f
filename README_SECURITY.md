# دليل الأمان - نظام الامتحانات الإلكترونية

## 🔒 التحسينات الأمنية المطبقة

### 1. حماية قاعدة البيانات
- **استخدام متغيرات البيئة**: نقل بيانات الاتصال إلى متغيرات البيئة
- **Prepared Statements**: حماية من SQL Injection
- **تشفير كلمات المرور**: استخدام `password_hash()` مع cost عالي

### 2. إدارة الجلسات الآمنة
- **HttpOnly Cookies**: منع الوصول للـ cookies عبر JavaScript
- **Secure Cookies**: إرسال cookies فقط عبر HTTPS
- **Session Regeneration**: تجديد معرف الجلسة عند تسجيل الدخول
- **Session Timeout**: انتهاء صلاحية الجلسة بعد 30 دقيقة من عدم النشاط

### 3. Rate Limiting
- **حماية تسجيل الدخول**: حد أقصى 5 محاولات كل 15 دقيقة
- **حماية AJAX**: حد أقصى 100 طلب في الدقيقة
- **تتبع IP**: مراقبة المحاولات حسب عنوان IP

### 4. Security Headers
- **X-Frame-Options**: منع تضمين الموقع في iframe
- **X-Content-Type-Options**: منع تخمين نوع المحتوى
- **X-XSS-Protection**: تفعيل حماية XSS في المتصفح
- **Content-Security-Policy**: تقييد مصادر المحتوى
- **Referrer-Policy**: التحكم في معلومات المرجع

### 5. حماية الملفات
- **منع الوصول للملفات الحساسة**: .sql, .log, .env
- **حماية مجلدات النظام**: config/, includes/
- **منع تنفيذ PHP في مجلدات الرفع**

### 6. تسجيل الأحداث الأمنية
- **Security Logs**: تسجيل جميع الأحداث الأمنية
- **Activity Logs**: تتبع أنشطة المستخدمين
- **Failed Login Attempts**: مراقبة محاولات تسجيل الدخول الفاشلة

## 🚀 خطوات التفعيل

### 1. إعداد متغيرات البيئة
```bash
# انسخ ملف المثال
cp .env.example .env

# عدل القيم في ملف .env
nano .env
```

### 2. تفعيل HTTPS
```apache
# في .htaccess - قم بإلغاء التعليق عن هذه الأسطر
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# في headers
Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"
```

### 3. إعداد صلاحيات الملفات
```bash
# صلاحيات آمنة للملفات
chmod 644 *.php
chmod 600 .env
chmod 644 .htaccess
chmod 755 admin/
chmod 755 ajax/
```

### 4. فحص الأمان
- قم بزيارة `/admin/security_check.php` لفحص حالة الأمان
- راجع التوصيات وطبقها

## 📊 مراقبة الأمان

### جداول قاعدة البيانات الجديدة
1. **security_logs**: تسجيل الأحداث الأمنية
2. **rate_limiting**: تتبع محاولات الوصول

### استعلامات مفيدة
```sql
-- محاولات تسجيل دخول فاشلة في آخر 24 ساعة
SELECT * FROM security_logs 
WHERE event_type = 'failed_login' 
AND created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR);

-- عناوين IP المحظورة
SELECT * FROM rate_limiting 
WHERE attempts >= 5;

-- أحداث أمنية عالية الخطورة
SELECT * FROM security_logs 
WHERE severity IN ('high', 'critical') 
ORDER BY created_at DESC;
```

## ⚠️ تحذيرات مهمة

### 1. النسخ الاحتياطي
- احتفظ بنسخة احتياطية قبل تطبيق التحديثات
- اختبر التحديثات في بيئة تطوير أولاً

### 2. كلمات المرور
- غير كلمة مرور قاعدة البيانات
- استخدم كلمات مرور قوية
- لا تشارك ملف .env

### 3. التحديثات
- حدث PHP إلى أحدث إصدار آمن
- حدث MySQL/MariaDB
- راقب التحديثات الأمنية

## 🔧 استكشاف الأخطاء

### مشاكل شائعة
1. **خطأ في الاتصال بقاعدة البيانات**
   - تحقق من ملف .env
   - تأكد من صحة بيانات الاتصال

2. **مشاكل في الجلسات**
   - تحقق من صلاحيات مجلد sessions
   - تأكد من إعدادات PHP

3. **مشاكل في .htaccess**
   - تأكد من تفعيل mod_rewrite
   - راجع سجل أخطاء Apache

## 📞 الدعم

في حالة وجود مشاكل أمنية:
1. راجع ملف `logs/php_errors.log`
2. فحص جدول `security_logs`
3. استخدم صفحة فحص الأمان

## 📈 تحسينات مستقبلية

### قريباً
- [ ] Two-Factor Authentication (2FA)
- [ ] IP Whitelisting للإدارة
- [ ] تشفير البيانات الحساسة
- [ ] Audit Trail شامل

### متقدم
- [ ] Web Application Firewall (WAF)
- [ ] Intrusion Detection System (IDS)
- [ ] Automated Security Scanning
- [ ] Security Incident Response Plan
