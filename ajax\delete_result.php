<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit();
}

if (!isset($_POST['result_id']) || empty($_POST['result_id'])) {
    echo json_encode(['success' => false, 'message' => 'معرف النتيجة مطلوب']);
    exit();
}

$result_id = (int)$_POST['result_id'];

try {

    $stmt = $pdo->prepare("SELECT er.*, e.title as exam_title FROM exam_results er LEFT JOIN exams e ON er.exam_id = e.id WHERE er.id = ?");
    $stmt->execute([$result_id]);
    $result = $stmt->fetch();

    if (!$result) {
        echo json_encode(['success' => false, 'message' => 'النتيجة غير موجودة']);
        exit();
    }

    $stmt = $pdo->prepare("DELETE FROM exam_results WHERE id = ?");
    if ($stmt->execute([$result_id])) {

        logActivity($_SESSION['user_id'], 'delete_result', "حذف نتيجة: {$result['student_name']} - {$result['exam_title']}");

        echo json_encode([
            'success' => true, 
            'message' => 'تم حذف النتيجة بنجاح'
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'خطأ في حذف النتيجة']);
    }

} catch (PDOException $e) {
    echo json_encode(['success' => false, 'message' => 'خطأ في قاعدة البيانات']);
}
?>