/* تحسينات الموبايل لنظام الامتحانات الإلكترونية */

/* تحسين body للموبايل */
body {
    overflow-x: hidden;
    overflow-y: auto;
}

/* تحسين navbar-brand */
.navbar-brand {
    font-weight: 700;
    font-size: 1.2rem;
    color: #2c3e50 !important;
    line-height: 1.2;
    display: flex;
    align-items: center;
}

.brand-text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* أزرار الموبايل المنفصلة */
.mobile-nav-buttons {
    display: flex !important;
    align-items: center;
    gap: 0.5rem;
    z-index: 1000;
}

.mobile-nav-buttons .btn {
    width: 40px !important;
    height: 40px !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 0 !important;
    border-width: 2px !important;
    transition: all 0.3s ease;
    color: #fff !important;
    border-color: rgba(255, 255, 255, 0.8) !important;
    margin: 0 !important;
}

.mobile-nav-buttons .btn i {
    font-size: 16px;
}

.mobile-nav-buttons .btn-outline-light {
    background-color: rgba(0, 123, 255, 0.2) !important;
    border-color: #007bff !important;
    color: #007bff !important;
}

.mobile-nav-buttons .btn-outline-light:hover {
    background-color: rgba(0, 123, 255, 0.3) !important;
    border-color: #0056b3 !important;
    color: #0056b3 !important;
}

.mobile-nav-buttons .btn-outline-danger {
    background-color: rgba(220, 53, 69, 0.2) !important;
    border-color: #dc3545 !important;
    color: #dc3545 !important;
}

.mobile-nav-buttons .btn-outline-danger:hover {
    background-color: rgba(220, 53, 69, 0.3) !important;
    border-color: #c82333 !important;
    color: #c82333 !important;
}

/* تحسينات للشاشات المتوسطة */
@media (max-width: 991px) {
    .navbar-brand {
        font-size: 1rem;
        max-width: none;
    }
}

/* تحسينات للموبايل */
@media (max-width: 768px) {
    body {
        -webkit-text-size-adjust: 100%;
        -ms-text-size-adjust: 100%;
        touch-action: pan-y;
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
    }
    
    .brand-text {
        max-width: calc(100vw - 140px);
    }
    
    .container {
        padding-left: 15px;
        padding-right: 15px;
    }
    
    .navbar .container {
        display: flex;
        align-items: center;
        justify-content: space-between;
        position: relative;
        gap: 0.5rem;
    }
    
    .navbar-brand {
        order: 1;
        flex: 1;
        margin-right: 0.5rem;
        font-size: 0.9rem;
        max-width: none;
        min-width: 0;
    }
    
    .mobile-nav-buttons {
        order: 2;
        flex-shrink: 0;
        display: flex !important;
        visibility: visible !important;
        opacity: 1 !important;
    }
    
    .navbar-toggler {
        order: 3;
        flex-shrink: 0;
        margin-left: 0.5rem;
        border: none;
        padding: 0.25rem 0.5rem;
        font-size: 1.1rem;
    }
    
    .navbar-toggler:focus {
        box-shadow: none;
    }
    
    .navbar-nav {
        text-align: center;
        padding-top: 1rem;
    }
    
    .nav-link {
        padding: 0.8rem 1rem !important;
        border-bottom: 1px solid rgba(255,255,255,0.1);
    }
    
    .dropdown-menu {
        border: none;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        border-radius: 8px;
        min-width: 200px;
        padding: 0.5rem 0;
        margin-top: 0.5rem;
    }
    
    .dropdown-item {
        padding: 0.8rem 1.2rem;
        font-size: 0.95rem;
        color: #333;
        transition: all 0.2s ease;
    }
    
    .dropdown-item:hover {
        background-color: #f8f9fa;
        color: #007bff;
    }
    
    .dropdown-item i {
        margin-left: 0.5rem;
        width: 16px;
        text-align: center;
    }
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 576px) {
    .container {
        padding-left: 8px;
        padding-right: 8px;
    }
    
    .navbar .container {
        gap: 0.3rem;
    }
    
    .navbar-brand {
        font-size: 0.8rem;
        max-width: none;
        flex: 1;
        margin-right: 0.3rem;
    }
    
    .brand-text {
        max-width: calc(100vw - 120px);
    }
    
    .mobile-nav-buttons {
        gap: 0.2rem;
        display: flex !important;
        visibility: visible !important;
    }
    
    .mobile-nav-buttons .btn {
        width: 32px;
        height: 32px;
        display: flex !important;
        visibility: visible !important;
    }
    
    .mobile-nav-buttons .btn i {
        font-size: 12px;
    }
    
    .navbar-toggler {
        padding: 0.2rem 0.4rem;
        font-size: 1rem;
    }
    
    .dropdown-menu {
        position: fixed !important;
        top: 60px !important;
        left: 10px !important;
        right: 10px !important;
        width: auto !important;
        margin: 0 !important;
        border-radius: 12px;
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }
    
    .dropdown-item {
        padding: 1rem 1.5rem;
        font-size: 1rem;
        text-align: center;
    }
}

/* فورس إظهار الأزرار على الموبايل */
@media screen and (max-width: 991px) {
    .d-lg-none {
        display: flex !important;
    }
    
    .mobile-nav-buttons,
    .mobile-nav-buttons .btn {
        display: flex !important;
        visibility: visible !important;
        opacity: 1 !important;
    }
}

/* منع الزوم على الموبايل */
input, select, textarea {
    font-size: 16px !important;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

/* تحسين اللمس والتمرير */
.nav-link, .btn, .card {
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
}

/* إصلاح مشاكل التمرير على الموبايل */
html, body {
    height: 100%;
    -webkit-overflow-scrolling: touch;
}

.container-fluid, .container {
    touch-action: pan-y;
}

/* منع التمرير الأفقي */
.row {
    margin-left: 0;
    margin-right: 0;
}

.col-md-3, .col-md-4, .col-md-6, .col-6, .col-12 {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
}
