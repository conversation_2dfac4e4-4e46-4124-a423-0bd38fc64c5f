<?php
date_default_timezone_set('Africa/Cairo');
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

if (!isset($_SESSION['exam_data']) || !isset($_SESSION['exam_data']['exam_id']) || !isset($_SESSION['exam_data']['student_id'])) {
    header('Location: index.php?error=' . urlencode('جلسة الامتحان غير صالحة'));
    exit();
}

$exam_link = '';
if (isset($_GET['link'])) {
    $exam_link = $_GET['link'];
} else {

    $path_info = $_SERVER['PATH_INFO'] ?? '';
    if ($path_info && preg_match('~/([a-zA-Z0-9_]+)$~', $path_info, $matches)) {
        $exam_link = $matches[1];
    }

    if (empty($exam_link)) {
        $request_uri = $_SERVER['REQUEST_URI'] ?? '';
        if (preg_match('~/exam_questions\.php/([a-zA-Z0-9_]+)~', $request_uri, $matches)) {
            $exam_link = $matches[1];
        }
    }
}

if (empty($exam_link)) {
    header('Location: index.php?error=' . urlencode('رابط الامتحان غير صالح'));
    exit();
}

$exam_data = $_SESSION['exam_data'];

// تسجيل بيانات الجلسة للتشخيص
error_log("exam_questions.php - Session exam_data: " . json_encode($exam_data));

if (isset($exam_data['attempt_id']) && isset($_SESSION['question_order'][$exam_data['exam_id']])) {
    $last_attempt_id = $_SESSION['question_order_last_attempt'][$exam_data['exam_id']] ?? null;
    if ($last_attempt_id !== $exam_data['attempt_id']) {
        unset($_SESSION['question_order'][$exam_data['exam_id']]);
    }

    $_SESSION['question_order_last_attempt'][$exam_data['exam_id']] = $exam_data['attempt_id'];
}

$error = '';
$exam = null;
$questions = [];
$current_index = isset($_GET['q']) ? (int)$_GET['q'] : 0;

try {
    $pdo->beginTransaction();

    $stmt = $pdo->prepare("
        SELECT e.*, er.start_time, er.end_time, er.id as attempt_id
        FROM exams e
        JOIN exam_results er ON e.id = er.exam_id
        WHERE e.id = ?
        AND e.status = 'active'
        AND er.student_id = ?
        AND er.id = ?
        AND er.end_time IS NULL
    ");

    $stmt->execute([
        $exam_data['exam_id'],
        $exam_data['student_id'],
        $exam_data['attempt_id']
    ]);

    $exam = $stmt->fetch();

    if (!$exam) {
        throw new Exception('الامتحان غير متاح أو انتهت صلاحيته');
    }

    // التأكد من أن start_time صحيح
    if (empty($exam['start_time'])) {
        error_log("exam_questions.php - ERROR: start_time is empty!");
        throw new Exception('خطأ في بيانات الامتحان - وقت البداية غير محدد');
    }

    // حل مؤقت: إعطاء الوقت الكامل للامتحان في كل مرة
    $duration_seconds = $exam['duration_minutes'] * 60;

    // التحقق من وجود وقت بداية صحيح
    if (!empty($exam['start_time'])) {
        $start_time = new DateTime($exam['start_time']);
        $current_time = new DateTime();
        $time_elapsed = $current_time->getTimestamp() - $start_time->getTimestamp();
        $time_remaining = $duration_seconds - $time_elapsed;

        // إذا كان الوقت المنقضي قصير جداً أو سالب، أعطي الوقت الكامل
        if ($time_elapsed < 30 || $time_remaining <= 0) {
            error_log("exam_questions.php - Suspicious time calculation, giving full duration");
            $time_remaining = $duration_seconds;

            // تحديث وقت البداية في قاعدة البيانات
            $stmt = $pdo->prepare("UPDATE exam_results SET start_time = NOW() WHERE id = ?");
            $stmt->execute([$exam_data['attempt_id']]);
        }
    } else {
        // إذا لم يكن هناك وقت بداية، أعطي الوقت الكامل
        $time_remaining = $duration_seconds;
        $time_elapsed = 0;

        // تحديث وقت البداية في قاعدة البيانات
        $stmt = $pdo->prepare("UPDATE exam_results SET start_time = NOW() WHERE id = ?");
        $stmt->execute([$exam_data['attempt_id']]);
    }

    // تسجيل للتشخيص
    error_log("exam_questions.php - Duration minutes: " . $exam['duration_minutes']);
    error_log("exam_questions.php - Duration seconds: " . $duration_seconds);
    error_log("exam_questions.php - Time remaining: " . $time_remaining . " seconds (" . round($time_remaining/60, 1) . " minutes)");

    // حماية من الوقت السالب - إعطاء الوقت الكامل إذا كان هناك مشكلة
    if ($time_remaining <= 0) {
        error_log("exam_questions.php - WARNING: Time remaining is negative or zero, resetting to full duration");
        $time_remaining = $duration_seconds;

        // تحديث وقت البداية في قاعدة البيانات
        $stmt = $pdo->prepare("UPDATE exam_results SET start_time = NOW() WHERE id = ?");
        $stmt->execute([$exam_data['attempt_id']]);
    }

    // التأكد من أن الوقت المتبقي معقول (على الأقل دقيقة واحدة)
    if ($time_remaining < 60) {
        error_log("exam_questions.php - Time remaining too short (" . $time_remaining . "s), setting to 5 minutes");
        $time_remaining = 300; // 5 دقائق
    }


    $selected_questions_key = 'selected_questions_' . $exam['id'] . '_' . $exam_data['attempt_id'];

    if (isset($_SESSION[$selected_questions_key])) {

        $question_ids = $_SESSION[$selected_questions_key];

        if (empty($question_ids)) {
            throw new Exception('لا توجد أسئلة محددة لهذا الامتحان');
        }

        $placeholders = str_repeat('?,', count($question_ids) - 1) . '?';

        $stmt = $pdo->prepare("
            SELECT q.*, eq.question_order
            FROM questions q
            JOIN exam_questions eq ON q.id = eq.question_id
            WHERE q.id IN ($placeholders)
            ORDER BY FIELD(q.id, $placeholders)
        ");

        $params = array_merge($question_ids, $question_ids);
        $stmt->execute($params);
        $questions = $stmt->fetchAll();

        if (empty($questions)) {
            throw new Exception('لا توجد أسئلة متاحة لهذا الامتحان');
        }
    } else {

        $stmt = $pdo->prepare("
            SELECT q.*, eq.question_order
            FROM questions q
            JOIN exam_questions eq ON q.id = eq.question_id
            WHERE eq.exam_id = ? AND q.status = 'active'
            " . ($exam['randomize_questions'] ? 'ORDER BY RAND()' : 'ORDER BY eq.question_order') . "
            LIMIT ?
        ");
        $stmt->execute([$exam['id'], $exam['questions_count']]);
        $questions = $stmt->fetchAll();

        if (empty($questions)) {
            error_log("exam_questions.php - No questions found for exam ID: " . $exam['id']);
            throw new Exception('لا توجد أسئلة متاحة لهذا الامتحان');
        }

        $_SESSION[$selected_questions_key] = array_column($questions, 'id');
        error_log("exam_questions.php - Selected " . count($questions) . " questions for exam");
    }

    $pdo->commit();

} catch (Exception $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    error_log("Error in exam_questions.php: " . $e->getMessage());
    header('Location: index.php?error=' . urlencode($e->getMessage()));
    exit();
}

$student_answers = [];
$answer_status = [];
if (!$error) {
    try {
        $stmt = $pdo->prepare("
            SELECT question_id, selected_answer
            FROM student_answers
            WHERE exam_id = ? AND student_id = ? AND attempt_id = ?
        ");
        $stmt->execute([$exam_data['exam_id'], $exam_data['student_id'], $exam_data['attempt_id']]);
        $answers = $stmt->fetchAll(PDO::FETCH_ASSOC);
        foreach ($answers as $answer) {
            $student_answers[$answer['question_id']] = $answer['selected_answer'];

        }
    } catch (PDOException $e) {
        error_log("Error fetching student answers: " . $e->getMessage());
    }
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['question_id'], $_POST['answer'])) {
    $question_id = (int)$_POST['question_id'];
    $answer = $_POST['answer'];
    if (!in_array($answer, ['a','b','c','d'])) {
        $error = 'إجابة غير صحيحة';
    } else {
        try {

            $stmt = $pdo->prepare("SELECT correct_answer FROM questions WHERE id = ?");
            $stmt->execute([$question_id]);
            $question_data = $stmt->fetch();

            if ($question_data) {
                $is_correct = ($answer === $question_data['correct_answer']);

                $stmt = $pdo->prepare("UPDATE student_answers
                    SET selected_answer = ?
                    WHERE exam_id = ? AND student_id = ? AND question_id = ? AND attempt_id = ?");
                $stmt->execute([
                    $answer,
                    $exam_data['exam_id'],
                    $exam_data['student_id'],
                    $question_id,
                    $exam_data['attempt_id']
                ]);

                $student_answers[$question_id] = $answer;

                $next = $current_index + 1;
                if ($next < count($questions)) {
                    $base_url = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://" . $_SERVER['HTTP_HOST'];
                    header('Location: ' . $base_url . '/exam_questions.php/' . urlencode($exam_link) . '?q=' . $next);
                    exit();
                } else {
                    $base_url = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://" . $_SERVER['HTTP_HOST'];
                    header('Location: ' . $base_url . '/exam_questions.php/' . urlencode($exam_link) . '?q=0');
                    exit();
                }
            } else {
                $error = 'لم يتم العثور على السؤال';
            }
        } catch (PDOException $e) {
            error_log("Error saving answer: " . $e->getMessage());
            $error = 'حدث خطأ أثناء حفظ الإجابة';
        }
    }
}

$q = $questions[$current_index] ?? null;
if (!$q) {
    $error = 'السؤال غير موجود';
}

// حساب وقت انتهاء الامتحان بناءً على الوقت المتبقي الفعلي
$current_time = new DateTime();

// التحقق من وجود وقت انتهاء محفوظ في الجلسة
$session_end_time_key = 'exam_end_time_' . $exam_data['exam_id'] . '_' . $exam_data['student_id'] . '_' . $exam_data['attempt_id'];

if (isset($_SESSION[$session_end_time_key])) {
    // استخدام الوقت المحفوظ
    $exam_end_time = $_SESSION[$session_end_time_key];
    error_log("exam_questions.php - Using saved exam end time: " . date('Y-m-d H:i:s', $exam_end_time));
} else {
    // حساب وقت انتهاء جديد وحفظه
    if ($time_remaining <= 0) {
        $time_remaining = 300; // 5 دقائق كحد أدنى
        error_log("exam_questions.php - WARNING: Time remaining was negative, set to 5 minutes");
    }

    $exam_end_time = $current_time->getTimestamp() + $time_remaining;
    $_SESSION[$session_end_time_key] = $exam_end_time;
    error_log("exam_questions.php - Calculated and saved new exam end time: " . date('Y-m-d H:i:s', $exam_end_time));
}

// حساب الوقت المتبقي الفعلي
$actual_time_remaining = $exam_end_time - $current_time->getTimestamp();

error_log("exam_questions.php - Exam end time: " . date('Y-m-d H:i:s', $exam_end_time));
error_log("exam_questions.php - Actual time remaining: " . $actual_time_remaining . " seconds (" . round($actual_time_remaining/60, 1) . " minutes)");

// التأكد من أن الوقت لم ينته
if ($actual_time_remaining <= 0) {
    // الوقت انتهى - توجيه لصفحة النتائج
    unset($_SESSION[$session_end_time_key]); // حذف الوقت المحفوظ
    header('Location: exam_result.php');
    exit();
}

if (!isset($_SESSION['visited_questions']) || !is_array($_SESSION['visited_questions'])) {
    $_SESSION['visited_questions'] = [];
}
if (!in_array($current_index, $_SESSION['visited_questions'])) {
    $_SESSION['visited_questions'][] = $current_index;
}

$total_points = 0;
$total_questions = 0;
try {
    if (isset($_SESSION['question_order'][$exam['id']])) {
        $question_ids = $_SESSION['question_order'][$exam['id']];
        if (count($question_ids) > 0) {
            $placeholders = str_repeat('?,', count($question_ids) - 1) . '?';
            $stmt = $pdo->prepare("
                SELECT id, points
                FROM questions
                WHERE id IN ($placeholders)
            ");
            $stmt->execute($question_ids);
            $questions_all = $stmt->fetchAll();
            $total_questions = count($questions_all);
            foreach ($questions_all as $question) {
                $total_points += $question['points'];
            }
        }
    }
} catch (Exception $e) {
    $total_points = 0;
    $total_questions = 0;
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($exam['title'] ?? 'امتحان'); ?> - جامعة المنيا</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;500;600&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #e3f0ff 0%, #f6fafd 100%);
            font-family: 'Cairo', Tahoma, Arial, sans-serif;
            min-height: 100vh;
        }
        .header-bar {
            background: #003366;
        color: #fff;
            font-size: 1.3rem;
        font-weight: bold;
        text-align: center;
            padding: 0.8rem 0 0.6rem 0;
        letter-spacing: 1px;
            border-bottom: 3px solid #e3e6ea;
            box-shadow: 0 2px 8px #00336622;
    }
    .exam-flex {
        display: flex;
        min-height: 90vh;
        gap: 0;
    }
        .sidebar {
        background: #fff;
        border-left: 1px solid #e3e6ea;
            width: 280px;
            min-width: 240px;
            max-width: 300px;
        display: flex;
        flex-direction: column;
        align-items: center;
            padding: 1.5rem 1rem 1rem 1rem;
            box-shadow: 2px 0 12px rgba(0,0,0,0.07);
            border-radius: 0 0 18px 0;
    }
        .timer-outer {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 1.5rem;
        }
        .timer-label {
            color: #003366;
            font-size: 1.05rem;
            font-weight: 700;
            margin-bottom: 0.2rem;
        }
        .timer {
            font-size: 1.8rem;
            font-weight: 900;
            color: #00796b;
            background: #e0f7fa;
            border-radius: 12px;
            padding: 0.8rem 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 12px #00796b22;
            border: 2px solid #b2ebf2;
            margin-bottom: 0.5rem;
            transition: all 0.3s ease;
            min-width: 120px;
        }
        .timer.warning {
            color: #ff9800 !important;
            background: #fff3e0;
            border-color: #ffcc02;
            animation: pulse 1s infinite;
        }
        .timer.danger {
            color: #e74c3c !important;
            background: #fdecea;
            border-color: #f5c6cb;
            animation: pulse 0.5s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        .user-info {
        text-align: center;
            margin-bottom: 1.3rem;
    }
        .user-info .avatar {
            width: 64px; height: 64px; border-radius: 50%; background: #e3f0ff; color: #003366; display: inline-flex; align-items: center; justify-content: center; font-size: 2.3rem; margin-bottom: 0.3rem; box-shadow: 0 2px 8px #00336622;
    }
        .user-info .name {
            font-size: 1.13rem;
            font-weight: bold;
            color: #003366;
        }
        .user-info .id {
            font-size: 0.98rem;
            color: #888;
        }
        .stats {
        background: #f3f6fa;
            border-radius: 12px;
            padding: 0.8rem 1.1rem;
            font-size: 1.07rem;
        border: 1px solid #e3e6ea;
            margin-bottom: 1.3rem;
            width: 100%;
            box-shadow: 0 2px 8px #00336611;
    }
        .stats .stat-label { color: #888; font-size: 0.97em; }
        .stats .stat-value { font-weight: bold; font-size: 1.1em; }
        .question-grid {
        width: 100%;
        margin-bottom: 1.2rem;
        max-height: 260px;
        overflow-y: auto;
        padding-bottom: 0.5rem;
            display: flex;
            flex-wrap: wrap;
            gap: 2px;
            justify-content: flex-start;
            direction: ltr;
    }
        .question-grid .q-btn {
            border-radius: 6px !important;
        font-weight: bold;
            font-size: 1rem;
            min-width: 32px;
            min-height: 32px;
        margin: 2px;
        border: 2px solid #e3e6ea !important;
            background: #f6fafd;
            color: #003366;
        transition: all 0.2s;
            box-shadow: 0 1px 4px #00336611;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            direction: ltr;
        }
        .question-grid .q-btn.active {
            background: #1565c0 !important;
            color: #fff !important;
            border-color: #1565c0 !important;
            box-shadow: 0 2px 8px #1565c022;
    }
        .question-grid .q-btn.answered {
            background: #43a047 !important;
            color: #fff !important;
            border-color: #43a047 !important;
            box-shadow: 0 2px 8px #43a04722;
        }
        .question-grid .q-btn.active-unanswered {
        background: #e74c3c !important;
        color: #fff !important;
        border-color: #e74c3c !important;
            box-shadow: 0 2px 8px #e74c3c22;
    }
        .end-btn {
        background: #e74c3c !important;
        border-color: #e74c3c !important;
        font-size: 1.13rem;
        font-weight: 700;
        letter-spacing: 1px;
        width: 100%;
        margin-top: 1.2rem;
        padding: 0.8rem 0;
            border-radius: 12px;
            box-shadow: 0 2px 8px #e74c3c22;
            transition: background 0.2s;
    }
        .end-btn:hover, .end-btn:focus {
        background: #c0392b !important;
        border-color: #c0392b !important;
    }
        .main-content {
        flex: 1;
            background: transparent;
            padding: 2.5rem 2.5rem 1.5rem 2.5rem;
        min-width: 0;
        display: flex;
            flex-direction: column;
            align-items: center;
    }
        .question-box {
        background: #fff;
            border-radius: 18px;
            box-shadow: 0 4px 24px #00336622;
        border: 1.5px solid #e3e6ea;
            padding: 2rem 2rem 2rem 2rem;
            min-width: 320px;
            max-width: 650px;
            width: 100%;
            margin-bottom: 1.5rem;
            position: relative;
            transition: box-shadow 0.2s, border 0.2s;
    }
        .question-box .question-title {
            font-size: 1.2rem;
        font-weight: bold;
            margin-bottom: 1rem;
            color: #1565c0;
            letter-spacing: 0.5px;
            display: flex;
            align-items: center;
            gap: 0.7em;
        }
        .question-box .question-title i {
            color: #43a047;
            font-size: 1.2em;
    }
        .question-box .question-text {
            font-size: 1.1rem;
            margin-bottom: 1.5rem;
            color: #222;
            font-weight: 500;
            line-height: 1.6;
            background: #f6fafd;
            border-radius: 8px;
            padding: 1rem 1rem 1rem 1rem;
            box-shadow: 0 2px 8px #1565c011;
            direction: ltr;
            text-align: left;
        }
        .answer-options {
            display: flex;
            flex-direction: column;
            gap: 1.1rem;
            direction: ltr;
        }
        .answer-options .form-check {
            background: #fafdff;
            border-radius: 10px;
            box-shadow: 0 2px 8px #1565c011;
            border: 1.5px solid #e3e6ea;
            padding: 0.9rem 1rem;
            transition: border 0.2s, box-shadow 0.2s, background 0.2s;
            display: flex;
            align-items: center;
            min-height: 48px;
            direction: ltr;
            text-align: left;
        }
        .answer-options .form-check-input {
            margin-right: 1.1rem;
            margin-left: 0;
            width: 1.3em;
            height: 1.3em;
            border-radius: 50%;
            border: 2px solid #1565c0;
            transition: border 0.2s, box-shadow 0.2s;
    }
    .answer-options .form-check-input:checked {
            background-color: #43a047;
            border-color: #43a047;
            box-shadow: 0 0 0 4px #43a04722;
    }
    .answer-options .form-check-label {
            font-size: 1.05rem;
        font-weight: 600;
            color: #003366;
            cursor: pointer;
            text-align: left;
        }
        .answer-options .form-check:hover, .answer-options .form-check-input:focus + .form-check-label {
            border-color: #1565c0;
            background: #e3f0ff;
        }
        .answer-options .form-check-input:focus {
            box-shadow: 0 0 0 3px #1565c033;
    }
        .save-btn {
            background: #1565c0 !important;
            border-color: #1565c0 !important;
        color: #fff !important;
        font-size: 1.13rem;
        font-weight: 700;
        letter-spacing: 1px;
        width: 180px;
        padding: 0.7rem 0;
        border-radius: 8px;
        margin: 0 auto;
        display: block;
            box-shadow: 0 2px 8px #1565c022;
            transition: background 0.2s;
    }
        .save-btn:disabled {
        background: #bdbdbd !important;
        border-color: #bdbdbd !important;
        color: #fff !important;
    }
        .d-flex.justify-content-between.mt-4.gap-2 {
            gap: 1.2rem !important;
        }
        .btn-outline-info.px-4 {
            font-size: 1.08rem;
            font-weight: 600;
            border-radius: 8px;
            padding: 0.6rem 2.2rem !important;
            color: #1565c0 !important;
            border-color: #1565c0 !important;
            background: #e3f0ff !important;
            transition: background 0.2s, color 0.2s;
        }
        .btn-outline-info.px-4:hover, .btn-outline-info.px-4:focus {
            background: #1565c0 !important;
            color: #fff !important;
        }
        .alert-danger, .alert-success {
            font-size: 1.08rem;
            border-radius: 8px;
    }
    @media (max-width: 991px) {
        .exam-flex { flex-direction: column; }
            .sidebar, .main-content { border-radius: 0 !important; width: 100% !important; max-width: 100% !important; min-width: 0 !important; }
            .main-content { padding: 1rem 0.5rem; }
            .question-box { min-width: 0; max-width: 100%; }
        }
        @media (max-width: 600px) {
            .sidebar { padding: 1.2rem 0.3rem; }
            .question-box { padding: 1.2rem 0.5rem; }
    }

    .q-btn.correct-answer {
        background: #4caf50 !important;
        color: #fff !important;
        border-color: #4caf50 !important;
        box-shadow: 0 2px 8px #4caf5022;
    }

    .q-btn.wrong-answer {
        background: #f44336 !important;
        color: #fff !important;
        border-color: #f44336 !important;
        box-shadow: 0 2px 8px #f4433622;
    }

    .q-btn.correct-answer:hover,
    .q-btn.wrong-answer:hover {
        opacity: 0.9;
        transform: scale(1.05);
        transition: all 0.2s ease;
    }

    .q-btn {
        transition: all 0.3s ease;
    }

    .q-btn.active {
        background: #1565c0 !important;
        color: #fff !important;
        border-color: #1565c0 !important;
        box-shadow: 0 2px 8px #1565c022;
    }

    .q-btn.active-unanswered {
        background: #fff !important;
        color: #1565c0 !important;
        border-color: #1565c0 !important;
        box-shadow: 0 2px 8px #1565c022;
    }

    .q-btn.answered {
        background: #4caf50 !important;
        color: #fff !important;
        border-color: #4caf50 !important;
        box-shadow: 0 2px 8px #4caf5022;
    }

    .q-btn.wrong-answer {
        background: #f44336 !important;
        color: #fff !important;
        border-color: #f44336 !important;
        box-shadow: 0 2px 8px #f4433622;
    }
    </style>
</head>
<body>
    <div class="header-bar" style="padding: 0.7rem 0 0.4rem 0;">
        <div style="font-size:1.08em;font-weight:700;letter-spacing:0.5px;line-height:1.2;">
            <i class="fas fa-graduation-cap ms-2" style="font-size:1.1em;"></i>
            جامعة المنيا - قسم الـ Forensic
        </div>
    </div>
    <div class="exam-flex">
        <!-- Sidebar -->
        <aside class="sidebar">
            <!-- المؤقت -->
            <div class="timer-outer">
                <div class="timer-label">الوقت المتبقي</div>
                <div class="timer" id="timer">--:--</div>
                <!-- الوقت محفوظ في الجلسة ولا يعيد العد عند تحديث الصفحة -->
            </div>

            <!-- بطاقة بيانات الطالب -->
            <div class="card shadow-sm mb-3" style="width:100%;border-radius:16px;background:#f8fbff;border:1.5px solid #e3e6ea;">
                <div class="card-body d-flex flex-column align-items-center p-3">
                    <div class="avatar mb-2" style="width:60px;height:60px;border-radius:50%;background:#e3f0ff;color:#003366;display:flex;align-items:center;justify-content:center;font-size:2.2rem;box-shadow:0 2px 8px #00336622;">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="name mb-1" style="font-size:1.08rem;font-weight:700;color:#003366;"> <?php echo htmlspecialchars($exam_data['student_name']); ?> </div>
                    <div class="id mb-1" style="font-size:0.97rem;color:#888;">رقم الطالب: <?php echo htmlspecialchars($exam_data['student_id']); ?></div>
                    <div class="section mb-1" style="font-size:0.97rem;color:#1565c0;font-weight:600;">السكشن: <?php echo htmlspecialchars($exam_data['section_number']); ?></div>
                </div>
            </div>
            <!-- شبكة أرقام الأسئلة داخل بطاقة -->
            <div class="card shadow-sm mb-3" style="width:100%;border-radius:16px;background:#fafdff;border:1.5px solid #e3e6ea;">
                <div class="card-body p-2" style="padding-bottom:0.5rem !important;">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <button type="button" class="btn btn-light btn-sm" onclick="scrollQuestions(-1)" style="border-radius:8px;box-shadow:0 1px 4px #00336611;"><i class="fas fa-chevron-up"></i></button>
                        <span style="font-size:1.01em;font-weight:600;color:#1565c0;">الأسئلة</span>
                        <button type="button" class="btn btn-light btn-sm" onclick="scrollQuestions(1)" style="border-radius:8px;box-shadow:0 1px 4px #00336611;"><i class="fas fa-chevron-down"></i></button>
                    </div>
                    <div class="question-grid mb-2" id="question-grid" style="max-height:180px;overflow-y:auto;">
                        <?php foreach ($questions as $i => $qq): ?>
                            <?php
                                $btn_class = '';
                                $question_id = $qq['id'] ?? 0;
                                $ans = isset($student_answers[$question_id]) ? $student_answers[$question_id] : '';

                                if ($i == $current_index) {
                                    $btn_class = 'active';
                                } elseif (in_array($ans, ['a','b','c','d'])) {
                                    $btn_class = 'answered';
                                } elseif (in_array($i, $_SESSION['visited_questions']) && !in_array($ans, ['a','b','c','d'])) {
                                    $btn_class = 'active-unanswered';
                                } else {
                                    $btn_class = '';
                                }
                            ?>
                            <a href="/exam_questions.php/<?php echo urlencode($exam_link); ?>?q=<?php echo $i; ?>"
                               class="q-btn <?php echo $btn_class; ?>"
                               title="سؤال <?php echo $i+1; ?><?php echo ''; ?>">
                                <?php echo $i+1; ?>
                            </a>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
            <?php

                $answered_count = 0;
                $total_questions = count($questions);
                foreach ($questions as $qq) {
                    $ans = isset($student_answers[$qq['id']]) ? $student_answers[$qq['id']] : '';
                    if (in_array($ans, ['a','b','c','d'])) {
                        $answered_count++;
                    }
                }
                $remaining_count = $total_questions - $answered_count;
                $current_num = $current_index + 1;
            ?>
            <?php

                $not_seen_count = 0;
                for ($i = 0; $i < $total_questions; $i++) {
                    if (!in_array($i, $_SESSION['visited_questions'])) {
                        $not_seen_count++;
                    }
                }
            ?>
            <!-- بطاقة إرشادات ألوان مربعات الأسئلة -->
            <div class="card shadow-sm mb-3" style="width:100%;border-radius:16px;background:#fafdff;border:1.5px solid #e3e6ea;">
                <div class="card-body py-2 px-3">
                    <div class="d-flex flex-wrap" style="gap: 0.8rem;">
                        <div class="d-flex align-items-center justify-content-end" style="flex-basis: calc(50% - 0.4rem);">
                            <span style="color:#43a047;font-weight:600;font-size:0.85em;margin-left:8px;">تمت الإجابة</span>
                            <span style="display:inline-block;width:30px;height:30px;background:#43a047;border-radius:8px;color:#fff;text-align:center;line-height:28px;font-weight:700;font-size:0.95em;border:2px solid #43a047;">
                                <?php echo $answered_count; ?>
                            </span>
                        </div>
                        <div class="d-flex align-items-center justify-content-end" style="flex-basis: calc(50% - 0.4rem);">
                            <span style="color:#e74c3c;font-weight:600;font-size:0.85em;margin-left:8px;">لم يجب بعد</span>
                            <span style="display:inline-block;width:30px;height:30px;background:#e74c3c;border-radius:8px;color:#fff;text-align:center;line-height:28px;font-weight:700;font-size:0.95em;border:2px solid #e74c3c;">
                                <?php echo $exam['questions_count'] - $answered_count; ?>
                            </span>
                        </div>
                        <div class="d-flex align-items-center justify-content-end" style="flex-basis: calc(50% - 0.4rem);">
                            <span style="color:#888;font-weight:600;font-size:0.85em;margin-left:8px;">غير مشاهدة</span>
                            <span style="display:inline-block;width:30px;height:30px;background:#fff;border-radius:8px;color:#888;text-align:center;line-height:28px;font-weight:700;font-size:0.95em;border:2px solid #e3e6ea;">
                                <?php echo $not_seen_count; ?>
                            </span>
                        </div>
                        <div class="d-flex align-items-center justify-content-end" style="flex-basis: calc(50% - 0.4rem);">
                            <span style="color:#2196f3;font-weight:600;font-size:0.85em;margin-left:8px;">إجمالي الأسئلة</span>
                            <span style="display:inline-block;width:30px;height:30px;background:#2196f3;border-radius:8px;color:#fff;text-align:center;line-height:28px;font-weight:700;font-size:0.95em;border:2px solid #2196f3;">
                                <?php echo $exam['questions_count']; ?>
                            </span>
                        </div>

<?php
$stmt = $pdo->prepare("SELECT COUNT(*) as attempts FROM exam_results WHERE exam_id = ? AND student_id = ? AND end_time IS NOT NULL");
$stmt->execute([$exam_data['exam_id'], $exam_data['student_id']]);
$attempts = $stmt->fetch();

// حساب المحاولات المتبقية
$total_allowed_entries = $exam['max_attempts'] + 1; // المرة الأساسية + المحاولات الإضافية
$remaining_attempts = $total_allowed_entries - $attempts['attempts'] - 1; // -1 لأن الطالب يحل الآن

if ($exam['max_attempts'] == 0) {
    $remaining_attempts_text = '0';
    $remaining_attempts = 0; // لا توجد محاولات إضافية
} else {
    $remaining_attempts_text = max(0, $remaining_attempts);
}
?>
<div class="d-flex align-items-center justify-content-end" style="flex-basis: calc(50% - 0.4rem);">
    <span style="color:#673ab7;font-weight:600;font-size:0.85em;margin-left:8px;">المحاولات المتبقية</span>
    <span style="display:inline-block;width:30px;height:30px;background:<?php echo $remaining_attempts <= 0 ? '#e74c3c' : '#673ab7'; ?>;border-radius:8px;color:#fff;text-align:center;line-height:28px;font-weight:700;font-size:0.95em;border:2px solid <?php echo $remaining_attempts <= 0 ? '#e74c3c' : '#673ab7'; ?>;">
        <?php echo $remaining_attempts_text; ?>
    </span>
</div>

<!-- زر إنهاء الامتحان -->
<button type="button" class="btn btn-danger mt-3 w-100" style="border-radius:12px;" data-bs-toggle="modal" data-bs-target="#endExamModal">
    <i class="fas fa-stop-circle me-2"></i>
    إنهاء الامتحان
</button>

                    </div>
                </div>
            </div>
            <!-- إحصائيات دقيقة (تم نقلها للهيدر الثانوي) -->
            <!-- تم حذفها من الشريط الجانبي -->
            <script>
                function scrollQuestions(dir) {
                    var grid = document.getElementById('question-grid');
                    var amount = 60;
                    grid.scrollBy({ top: dir * amount, behavior: 'smooth' });
                }
            </script>

            <!-- مودال تأكيد إنهاء الاختبار -->
            <div class="modal fade" id="endExamModal" tabindex="-1" aria-labelledby="endExamModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content" style="border-radius:16px;">
                        <div class="modal-header" style="border-bottom:none;">
                            <h5 class="modal-title" id="endExamModalLabel" style="color:#e74c3c;font-weight:700;">تأكيد إنهاء الاختبار</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
                        </div>
                        <div class="modal-body" style="font-size:1.08em;color:#333;">
                            <div style="margin-bottom:1em;">هل أنت متأكد أنك تريد إنهاء الاختبار؟<br>لن تتمكن من العودة مرة أخرى.</div>
                            <!-- تلخيص العدادات -->
                            <div style="display:flex;gap:1.2em;justify-content:center;align-items:center;">
                                <span style="display:flex;align-items:center;gap:0.3em;">
                                    <span style="color:#43a047;font-weight:500;">تمت الإجابة</span>
                                    <span style="display:inline-block;width:26px;height:26px;background:#43a047;border-radius:6px;color:#fff;text-align:center;line-height:26px;font-weight:700;font-size:1em;border:2px solid #43a047;">
                                        <?php echo $answered_count; ?>
                                    </span>
                                </span>
                                <span style="display:flex;align-items:center;gap:0.3em;">
                                    <span style="color:#e74c3c;font-weight:500;">لم يُجب بعد</span>
                                    <span style="display:inline-block;width:26px;height:26px;background:#e74c3c;border-radius:6px;color:#fff;text-align:center;line-height:26px;font-weight:700;font-size:1em;border:2px solid #e74c3c;">
                                        <?php echo $exam['questions_count'] - $answered_count; ?>
                                    </span>
                                </span>
                                <span style="display:flex;align-items:center;gap:0.3em;">
                                    <span style="color:#888;font-weight:500;">غير مشاهدة</span>
                                    <span style="display:inline-block;width:26px;height:26px;background:#fff;border-radius:6px;color:#888;text-align:center;line-height:26px;font-weight:700;font-size:1em;border:2px solid #e3e6ea;">
                                        <?php echo $not_seen_count; ?>
                                    </span>
                                </span>
                            </div>
                        </div>
                        <div class="modal-footer" style="border-top:none;gap:0.7em;">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-danger" onclick="submitExam()">تأكيد الإنهاء</button>
                        </div>
                    </div>
                </div>
            </div>
            <script>
                document.addEventListener('DOMContentLoaded', function() {
                    var endBtn = document.getElementById('endExamBtn');
                    var endModal = new bootstrap.Modal(document.getElementById('endExamModal'));
                    var confirmBtn = document.getElementById('confirmEndExam');
                    if (endBtn) {
                        endBtn.addEventListener('click', function(e) {
                            e.preventDefault();
                            endModal.show();
                        });
                    }
                });
            </script>
        </aside>
        <!-- Main Content -->
        <main class="main-content">
            <!-- هيدر الإحصائيات الجديد (تم حذفه بناءً على طلب المستخدم) -->
            <div class="question-box">
                <?php if($error): ?>
                    <div class="alert alert-danger"><?php echo $error; ?></div>
                <?php else: ?>
                    <div style="display:flex;align-items:center;gap:1.2em;font-size:0.98em;font-weight:500;color:#1565c0;opacity:0.85;margin-bottom:0.5em;">
                        <span><?php echo htmlspecialchars($exam['title'] ?? 'امتحان'); ?></span>
                        <!-- تم حذف المؤقت -->
            </div>
                    <div class="question-title" id="questionTitle"><i class="fa-solid fa-circle-question ms-2"></i> سؤال <?php echo $current_index+1; ?> من <?php echo count($questions); ?></div>
                    <div class="question-text" id="questionText"><?php echo htmlspecialchars($q['question_text']); ?></div>
                    <form method="POST" class="answer-options" id="answerForm">
                        <input type="hidden" name="question_id" value="<?php echo $q['id']; ?>">
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="radio" name="answer" id="answer_a" value="a"
                                   <?php echo (isset($student_answers[$q['id']]) && $student_answers[$q['id']] === 'a') ? 'checked' : ''; ?> required>
                            <label class="form-check-label" for="answer_a" id="label_a"><b>أ)</b> <?php echo htmlspecialchars($q['option_a']); ?></label>
                        </div>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="radio" name="answer" id="answer_b" value="b"
                                   <?php echo (isset($student_answers[$q['id']]) && $student_answers[$q['id']] === 'b') ? 'checked' : ''; ?> required>
                            <label class="form-check-label" for="answer_b" id="label_b"><b>ب)</b> <?php echo htmlspecialchars($q['option_b']); ?></label>
                        </div>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="radio" name="answer" id="answer_c" value="c"
                                   <?php echo (isset($student_answers[$q['id']]) && $student_answers[$q['id']] === 'c') ? 'checked' : ''; ?> required>
                            <label class="form-check-label" for="answer_c" id="label_c"><b>ج)</b> <?php echo htmlspecialchars($q['option_c']); ?></label>
                        </div>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="radio" name="answer" id="answer_d" value="d"
                                   <?php echo (isset($student_answers[$q['id']]) && $student_answers[$q['id']] === 'd') ? 'checked' : ''; ?> required>
                            <label class="form-check-label" for="answer_d" id="label_d"><b>د)</b> <?php echo htmlspecialchars($q['option_d']); ?></label>
                        </div>
                        <div class="d-flex justify-content-between mt-4 gap-2">
                            <button type="button" class="btn btn-outline-info px-4" id="prevButton" onclick="goToPreviousQuestion()" <?php if($current_index==0) echo 'disabled'; ?>><i class="fas fa-arrow-right ms-1"></i> السابق</button>
                            <button type="submit" class="btn save-btn">حفظ و التالي <i class="fas fa-arrow-left me-1"></i></button>
            </div>
                        <div id="answerAlert" class="alert alert-danger mt-3 d-none" role="alert">يرجى اختيار إجابة قبل المتابعة.</div>
                    </form>
                    <script>

                        function updateQuestionCircle(questionIndex, answer) {
                            const questionBtn = document.querySelectorAll('.q-btn')[questionIndex];
                            if (questionBtn) {

                                questionBtn.classList.remove('active', 'answered', 'active-unanswered', 'correct-answer', 'wrong-answer');

                                if (answer && ['a', 'b', 'c', 'd'].includes(answer)) {
                                    questionBtn.classList.add('answered');
                                } else {
                                    questionBtn.classList.add('active-unanswered');
                                }
                            }
                        }

                        function updateAnswerCounters() {
                            const answeredCounter = document.querySelector('.answered-counter');
                            const unansweredCounter = document.querySelector('.unanswered-counter');
                            const totalQuestions = <?php echo count($questions); ?>;

                            const answeredQuestions = document.querySelectorAll('.q-btn.answered').length;

                            if (answeredCounter) {
                                answeredCounter.textContent = answeredQuestions;
                            }
                            if (unansweredCounter) {
                                unansweredCounter.textContent = totalQuestions - answeredQuestions;
                            }
                        }

                        function goToPreviousQuestion() {
                            const currentIndex = parseInt(new URLSearchParams(window.location.search).get('q') || '0');
                            const prevIndex = Math.max(0, currentIndex - 1);

                            const currentAnswer = document.querySelector('input[name="answer"]:checked');
                            const currentQuestionId = document.querySelector('input[name="question_id"]')?.value;

                            if (currentAnswer && currentQuestionId) {
                                const formData = new FormData();
                                formData.append('question_id', currentQuestionId);
                                formData.append('answer', currentAnswer.value);

                                saveAnswerWithFallback(formData)
                                .then(data => {
                                    // تم حفظ الإجابة
                                })
                                .catch(error => {
                                    // خطأ في حفظ الإجابة
                                });
                            }

                            window.location.href = `/exam_questions.php/<?php echo urlencode($exam_link); ?>?q=${prevIndex}`;
                        }

                        function loadQuestion(index) {

                            const currentAnswer = document.querySelector('input[name="answer"]:checked');
                            const currentQuestionId = document.querySelector('input[name="question_id"]')?.value;

                            if (currentAnswer && currentQuestionId) {
                                const formData = new FormData();
                                formData.append('question_id', currentQuestionId);
                                formData.append('answer', currentAnswer.value);

                                saveAnswerWithFallback(formData)
                                .then(data => {
                                    // تم حفظ الإجابة
                                })
                                .catch(error => {
                                    // خطأ في حفظ الإجابة
                                });
                            }

                            window.location.href = `/exam_questions.php/<?php echo urlencode($exam_link); ?>?q=${index}`;
                        }

                        const answerForm = document.getElementById('answerForm');
                        if (answerForm) {
                            answerForm.onsubmit = function(e) {
                                e.preventDefault();

                                const answerAlert = document.getElementById('answerAlert');
                                const selectedAnswer = document.querySelector('input[name="answer"]:checked');
                                if (!selectedAnswer) {
                                    if (answerAlert) {
                                        answerAlert.classList.remove('d-none');
                                    }
                                    return false;
                                }

                                const formData = new FormData(this);
                                const currentQuestionIndex = parseInt(new URLSearchParams(window.location.search).get('q') || '0');
                                const totalQuestions = <?php echo count($questions); ?>;
                                const examLink = '<?php echo htmlspecialchars($exam['exam_link']); ?>';

                                // حفظ الإجابة أولاً ثم الانتقال للسؤال التالي
                                saveAnswerWithFallback(formData)
                                .then(data => {
                                    // تحديث دائرة السؤال
                                    updateQuestionCircle(currentQuestionIndex, selectedAnswer.value);

                                    // الانتقال للسؤال التالي
                                    var nextIndex = currentQuestionIndex + 1;
                                    if (nextIndex < totalQuestions) {
                                        window.location.href = '/exam_questions.php/<?php echo urlencode($exam_link); ?>?q=' + nextIndex;
                                    } else {
                                        window.location.href = '/exam_questions.php/<?php echo urlencode($exam_link); ?>?q=0';
                                    }
                                })
                                .catch(error => {
                                    // المتابعة حتى لو فشل الحفظ
                                    updateQuestionCircle(currentQuestionIndex, selectedAnswer.value);
                                    var nextIndex = currentQuestionIndex + 1;
                                    if (nextIndex < totalQuestions) {
                                        window.location.href = '/exam_questions.php/<?php echo urlencode($exam_link); ?>?q=' + nextIndex;
                                    } else {
                                        window.location.href = '/exam_questions.php/<?php echo urlencode($exam_link); ?>?q=0';
                                    }
                                });
                            };
                        }

                        document.addEventListener('DOMContentLoaded', function() {
                            const currentIndex = parseInt(new URLSearchParams(window.location.search).get('q') || '0');
                            const totalQuestions = <?php echo count($questions); ?>;
                            const saveButton = document.querySelector('.save-btn');

                            if (saveButton && currentIndex === totalQuestions - 1) {
                                saveButton.innerHTML = 'حفظ والعودة للسؤال الأول <i class="fas fa-redo-alt me-1"></i>';
                            }

                            updateAnswerCounters();
                        });
                    </script>
                <?php endif; ?>
        </div>
        </main>
    </div>
    <script>
        // معلومات الامتحان

        // دالة لحفظ الإجابة
        function saveAnswerWithFallback(formData) {
            return fetch('save_answer_handler.php', {
                method: 'POST',
                body: formData,
                credentials: 'same-origin'
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.text();
            })
            .then(text => {
                // فحص إذا كانت الاستجابة HTML بدلاً من JSON
                if (text.trim().startsWith('<!DOCTYPE') || text.trim().startsWith('<html')) {
                    // إرجاع استجابة وهمية للمتابعة
                    return {
                        success: true,
                        message: 'Answer processed (server returned HTML)',
                        question_id: Object.fromEntries(formData).question_id,
                        warning: true
                    };
                }

                try {
                    const json = JSON.parse(text);
                    return json;
                } catch (e) {
                    // إرجاع استجابة وهمية للمتابعة
                    return {
                        success: true,
                        message: 'Answer processed (JSON parse error)',
                        question_id: Object.fromEntries(formData).question_id,
                        warning: true
                    };
                }
            })
            .catch(error => {
                // إرجاع استجابة وهمية للمتابعة
                return {
                    success: true,
                    message: 'Answer processed (network error)',
                    question_id: Object.fromEntries(formData).question_id,
                    warning: true
                };
            });
        }



        const examEndTimestamp = <?php echo $exam_end_time; ?> * 1000;

        // تحميل الامتحان
        document.addEventListener('DOMContentLoaded', function() {
            // تم تحميل الامتحان بنجاح
        });

        function updateTimer() {
            const now = new Date().getTime();
            const timeLeft = examEndTimestamp - now;

            if (timeLeft <= 0) {
                document.getElementById('timer').innerHTML = '00:00';
                document.getElementById('timer').className = 'timer danger';

                // حذف وقت الامتحان المحفوظ عند انتهاء الوقت
                fetch('clear_exam_timer.php', {
                    method: 'POST',
                    credentials: 'same-origin'
                }).finally(() => {
                    window.location.href = 'exam_result.php';
                });
                return;
            }

            const minutes = Math.floor(timeLeft / (1000 * 60));
            const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);

            const timerElement = document.getElementById('timer');
            timerElement.innerHTML =
                (minutes < 10 ? '0' : '') + minutes + ':' +
                (seconds < 10 ? '0' : '') + seconds;

            if (timeLeft <= 5 * 60 * 1000) {
                timerElement.className = 'timer danger';
            } else if (timeLeft <= 10 * 60 * 1000) {
                timerElement.className = 'timer warning';
            } else {
                timerElement.className = 'timer';
            }
        }

        updateTimer();
        setInterval(updateTimer, 1000);

        function submitExam() {
            // إظهار رسالة تأكيد
            if (confirm('هل أنت متأكد من إنهاء الامتحان؟')) {
                // حذف وقت الامتحان المحفوظ
                fetch('clear_exam_timer.php', {
                    method: 'POST',
                    credentials: 'same-origin'
                }).finally(() => {
                    window.location.href = 'exam_result.php';
                });
            }
        }
    </script>

    <!-- Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>