/**
 * تحسينات JavaScript للموبايل
 * نظام الامتحانات الإلكترونية - جامعة المنيا
 */

document.addEventListener('DOMContentLoaded', function() {
    // منع الزوم على الموبايل
    document.addEventListener('gesturestart', function (e) {
        e.preventDefault();
    });

    document.addEventListener('gesturechange', function (e) {
        e.preventDefault();
    });

    document.addEventListener('gestureend', function (e) {
        e.preventDefault();
    });

    // منع الزوم بالضغط المزدوج
    let lastTouchEnd = 0;
    document.addEventListener('touchend', function (event) {
        const now = (new Date()).getTime();
        if (now - lastTouchEnd <= 300) {
            event.preventDefault();
        }
        lastTouchEnd = now;
    }, false);

    // منع الزوم بالقرص مع السماح بالتمرير
    document.addEventListener('touchmove', function (event) {
        if (event.touches.length > 1) {
            event.preventDefault(); 
        }
    }, { passive: false });

    // تحسين الـ viewport height للموبايل
    function setVH() {
        let vh = window.innerHeight * 0.01;
        document.documentElement.style.setProperty('--vh', `${vh}px`);
    }
    
    setVH();
    window.addEventListener('resize', setVH);
    window.addEventListener('orientationchange', function() {
        setTimeout(setVH, 100);
    });

    // إغلاق navbar تلقائياً عند النقر على رابط (للموبايل)
    const navLinks = document.querySelectorAll('.navbar-nav .nav-link');
    const navbarCollapse = document.querySelector('.navbar-collapse');
    
    navLinks.forEach(link => {
        link.addEventListener('click', () => {
            if (window.innerWidth < 992) {
                const bsCollapse = new bootstrap.Collapse(navbarCollapse, {
                    toggle: false
                });
                bsCollapse.hide();
            }
        });
    });

    // تحسين الجداول للموبايل
    const tables = document.querySelectorAll('.table');
    tables.forEach(table => {
        if (!table.parentElement.classList.contains('table-responsive')) {
            const wrapper = document.createElement('div');
            wrapper.className = 'table-responsive';
            table.parentNode.insertBefore(wrapper, table);
            wrapper.appendChild(table);
        }
    });

    // تحسين الأزرار المنفصلة للموبايل
    const mobileButtons = document.querySelectorAll('.mobile-nav-buttons .btn');
    
    mobileButtons.forEach(button => {
        // التأكد من ظهور الأزرار
        button.style.display = 'flex';
        button.style.visibility = 'visible';
        button.style.opacity = '1';
        
        button.addEventListener('touchstart', function() {
            this.style.transform = 'scale(0.95)';
        });
        
        button.addEventListener('touchend', function() {
            this.style.transform = 'scale(1)';
        });
        
        button.addEventListener('touchcancel', function() {
            this.style.transform = 'scale(1)';
        });
    });

    // التأكد من ظهور container الأزرار
    const mobileButtonsContainer = document.querySelector('.mobile-nav-buttons');
    if (mobileButtonsContainer) {
        mobileButtonsContainer.style.display = 'flex';
        mobileButtonsContainer.style.visibility = 'visible';
        mobileButtonsContainer.style.opacity = '1';
    }

    // تأكيد تسجيل الخروج على الموبايل
    const logoutButton = document.querySelector('.mobile-nav-buttons .btn-outline-danger');
    if (logoutButton) {
        logoutButton.addEventListener('click', function(e) {
            if (!confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                e.preventDefault();
            }
        });
    }

    // إصلاح مشكلة التمرير على iOS
    if (/iPad|iPhone|iPod/.test(navigator.userAgent)) {
        document.body.style.webkitOverflowScrolling = 'touch';
        document.body.style.overflowY = 'auto';
    }

    // تحسين البطاقات للموبايل
    function optimizeForMobile() {
        const isMobile = window.innerWidth < 768;
        const statCards = document.querySelectorAll('.stat-card');
        
        statCards.forEach(card => {
            if (isMobile) {
                card.style.transform = 'none';
                card.style.transition = 'box-shadow 0.2s ease';
            }
        });

        // تحسين التمرير للموبايل
        if (isMobile) {
            document.body.style.touchAction = 'pan-y';
            document.body.style.overflowY = 'auto';
            document.body.style.webkitOverflowScrolling = 'touch';
        }
    }

    optimizeForMobile();
    window.addEventListener('resize', optimizeForMobile);

    // تحسين النماذج للموبايل
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        const inputs = form.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            input.addEventListener('focus', function() {
                // منع الزوم عند التركيز على الحقول
                if (window.innerWidth < 768) {
                    this.style.fontSize = '16px';
                }
            });
        });
    });

    // تحسين الجداول للموبايل
    function optimizeTables() {
        const tables = document.querySelectorAll('.table');
        tables.forEach(table => {
            if (window.innerWidth < 768) {
                // إضافة تمرير أفقي للجداول على الموبايل
                if (!table.parentElement.classList.contains('table-responsive')) {
                    const wrapper = document.createElement('div');
                    wrapper.className = 'table-responsive';
                    table.parentNode.insertBefore(wrapper, table);
                    wrapper.appendChild(table);
                }
            }
        });
    }

    optimizeTables();
    window.addEventListener('resize', optimizeTables);

    // تحسين الـ modals للموبايل
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        modal.addEventListener('shown.bs.modal', function() {
            // منع التمرير في الخلفية عند فتح modal
            document.body.style.overflow = 'hidden';
        });
        
        modal.addEventListener('hidden.bs.modal', function() {
            // إعادة تفعيل التمرير عند إغلاق modal
            document.body.style.overflow = 'auto';
        });
    });

    // تحسين الـ alerts للموبايل
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        // إخفاء التنبيهات تلقائياً بعد 5 ثوان
        setTimeout(() => {
            if (alert.classList.contains('alert-dismissible')) {
                const closeButton = alert.querySelector('.btn-close');
                if (closeButton) {
                    closeButton.click();
                }
            }
        }, 5000);
    });

    // تحسين الأزرار للموبايل
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(button => {
        // إضافة تأثير اللمس للأزرار
        button.addEventListener('touchstart', function() {
            this.style.transform = 'scale(0.98)';
        });
        
        button.addEventListener('touchend', function() {
            this.style.transform = 'scale(1)';
        });
        
        button.addEventListener('touchcancel', function() {
            this.style.transform = 'scale(1)';
        });
    });

    // تحسين الـ dropdowns للموبايل
    const dropdowns = document.querySelectorAll('.dropdown-toggle');
    dropdowns.forEach(dropdown => {
        dropdown.addEventListener('click', function(e) {
            if (window.innerWidth < 768) {
                // إغلاق dropdowns أخرى عند فتح واحد جديد
                const otherDropdowns = document.querySelectorAll('.dropdown-menu.show');
                otherDropdowns.forEach(other => {
                    if (other !== this.nextElementSibling) {
                        other.classList.remove('show');
                    }
                });
            }
        });
    });

    // إضافة loading state للنماذج
    const submitButtons = document.querySelectorAll('button[type="submit"]');
    submitButtons.forEach(button => {
        const form = button.closest('form');
        if (form) {
            form.addEventListener('submit', function() {
                button.disabled = true;
                button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري المعالجة...';
                
                // إعادة تفعيل الزر بعد 10 ثوان كحد أقصى
                setTimeout(() => {
                    button.disabled = false;
                    button.innerHTML = button.getAttribute('data-original-text') || 'حفظ';
                }, 10000);
            });
            
            // حفظ النص الأصلي للزر
            const originalText = button.innerHTML;
            button.setAttribute('data-original-text', originalText);
        }
    });
});
