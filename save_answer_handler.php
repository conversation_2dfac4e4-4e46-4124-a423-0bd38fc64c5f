<?php
// منع أي output قبل JSON
ob_start();

// إعدادات الأمان
ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);
error_reporting(0);

// تعيين header للـ JSON
header('Content-Type: application/json; charset=utf-8');

// تنظيف أي output سابق
ob_clean();

session_start();

// تطبيق headers الأمنية
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');

// فحص طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'error' => 'طريقة طلب غير صحيحة']);
    exit();
}

// فحص الجلسة
if (!isset($_SESSION['exam_data']) || !isset($_SESSION['exam_data']['exam_id']) || !isset($_SESSION['exam_data']['student_id'])) {
    echo json_encode([
        'success' => false, 
        'error' => 'جلسة الامتحان غير صالحة'
    ]);
    exit();
}

// فحص البيانات المطلوبة
if (!isset($_POST['question_id']) || !isset($_POST['answer'])) {
    echo json_encode([
        'success' => false,
        'error' => 'بيانات غير مكتملة'
    ]);
    exit();
}

$exam_data = $_SESSION['exam_data'];
$question_id = (int)$_POST['question_id'];
$answer = $_POST['answer'];

try {
    require_once 'config/database.php';
    
    $pdo->beginTransaction();

    // التحقق من وجود السؤال في الامتحان
    $stmt = $pdo->prepare("
        SELECT id FROM questions 
        WHERE id = ? AND lecture_id IN (
            SELECT id FROM lectures WHERE exam_id = ?
        )
    ");
    $stmt->execute([$question_id, $exam_data['exam_id']]);
    
    if (!$stmt->fetch()) {
        throw new Exception('السؤال غير موجود في هذا الامتحان');
    }

    // حفظ أو تحديث الإجابة
    $stmt = $pdo->prepare("
        INSERT INTO student_answers (exam_id, student_id, question_id, answer, attempt_id, created_at)
        VALUES (?, ?, ?, ?, ?, NOW())
        ON DUPLICATE KEY UPDATE
        answer = VALUES(answer),
        updated_at = NOW()
    ");
    
    $stmt->execute([
        $exam_data['exam_id'],
        $exam_data['student_id'],
        $question_id,
        $answer,
        $exam_data['attempt_id']
    ]);

    $pdo->commit();

    echo json_encode([
        'success' => true,
        'message' => 'تم حفظ الإجابة بنجاح',
        'question_id' => $question_id
    ]);

} catch (Exception $e) {
    $pdo->rollback();
    
    echo json_encode([
        'success' => false,
        'error' => 'خطأ في حفظ الإجابة: ' . $e->getMessage()
    ]);
}

// تنظيف output buffer وإرسال JSON فقط
ob_clean();
ob_end_flush();
?>
