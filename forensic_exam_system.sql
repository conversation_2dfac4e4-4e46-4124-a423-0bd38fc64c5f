-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: 18 يوليو 2025 الساعة 23:50
-- إصدار الخادم: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `forensic_exam_system`
--

-- --------------------------------------------------------

--
-- بنية الجدول `activity_logs`
--

CREATE TABLE `activity_logs` (
  `id` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `action` varchar(100) NOT NULL,
  `details` text DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- إرجاع أو استيراد بيانات الجدول `activity_logs`
--

INSERT INTO `activity_logs` (`id`, `user_id`, `action`, `details`, `ip_address`, `user_agent`, `created_at`) VALUES
(1, 1, 'add_subject', 'إضافة مادة جديدة: Mostafa nady', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-13 19:22:56'),
(2, 1, 'edit_subject', 'تحديث مادة: Mostafa nady', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-13 19:23:20'),
(3, 1, 'edit_subject', 'تحديث مادة: Mostafa nady', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-13 19:23:26'),
(4, 1, 'delete_subject', 'حذف مادة برقم: 2', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-13 19:23:41'),
(5, 1, 'delete_subject', 'حذف مادة برقم: 2', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-13 19:24:13'),
(6, 1, 'delete_subject', 'حذف مادة برقم: 2', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-13 19:26:41'),
(7, 1, 'add_lecture', 'إضافة محاضرة جديدة: ستيىن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-13 19:29:23'),
(8, 1, 'add_lecture', 'إضافة محاضرة جديدة: ستيىن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-13 19:31:17'),
(9, 1, 'delete_lecture', 'حذف محاضرة برقم: 10', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-13 19:31:27'),
(10, 1, 'move_lecture', 'تحريك محاضرة لأعلى: 9', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-13 19:34:04'),
(11, 1, 'move_lecture', 'تحريك محاضرة لأعلى: 9', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-13 19:34:13'),
(12, 1, 'move_lecture', 'تحريك محاضرة لأعلى: 9', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-13 19:34:47'),
(13, 1, 'add_question', 'إضافة سؤال جديد للمحاضرة: مقدمة في البرمجة', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-13 19:47:20'),
(14, 1, 'edit_question', 'تحديث سؤال: 10', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-13 19:47:44'),
(15, 1, 'delete_question', 'حذف سؤال: 10', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-13 19:47:59'),
(16, 1, 'bulk_add_questions', 'إضافة 5 سؤال للمحاضرة: مقدمة في البرمجة', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-13 19:58:47'),
(17, 1, 'bulk_add_questions', 'إضافة 5 سؤال للمحاضرة: مقدمة في البرمجة', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-13 19:59:29'),
(18, 1, 'bulk_add_questions', 'إضافة 5 سؤال للمحاضرة: ستيىن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-13 20:00:31'),
(19, 1, 'add_lecture', 'إضافة محاضرة جديدة: نةتهنن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-13 20:04:08'),
(20, 1, 'bulk_add_questions', 'إضافة 5 سؤال للمحاضرة: نةتهنن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-13 20:04:22'),
(21, 1, 'delete_exam', 'حذف امتحان برقم: 1', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-13 20:04:39'),
(22, 1, 'delete_exam', 'حذف امتحان برقم: 1', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-13 20:09:19'),
(23, 1, 'add_exam', 'إضافة امتحان جديد: نةنمة', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-13 21:07:17'),
(24, 1, 'delete_exam', 'حذف امتحان برقم: 2', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-13 21:07:39'),
(25, 1, 'delete_exam', 'حذف امتحان برقم: 2', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-13 21:07:43'),
(26, 1, 'delete_exam', 'حذف امتحان برقم: 3', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-13 21:07:49'),
(27, 1, 'delete_exam', 'حذف امتحان برقم: 4', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-13 21:07:55'),
(28, 1, 'edit_exam', 'تحديث امتحان: نةنمة', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-13 21:11:07'),
(29, 1, 'edit_exam', 'تحديث امتحان: نةنمة', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-13 21:11:22'),
(30, 1, 'delete_exam', 'حذف امتحان برقم: 5', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-13 21:13:16'),
(31, 1, 'add_exam', 'إضافة امتحان جديد: نةءةئ', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-13 21:16:56'),
(32, 1, 'add_exam', 'إضافة امتحان جديد: نةءةئ', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-13 21:21:33'),
(33, 1, 'delete_exam', 'حذف امتحان برقم: 9', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-13 21:22:41'),
(34, 1, 'delete_exam', 'حذف امتحان برقم: 10', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-13 21:24:49'),
(35, 1, 'delete_exam', 'حذف امتحان برقم: 6', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-13 21:24:53'),
(36, 1, 'delete_exam', 'حذف امتحان برقم: 7', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-13 21:24:57'),
(37, 1, 'delete_exam', 'حذف امتحان برقم: 8', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-13 21:28:10'),
(38, 1, 'add_exam', 'إضافة امتحان جديد: نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-13 21:31:12'),
(39, 1, 'add_exam_questions', 'إضافة 5 سؤال للامتحان: نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-13 21:40:47'),
(40, 1, 'add_exam_questions', 'إضافة 22 سؤال للامتحان: نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-13 21:44:29'),
(41, 1, 'edit_exam', 'تحديث امتحان: نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-13 21:47:00'),
(42, 1, 'edit_exam', 'تحديث امتحان: نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-13 21:59:02'),
(43, 1, 'add_exam_questions', 'إضافة 27 سؤال للامتحان: نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-13 22:00:32'),
(44, 1, 'remove_exam_question', 'إزالة سؤال من الامتحان: نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-13 22:01:03'),
(45, 1, 'edit_exam', 'تحديث امتحان: نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-13 22:44:16'),
(46, 1, 'edit_exam', 'تحديث امتحان: نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-13 22:47:40'),
(47, 1, 'edit_exam', 'تحديث امتحان: نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-13 22:51:31'),
(48, 1, 'edit_exam', 'تحديث امتحان: نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-13 23:12:12'),
(49, 1, 'add_exam_questions', 'إضافة 27 سؤال للامتحان: نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-14 20:04:44'),
(50, NULL, 'exam_submitted', 'امتحان مكتمل: ةسنم - نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-14 21:34:18'),
(51, 1, 'edit_exam', 'تحديث امتحان: نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-14 21:36:12'),
(52, NULL, 'exam_submitted', 'امتحان مكتمل: تىتن - نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-14 21:36:49'),
(53, 1, 'edit_exam', 'تحديث امتحان: نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-14 21:40:18'),
(54, NULL, 'exam_submitted', 'امتحان مكتمل: الاات - نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-14 21:40:35'),
(55, NULL, 'exam_submitted', 'امتحان مكتمل: تةىتن - نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-14 21:42:40'),
(56, 1, 'edit_exam', 'تحديث امتحان: نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-14 21:45:51'),
(57, 1, 'edit_exam', 'تحديث امتحان: نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-14 21:49:01'),
(58, NULL, 'exam_submitted', 'امتحان مكتمل: تىتن - نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-14 22:00:41'),
(59, NULL, 'exam_submitted', 'امتحان مكتمل: تىتن - نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-14 22:04:47'),
(65, 1, 'edit_exam', 'تحديث امتحان: نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-15 19:58:00'),
(66, NULL, 'exam_started', 'بدء امتحان: ؤءىتن - نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-15 19:58:15'),
(67, NULL, 'exam_submitted', 'امتحان مكتمل: ؤءىتن - نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-15 19:59:06'),
(68, NULL, 'exam_started', 'بدء امتحان: تىنتئ - نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-15 19:59:27'),
(69, NULL, 'exam_submitted', 'امتحان مكتمل: تىنتئ - نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-15 20:02:34'),
(70, NULL, 'exam_started', 'بدء امتحان: ءتىن - نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-15 20:03:15'),
(71, NULL, 'exam_started', 'بدء امتحان: ى ةىى - نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-15 20:07:52'),
(72, NULL, 'exam_started', 'بدء امتحان: تىىنت - نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-15 20:13:20'),
(73, NULL, 'exam_started', 'بدء امتحان: تالاات - نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-15 20:16:05'),
(74, NULL, 'exam_started', 'بدء امتحان: ة س - نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-15 20:26:31'),
(75, NULL, 'exam_submitted', 'امتحان مكتمل: ة س - نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-15 20:29:00'),
(76, NULL, 'exam_started', 'بدء امتحان: اات - نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-15 20:29:50'),
(77, NULL, 'exam_submitted', 'امتحان مكتمل: اات - نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-15 20:30:17'),
(78, NULL, 'exam_started', 'بدء امتحان: عتايعس - نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-15 20:31:23'),
(79, NULL, 'exam_started', 'بدء امتحان: تتنتنتن - نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-15 20:46:17'),
(80, NULL, 'exam_started', 'بدء امتحان: تىتن - نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-15 20:49:53'),
(81, NULL, 'exam_started', 'بدء امتحان: يسىتن - نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-15 21:04:05'),
(82, NULL, 'exam_submitted', 'امتحان مكتمل: يسىتن - نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-15 21:12:55'),
(83, NULL, 'exam_started', 'بدء امتحان: تىنت - نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-15 21:14:07'),
(84, NULL, 'exam_submitted', 'امتحان مكتمل: تىنت - نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-15 21:14:41'),
(85, NULL, 'exam_started', 'بدء امتحان: تىت - نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-15 21:15:35'),
(86, NULL, 'exam_submitted', 'امتحان مكتمل: تىت - نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-15 21:16:03'),
(87, NULL, 'exam_started', 'بدء امتحان: تىتن - نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-15 21:22:59'),
(88, NULL, 'exam_submitted', 'امتحان مكتمل: تىتن - نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-15 21:23:40'),
(89, NULL, 'exam_started', 'بدء امتحان: ةتت - نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-15 21:26:42'),
(90, NULL, 'exam_submitted', 'امتحان مكتمل: ةتت - نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-15 21:27:08'),
(91, NULL, 'exam_started', 'بدء امتحان: يتستىن - نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-15 21:29:08'),
(92, NULL, 'exam_submitted', 'امتحان مكتمل: يتستىن - نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-15 21:29:15'),
(93, 1, 'edit_exam', 'تحديث امتحان: نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-15 22:59:20'),
(94, 1, 'edit_exam', 'تحديث امتحان: نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-15 22:59:48'),
(95, 1, 'edit_exam', 'تحديث امتحان: نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-15 23:00:08'),
(96, 1, 'edit_exam', 'تحديث امتحان: نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-15 23:01:14'),
(97, 1, 'edit_exam', 'تحديث امتحان: نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-15 23:11:30'),
(98, 1, 'edit_exam', 'تحديث امتحان: نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-15 23:12:08'),
(99, NULL, 'exam_started', 'بدء امتحان: تىتنؤ - نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-15 23:19:42'),
(100, NULL, 'exam_started', 'بدء امتحان: ننت - نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-15 23:22:04'),
(101, NULL, 'exam_started', 'بدء امتحان: تءن - نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-15 23:22:55'),
(102, NULL, 'exam_started', 'بدء امتحان: نةن - نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-15 23:26:52'),
(103, NULL, 'exam_started', 'بدء امتحان: تىتن - نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-15 23:29:18'),
(104, 1, 'login', 'تسجيل دخول ناجح', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-17 19:04:06'),
(105, 1, 'edit_exam', 'تحديث امتحان: نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-17 19:05:03'),
(106, 1, 'edit_exam', 'تحديث امتحان: نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-17 19:09:43'),
(107, 1, 'edit_exam', 'تحديث امتحان: نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-17 19:10:01'),
(108, 1, 'auto_complete_exams', 'تم تحديث حالة 1 امتحان إلى مكتمل تلقائياً', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-17 19:10:10'),
(109, 1, 'edit_exam', 'تحديث امتحان: نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-17 19:10:31'),
(110, 1, 'auto_complete_exams', 'تم تحديث حالة 1 امتحان إلى مكتمل تلقائياً', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-17 19:10:54'),
(111, 1, 'edit_exam', 'تحديث امتحان: نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-17 19:10:54'),
(112, 1, 'auto_complete_exams', 'تم تحديث حالة 1 امتحان إلى مكتمل تلقائياً', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-17 19:10:57'),
(113, 1, 'edit_exam', 'تحديث امتحان: نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-17 19:11:11'),
(114, 1, 'edit_exam', 'تحديث امتحان: نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-17 19:11:31'),
(115, NULL, 'exam_started', 'بدء امتحان: ةنةؤن - نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-17 19:11:48'),
(116, NULL, 'exam_started', 'بدء امتحان: ننتنت - نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-17 19:22:41'),
(117, NULL, 'exam_started', 'بدء امتحان: نممن - نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-17 19:25:57'),
(118, NULL, 'exam_started', 'بدء امتحان: ةىتىئة - نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-17 19:43:08'),
(119, NULL, 'exam_started', 'بدء امتحان: تتنءؤ - نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-17 19:48:01'),
(120, NULL, 'exam_submitted', 'امتحان مكتمل: تتنءؤ - نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-17 19:52:16'),
(121, NULL, 'exam_started', 'بدء امتحان: ةن - نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-17 21:14:30'),
(122, NULL, 'exam_submitted', 'امتحان مكتمل: ةن - نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-17 21:15:04'),
(123, NULL, 'exam_started', 'بدء امتحان: نتت - نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-17 21:23:04'),
(124, NULL, 'exam_started', 'بدء امتحان: ةةو - نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-17 22:14:08'),
(125, NULL, 'exam_submitted', 'امتحان مكتمل: ةةو - نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-17 22:15:43'),
(126, NULL, 'exam_started', 'بدء امتحان: ءنت - نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-17 22:39:43'),
(127, NULL, 'exam_submitted', 'امتحان مكتمل: ءنت - نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-17 22:41:53'),
(128, NULL, 'exam_started', 'بدء امتحان: مصطفي نادي - نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-17 22:53:22'),
(129, NULL, 'exam_submitted', 'امتحان مكتمل: مصطفي نادي - نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-17 22:55:12'),
(130, 1, 'delete_exam', 'حذف امتحان برقم: 13', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-17 22:57:36'),
(131, 1, 'delete_exam', 'حذف امتحان برقم: 13', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-17 22:58:28'),
(132, NULL, 'auto_complete_exams', 'تم تحديث حالة 1 امتحان إلى مكتمل تلقائياً', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-18 12:10:36'),
(133, 1, 'delete_exam', 'حذف امتحان برقم: 13', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-18 12:10:42'),
(134, 1, 'delete_result', 'حذف نتيجة برقم: 34', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-18 12:25:34'),
(135, 1, 'add_exam', 'إضافة امتحان جديد: تةسخه', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-18 12:48:49'),
(136, 1, 'add_exam', 'إضافة امتحان جديد: تةسخه', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-18 12:49:32'),
(137, 1, 'delete_exam', 'حذف امتحان برقم: 15', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-18 12:49:39'),
(138, 1, 'edit_exam', 'تحديث امتحان: تةسخه', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-18 13:04:34'),
(139, 1, 'add_exam_questions', 'إضافة 27 سؤال للامتحان: تةسخه', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-18 13:05:19'),
(140, 1, 'edit_exam', 'تحديث امتحان: نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-18 13:06:26'),
(141, 1, 'auto_complete_exams', 'تم تحديث حالة 1 امتحان إلى مكتمل تلقائياً', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-18 13:07:16'),
(142, 1, 'edit_exam', 'تحديث امتحان: نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-18 13:08:36'),
(143, 1, 'edit_exam', 'تحديث امتحان: نةن', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-18 13:08:54'),
(144, 1, 'logout', 'تسجيل خروج', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-18 14:28:58'),
(145, 1, 'logout', 'تسجيل خروج', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-18 15:00:08'),
(146, 1, 'logout', 'تسجيل خروج', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-18 15:40:50'),
(147, 1, 'logout', 'تسجيل خروج', '192.168.1.7', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36', '2025-07-18 15:41:01'),
(148, 1, 'add_subject', 'إضافة مادة جديدة: Mostafa', '192.168.1.7', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36', '2025-07-18 17:21:13'),
(149, 1, 'edit_subject', 'تحديث مادة: Mostafa', '192.168.1.7', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36', '2025-07-18 17:21:25'),
(150, 1, 'edit_subject', 'تحديث مادة: Mostafa', '192.168.1.7', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36', '2025-07-18 17:21:31'),
(151, 1, 'edit_lecture', 'تحديث محاضرة: نةتهنن', '192.168.1.7', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36', '2025-07-18 18:08:48'),
(152, 1, 'edit_question', 'تحديث سؤال: 21', '192.168.1.7', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36', '2025-07-18 18:15:24'),
(153, 1, 'add_exam_questions', 'إضافة 6 سؤال للامتحان: تةسخه', '192.168.1.7', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36', '2025-07-18 19:53:09'),
(154, 1, 'add_exam_questions', 'إضافة 6 سؤال للامتحان: تةسخه', '192.168.1.7', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36', '2025-07-18 19:55:29'),
(155, 1, 'remove_exam_question', 'إزالة سؤال من الامتحان: تةسخه', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-18 19:56:06'),
(156, 1, 'logout', 'تسجيل خروج', '192.168.1.7', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36', '2025-07-18 20:03:42'),
(157, NULL, 'exam_started', 'بدء امتحان: مصطفى نادي صابر ماضي - تةسخه', '192.168.1.7', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36', '2025-07-18 20:13:04'),
(158, NULL, 'exam_started', 'بدء امتحان: نةمن - تةسخه', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-18 20:14:09'),
(159, NULL, 'exam_submitted', 'امتحان مكتمل: مصطفى نادي صابر ماضي - تةسخه', '192.168.1.7', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36', '2025-07-18 20:14:57'),
(160, NULL, 'exam_submitted', 'امتحان مكتمل: نةمن - تةسخه', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-18 20:15:05'),
(161, 1, 'logout', 'تسجيل خروج', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-18 20:53:27'),
(162, 1, 'logout', 'تسجيل خروج', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-18 21:07:45'),
(163, 1, 'add_subject', 'إضافة مادة جديدة: تت', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-18 21:15:05'),
(164, 1, 'delete_subject', 'حذف مادة برقم: 7', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-18 21:15:12'),
(165, 1, 'delete_subject', 'حذف مادة برقم: 7', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-18 21:15:21'),
(166, 1, 'add_lecture', 'إضافة محاضرة جديدة: نتىنت', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-18 21:25:04'),
(167, 1, 'move_lecture', 'تحريك محاضرة لأسفل: 9', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-18 21:25:44'),
(168, 1, 'move_lecture', 'تحريك محاضرة لأسفل: 9', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-18 21:25:47'),
(169, 1, 'delete_lecture', 'حذف محاضرة برقم: 12', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-18 21:28:46'),
(170, 1, 'add_exam', 'إضافة امتحان جديد: نتسي', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-18 21:39:12'),
(171, 1, 'delete_exam', 'حذف امتحان برقم: 21', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-18 21:39:21'),
(172, 1, 'remove_exam_question', 'إزالة سؤال من الامتحان: تةسخه', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-18 21:46:42'),
(173, 1, 'add_exam_questions', 'إضافة 2 سؤال للامتحان: تةسخه', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-18 21:46:58'),
(174, NULL, 'exam_started', 'بدء امتحان: عاعه - تةسخه', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-18 21:48:10'),
(175, NULL, 'exam_submitted', 'امتحان مكتمل: عاعه - تةسخه', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-18 21:48:18');

-- --------------------------------------------------------

--
-- بنية الجدول `exams`
--

CREATE TABLE `exams` (
  `id` int(11) NOT NULL,
  `title` varchar(200) NOT NULL,
  `subject_id` int(11) NOT NULL,
  `exam_type` enum('exam','quiz') NOT NULL DEFAULT 'exam',
  `description` text DEFAULT NULL,
  `start_time` datetime NOT NULL,
  `end_time` datetime NOT NULL,
  `duration_minutes` int(11) NOT NULL,
  `max_attempts` int(11) DEFAULT 1,
  `questions_count` int(11) DEFAULT 10 COMMENT 'عدد الأسئلة المطلوبة للامتحان',
  `randomize_questions` tinyint(1) DEFAULT 0,
  `show_results_immediately` tinyint(1) DEFAULT 0,
  `show_results_after_end` tinyint(1) DEFAULT 1,
  `passing_percentage` decimal(5,2) DEFAULT 50.00,
  `status` enum('draft','active','completed','cancelled') NOT NULL DEFAULT 'draft',
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `exam_link` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- إرجاع أو استيراد بيانات الجدول `exams`
--

INSERT INTO `exams` (`id`, `title`, `subject_id`, `exam_type`, `description`, `start_time`, `end_time`, `duration_minutes`, `max_attempts`, `questions_count`, `randomize_questions`, `show_results_immediately`, `show_results_after_end`, `passing_percentage`, `status`, `created_by`, `created_at`, `updated_at`, `exam_link`) VALUES
(11, 'نةن', 5, 'quiz', 'نةنة', '2025-07-12 00:29:00', '2025-07-19 03:46:00', 20, 2, 20, 1, 1, 1, 50.00, 'active', 1, '2025-07-13 21:31:12', '2025-07-18 13:08:54', '48af0b8dda251042e1f62b4046a418cf'),
(14, 'تةسخه', 5, 'exam', 'نةءن', '2025-07-17 15:47:00', '2025-07-19 15:47:00', 15, 1, 10, 0, 0, 1, 50.00, 'active', 1, '2025-07-18 12:48:49', '2025-07-18 13:20:04', '29561e133bd82669f3dbdc830901a0d7'),
(18, 'تةسخه', 5, 'exam', 'نةءن', '2025-07-17 15:47:00', '2025-07-19 15:47:00', 15, 1, 10, 0, 0, 1, 50.00, 'active', 1, '2025-07-18 12:48:49', '2025-07-18 14:05:28', '212942d91b1f8c7443a3b6bf4b88324b'),
(20, 'نةن', 5, 'quiz', 'نةنة', '2025-07-12 00:29:00', '2025-07-19 03:46:00', 20, 2, 20, 1, 1, 1, 50.00, 'active', 1, '2025-07-13 21:31:12', '2025-07-18 14:05:28', '2ae50b5637ba394432fb684c78b9bf39');

-- --------------------------------------------------------

--
-- بنية الجدول `exam_lectures`
--

CREATE TABLE `exam_lectures` (
  `id` int(11) NOT NULL,
  `exam_id` int(11) NOT NULL,
  `lecture_id` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- إرجاع أو استيراد بيانات الجدول `exam_lectures`
--

INSERT INTO `exam_lectures` (`id`, `exam_id`, `lecture_id`, `created_at`) VALUES
(249, 14, 11, '2025-07-18 13:04:34'),
(250, 14, 6, '2025-07-18 13:04:34'),
(251, 14, 7, '2025-07-18 13:04:34'),
(252, 14, 9, '2025-07-18 13:04:34'),
(253, 14, 1, '2025-07-18 13:04:34'),
(254, 14, 2, '2025-07-18 13:04:34'),
(255, 14, 3, '2025-07-18 13:04:34'),
(270, 11, 11, '2025-07-18 13:08:54'),
(271, 11, 6, '2025-07-18 13:08:54'),
(272, 11, 7, '2025-07-18 13:08:54'),
(273, 11, 9, '2025-07-18 13:08:54'),
(274, 11, 1, '2025-07-18 13:08:54'),
(275, 11, 2, '2025-07-18 13:08:54'),
(276, 11, 3, '2025-07-18 13:08:54');

-- --------------------------------------------------------

--
-- بنية الجدول `exam_questions`
--

CREATE TABLE `exam_questions` (
  `id` int(11) NOT NULL,
  `exam_id` int(11) NOT NULL,
  `question_id` int(11) NOT NULL,
  `question_order` int(11) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- إرجاع أو استيراد بيانات الجدول `exam_questions`
--

INSERT INTO `exam_questions` (`id`, `exam_id`, `question_id`, `question_order`, `created_at`) VALUES
(60, 11, 1, 1, '2025-07-14 20:04:44'),
(61, 11, 2, 2, '2025-07-14 20:04:44'),
(62, 11, 3, 3, '2025-07-14 20:04:44'),
(63, 11, 4, 4, '2025-07-14 20:04:44'),
(64, 11, 5, 5, '2025-07-14 20:04:44'),
(65, 11, 8, 6, '2025-07-14 20:04:44'),
(66, 11, 9, 7, '2025-07-14 20:04:44'),
(67, 11, 11, 8, '2025-07-14 20:04:44'),
(68, 11, 12, 9, '2025-07-14 20:04:44'),
(69, 11, 13, 10, '2025-07-14 20:04:44'),
(70, 11, 14, 11, '2025-07-14 20:04:44'),
(71, 11, 15, 12, '2025-07-14 20:04:44'),
(72, 11, 16, 13, '2025-07-14 20:04:44'),
(73, 11, 17, 14, '2025-07-14 20:04:44'),
(74, 11, 18, 15, '2025-07-14 20:04:44'),
(75, 11, 19, 16, '2025-07-14 20:04:44'),
(76, 11, 20, 17, '2025-07-14 20:04:44'),
(77, 11, 21, 18, '2025-07-14 20:04:44'),
(78, 11, 22, 19, '2025-07-14 20:04:44'),
(79, 11, 23, 20, '2025-07-14 20:04:44'),
(80, 11, 24, 21, '2025-07-14 20:04:44'),
(81, 11, 25, 22, '2025-07-14 20:04:44'),
(82, 11, 26, 23, '2025-07-14 20:04:44'),
(83, 11, 27, 24, '2025-07-14 20:04:44'),
(84, 11, 28, 25, '2025-07-14 20:04:44'),
(85, 11, 29, 26, '2025-07-14 20:04:44'),
(86, 11, 30, 27, '2025-07-14 20:04:44'),
(88, 14, 2, 2, '2025-07-18 13:05:19'),
(89, 14, 3, 3, '2025-07-18 13:05:19'),
(90, 14, 4, 4, '2025-07-18 13:05:19'),
(91, 14, 5, 5, '2025-07-18 13:05:19'),
(93, 14, 9, 7, '2025-07-18 13:05:19'),
(94, 14, 11, 8, '2025-07-18 13:05:19'),
(95, 14, 12, 9, '2025-07-18 13:05:19'),
(96, 14, 13, 10, '2025-07-18 13:05:19'),
(97, 14, 14, 11, '2025-07-18 13:05:19'),
(98, 14, 15, 12, '2025-07-18 13:05:19'),
(99, 14, 16, 13, '2025-07-18 13:05:19'),
(100, 14, 17, 14, '2025-07-18 13:05:19'),
(101, 14, 18, 15, '2025-07-18 13:05:19'),
(102, 14, 19, 16, '2025-07-18 13:05:19'),
(103, 14, 20, 17, '2025-07-18 13:05:19'),
(104, 14, 21, 18, '2025-07-18 13:05:19'),
(105, 14, 22, 19, '2025-07-18 13:05:19'),
(106, 14, 23, 20, '2025-07-18 13:05:19'),
(107, 14, 24, 21, '2025-07-18 13:05:19'),
(108, 14, 25, 22, '2025-07-18 13:05:19'),
(109, 14, 26, 2, '2025-07-18 13:05:19'),
(110, 14, 27, 3, '2025-07-18 13:05:19'),
(111, 14, 28, 4, '2025-07-18 13:05:19'),
(112, 14, 29, 5, '2025-07-18 13:05:19'),
(113, 14, 30, 6, '2025-07-18 13:05:19'),
(126, 14, 1, 23, '2025-07-18 21:46:58'),
(131, 14, 8, 28, '2025-07-18 21:46:58');

-- --------------------------------------------------------

--
-- بنية الجدول `exam_results`
--

CREATE TABLE `exam_results` (
  `id` int(11) NOT NULL,
  `exam_id` int(11) NOT NULL,
  `student_id` varchar(50) NOT NULL,
  `student_name` varchar(255) NOT NULL,
  `section_number` varchar(50) NOT NULL,
  `start_time` datetime NOT NULL,
  `end_time` datetime DEFAULT NULL,
  `duration_minutes` int(11) NOT NULL DEFAULT 0,
  `submitted_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- إرجاع أو استيراد بيانات الجدول `exam_results`
--

INSERT INTO `exam_results` (`id`, `exam_id`, `student_id`, `student_name`, `section_number`, `start_time`, `end_time`, `duration_minutes`, `submitted_at`) VALUES
(35, 11, '1234', 'ءنت', 'ننم', '2025-07-18 01:39:43', '2025-07-18 01:41:53', 20, '2025-07-17 22:39:43'),
(36, 11, '123', 'مصطفي نادي', 'تيستن', '2025-07-18 01:53:22', '2025-07-18 01:55:12', 20, '2025-07-17 22:53:22'),
(37, 14, '12345678', 'مصطفى نادي صابر ماضي', 'D1', '2025-07-18 23:13:04', '2025-07-18 23:14:57', 15, '2025-07-18 20:13:04'),
(38, 14, '767', 'نةمن', 'ةن', '2025-07-18 23:14:09', '2025-07-18 23:15:05', 15, '2025-07-18 20:14:09'),
(39, 14, '1235', 'عاعه', 'ىتىت', '2025-07-19 00:48:10', '2025-07-19 00:48:18', 15, '2025-07-18 21:48:10');

-- --------------------------------------------------------

--
-- بنية الجدول `lectures`
--

CREATE TABLE `lectures` (
  `id` int(11) NOT NULL,
  `subject_id` int(11) NOT NULL,
  `title` varchar(200) NOT NULL,
  `lecture_number` int(11) NOT NULL,
  `description` text DEFAULT NULL,
  `status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- إرجاع أو استيراد بيانات الجدول `lectures`
--

INSERT INTO `lectures` (`id`, `subject_id`, `title`, `lecture_number`, `description`, `status`, `created_by`, `created_at`, `updated_at`) VALUES
(1, 1, 'مقدمة في البرمجة', 1, 'أساسيات البرمجة والخوارزميات', 'active', 1, '2025-07-13 19:22:42', '2025-07-18 21:25:44'),
(2, 1, 'متغيرات وأنواع البيانات', 2, 'أنواع البيانات والمتغيرات في البرمجة', 'active', 1, '2025-07-13 19:22:42', '2025-07-18 21:25:47'),
(3, 1, 'التحكم في التدفق', 4, 'الشروط والحلقات التكرارية', 'active', 1, '2025-07-13 19:22:42', '2025-07-13 19:34:04'),
(6, 3, 'مبادئ الأمن', 1, 'المبادئ الأساسية لأمن المعلومات', 'active', 1, '2025-07-13 19:22:42', '2025-07-13 19:22:42'),
(7, 4, 'أدوات التحليل الجنائي', 1, 'الأدوات المستخدمة في التحليل الجنائي الرقمي', 'active', 1, '2025-07-13 19:22:42', '2025-07-13 19:22:42'),
(9, 1, 'ستيىن', 3, 'يستىتنسي', 'active', 1, '2025-07-13 19:29:23', '2025-07-18 21:25:47'),
(11, 5, 'نةتهنن', 1, 'bbsbs', 'active', 1, '2025-07-13 20:04:08', '2025-07-18 18:08:48');

-- --------------------------------------------------------

--
-- بنية الجدول `notifications`
--

CREATE TABLE `notifications` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `title` varchar(200) NOT NULL,
  `message` text NOT NULL,
  `type` enum('info','success','warning','error') DEFAULT 'info',
  `is_read` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- بنية الجدول `questions`
--

CREATE TABLE `questions` (
  `id` int(11) NOT NULL,
  `lecture_id` int(11) NOT NULL,
  `question_text` text NOT NULL,
  `option_a` varchar(500) NOT NULL,
  `option_b` varchar(500) NOT NULL,
  `option_c` varchar(500) NOT NULL,
  `option_d` varchar(500) NOT NULL,
  `correct_answer` enum('a','b','c','d') NOT NULL,
  `points` int(11) DEFAULT 1,
  `difficulty` enum('easy','medium','hard') DEFAULT 'medium',
  `status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- إرجاع أو استيراد بيانات الجدول `questions`
--

INSERT INTO `questions` (`id`, `lecture_id`, `question_text`, `option_a`, `option_b`, `option_c`, `option_d`, `correct_answer`, `points`, `difficulty`, `status`, `created_by`, `created_at`, `updated_at`) VALUES
(1, 1, 'ما هو البرنامج؟', 'جهاز حاسوب', 'مجموعة من التعليمات', 'لغة برمجة', 'مترجم', 'b', 1, 'easy', 'active', 1, '2025-07-13 19:22:42', '2025-07-13 19:22:42'),
(2, 1, 'أي من التالي ليس لغة برمجة؟', 'Python', 'Java', 'HTML', 'C++', 'c', 1, 'easy', 'active', 1, '2025-07-13 19:22:42', '2025-07-13 19:22:42'),
(3, 2, 'ما هو نوع البيانات المناسب لتخزين النصوص؟', 'int', 'float', 'string', 'boolean', 'c', 1, 'easy', 'active', 1, '2025-07-13 19:22:42', '2025-07-13 19:22:42'),
(4, 2, 'أي من التالي متغير صحيح؟', '1variable', 'variable1', 'var-iable', 'var iable', 'b', 1, 'medium', 'active', 1, '2025-07-13 19:22:42', '2025-07-13 19:22:42'),
(5, 3, 'ما هو نوع الحلقة المستخدمة عند معرفة عدد التكرارات؟', 'while', 'for', 'do-while', 'if', 'b', 1, 'medium', 'active', 1, '2025-07-13 19:22:42', '2025-07-13 19:22:42'),
(8, 6, 'ما هو نوع التشفير الذي يستخدم مفتاح واحد؟', 'التشفير المتماثل', 'التشفير غير المتماثل', 'التشفير الهجين', 'التشفير الكمي', 'a', 1, 'medium', 'active', 1, '2025-07-13 19:22:42', '2025-07-13 19:22:42'),
(9, 7, 'ما هي الأداة المستخدمة لاستخراج البيانات المحذوفة؟', 'Recuva', 'Photoshop', 'Word', 'Excel', 'a', 1, 'medium', 'active', 1, '2025-07-13 19:22:42', '2025-07-13 19:22:42'),
(11, 1, 'ما هي عاصمة إيطاليا؟', 'باريس', 'مدريد', 'روما', 'أثينا', 'c', 1, 'medium', 'active', 1, '2025-07-13 19:58:47', '2025-07-13 19:58:47'),
(12, 1, 'كم عدد الكواكب في المجموعة الشمسية؟', '7', '8', '9', '10', 'b', 1, 'medium', 'active', 1, '2025-07-13 19:58:47', '2025-07-13 19:58:47'),
(13, 1, 'ما هو الحيوان المعروف بـ \"سفينة الصحراء\"؟', 'الجمل', 'الحصان', 'الثعلب', 'النمر', 'a', 1, 'medium', 'active', 1, '2025-07-13 19:58:47', '2025-07-13 19:58:47'),
(14, 1, 'من اخترع المصباح الكهربائي؟', 'ألكسندر جراهام بيل', 'توماس إديسون', 'إسحاق نيوتن', 'نيكولا تسلا', 'b', 1, 'medium', 'active', 1, '2025-07-13 19:58:47', '2025-07-13 19:58:47'),
(15, 1, 'ما هو أقرب كوكب للشمس؟', 'الزهرة', 'الأرض', 'عطارد', 'المريخ', 'c', 1, 'medium', 'active', 1, '2025-07-13 19:58:47', '2025-07-13 19:58:47'),
(16, 1, 'What is the capital of Italy?', 'Paris', 'Madrid', 'Rome', 'Athens', 'c', 1, 'medium', 'active', 1, '2025-07-13 19:59:29', '2025-07-13 19:59:29'),
(17, 1, 'How many planets are in the Solar System?', '7', '8', '9', '10', 'b', 1, 'medium', 'active', 1, '2025-07-13 19:59:29', '2025-07-13 19:59:29'),
(18, 1, 'Which animal is known as the \"Ship of the Desert\"?', 'Camel', 'Horse', 'Fox', 'Tiger', 'a', 1, 'medium', 'active', 1, '2025-07-13 19:59:29', '2025-07-13 19:59:29'),
(19, 1, 'Who invented the electric light bulb?', 'Alexander Graham Bell', 'Thomas Edison', 'Isaac Newton', 'Nikola Tesla', 'b', 1, 'medium', 'active', 1, '2025-07-13 19:59:29', '2025-07-13 19:59:29'),
(20, 1, 'Which planet is closest to the Sun?', 'Venus', 'Earth', 'Mercury', 'Mars', 'c', 1, 'medium', 'active', 1, '2025-07-13 19:59:29', '2025-07-13 19:59:29'),
(21, 9, 'What is the capital of Italy?', 'Paris', 'Madrid', 'Rome', 'Athens', 'c', 1, 'medium', 'active', 1, '2025-07-13 20:00:31', '2025-07-13 20:00:31'),
(22, 9, 'How many planets are in the Solar System?', '7', '8', '9', '10', 'b', 1, 'medium', 'active', 1, '2025-07-13 20:00:31', '2025-07-13 20:00:31'),
(23, 9, 'Which animal is known as the \"Ship of the Desert\"?', 'Camel', 'Horse', 'Fox', 'Tiger', 'a', 1, 'medium', 'active', 1, '2025-07-13 20:00:31', '2025-07-13 20:00:31'),
(24, 9, 'Who invented the electric light bulb?', 'Alexander Graham Bell', 'Thomas Edison', 'Isaac Newton', 'Nikola Tesla', 'b', 1, 'medium', 'active', 1, '2025-07-13 20:00:31', '2025-07-13 20:00:31'),
(25, 9, 'Which planet is closest to the Sun?', 'Venus', 'Earth', 'Mercury', 'Mars', 'c', 1, 'medium', 'active', 1, '2025-07-13 20:00:31', '2025-07-13 20:00:31'),
(26, 11, 'What is the capital of Italy?', 'Paris', 'Madrid', 'Rome', 'Athens', 'c', 1, 'medium', 'active', 1, '2025-07-13 20:04:22', '2025-07-13 20:04:22'),
(27, 11, 'How many planets are in the Solar System?', '7', '8', '9', '10', 'b', 1, 'medium', 'active', 1, '2025-07-13 20:04:22', '2025-07-13 20:04:22'),
(28, 11, 'Which animal is known as the \"Ship of the Desert\"?', 'Camel', 'Horse', 'Fox', 'Tiger', 'a', 1, 'medium', 'active', 1, '2025-07-13 20:04:22', '2025-07-13 20:04:22'),
(29, 11, 'Who invented the electric light bulb?', 'Alexander Graham Bell', 'Thomas Edison', 'Isaac Newton', 'Nikola Tesla', 'b', 1, 'medium', 'active', 1, '2025-07-13 20:04:22', '2025-07-13 20:04:22'),
(30, 11, 'Which planet is closest to the Sun?', 'Venus', 'Earth', 'Mercury', 'Mars', 'c', 1, 'medium', 'active', 1, '2025-07-13 20:04:22', '2025-07-13 20:04:22');

-- --------------------------------------------------------

--
-- بنية الجدول `student_answers`
--

CREATE TABLE `student_answers` (
  `id` int(11) NOT NULL,
  `exam_id` int(11) NOT NULL,
  `student_id` varchar(50) NOT NULL,
  `question_id` int(11) NOT NULL,
  `selected_answer` enum('a','b','c','d','') DEFAULT '',
  `attempt_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- إرجاع أو استيراد بيانات الجدول `student_answers`
--

INSERT INTO `student_answers` (`id`, `exam_id`, `student_id`, `question_id`, `selected_answer`, `attempt_id`) VALUES
(89, 11, '1234', 1, '', 35),
(90, 11, '1234', 2, 'c', 35),
(91, 11, '1234', 3, '', 35),
(92, 11, '1234', 4, 'b', 35),
(93, 11, '1234', 5, 'b', 35),
(94, 11, '1234', 8, 'b', 35),
(95, 11, '1234', 9, 'a', 35),
(96, 11, '1234', 11, 'c', 35),
(97, 11, '1234', 12, 'a', 35),
(98, 11, '1234', 13, 'a', 35),
(99, 11, '1234', 14, 'b', 35),
(100, 11, '1234', 15, 'a', 35),
(101, 11, '1234', 16, 'c', 35),
(102, 11, '1234', 17, 'a', 35),
(103, 11, '1234', 18, 'a', 35),
(104, 11, '1234', 19, 'b', 35),
(105, 11, '1234', 20, 'a', 35),
(106, 11, '1234', 21, '', 35),
(107, 11, '1234', 22, 'a', 35),
(108, 11, '1234', 23, '', 35),
(111, 11, '1234', 25, 'a', 35),
(120, 11, '1234', 28, 'a', 35),
(123, 11, '1234', 27, 'a', 35),
(126, 11, '1234', 29, 'b', 35),
(129, 11, '123', 28, 'a', 36),
(130, 11, '123', 16, 'c', 36),
(131, 11, '123', 21, 'c', 36),
(132, 11, '123', 14, 'b', 36),
(133, 11, '123', 17, 'a', 36),
(134, 11, '123', 24, 'b', 36),
(135, 11, '123', 20, 'a', 36),
(136, 11, '123', 25, 'a', 36),
(137, 11, '123', 22, 'a', 36),
(138, 11, '123', 2, 'c', 36),
(139, 11, '123', 19, 'b', 36),
(140, 11, '123', 12, 'a', 36),
(141, 11, '123', 18, 'a', 36),
(142, 11, '123', 9, 'a', 36),
(143, 11, '123', 13, 'a', 36),
(144, 11, '123', 8, 'b', 36),
(145, 11, '123', 26, 'c', 36),
(146, 11, '123', 29, 'b', 36),
(147, 11, '123', 3, 'c', 36),
(148, 11, '123', 15, 'c', 36),
(169, 14, '12345678', 8, 'b', 37),
(170, 14, '12345678', 2, 'c', 37),
(171, 14, '12345678', 26, 'c', 37),
(172, 14, '12345678', 3, 'c', 37),
(173, 14, '12345678', 27, 'a', 37),
(174, 14, '12345678', 4, 'b', 37),
(175, 14, '12345678', 28, '', 37),
(176, 14, '12345678', 29, '', 37),
(177, 14, '12345678', 5, '', 37),
(178, 14, '12345678', 30, '', 37),
(187, 14, '767', 8, 'b', 38),
(188, 14, '767', 2, '', 38),
(189, 14, '767', 26, '', 38),
(190, 14, '767', 3, '', 38),
(191, 14, '767', 27, '', 38),
(192, 14, '767', 4, '', 38),
(193, 14, '767', 28, '', 38),
(194, 14, '767', 29, '', 38),
(195, 14, '767', 5, '', 38),
(196, 14, '767', 30, '', 38),
(205, 14, '1235', 26, '', 39),
(206, 14, '1235', 2, '', 39),
(207, 14, '1235', 3, '', 39),
(208, 14, '1235', 27, '', 39),
(209, 14, '1235', 4, '', 39),
(210, 14, '1235', 28, '', 39),
(211, 14, '1235', 5, '', 39),
(212, 14, '1235', 29, '', 39),
(213, 14, '1235', 30, '', 39),
(214, 14, '1235', 9, '', 39);

-- --------------------------------------------------------

--
-- بنية الجدول `subjects`
--

CREATE TABLE `subjects` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- إرجاع أو استيراد بيانات الجدول `subjects`
--

INSERT INTO `subjects` (`id`, `name`, `status`, `created_by`, `created_at`, `updated_at`) VALUES
(1, 'مقدمة في علوم الحاسب', 'active', 1, '2025-07-13 19:22:42', '2025-07-13 19:22:42'),
(3, 'أمن المعلومات', 'active', 1, '2025-07-13 19:22:42', '2025-07-13 19:22:42'),
(4, 'التحليل الجنائي الرقمي', 'active', 1, '2025-07-13 19:22:42', '2025-07-13 19:22:42'),
(5, 'Mostafa nady', 'active', 1, '2025-07-13 19:22:55', '2025-07-13 19:23:26'),
(6, 'Mostafa', 'active', 1, '2025-07-18 17:21:13', '2025-07-18 17:21:31');

-- --------------------------------------------------------

--
-- بنية الجدول `users`
--

CREATE TABLE `users` (
  `id` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `full_name` varchar(100) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `role` enum('admin','assistant') NOT NULL DEFAULT 'assistant',
  `status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `last_login` datetime DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- إرجاع أو استيراد بيانات الجدول `users`
--

INSERT INTO `users` (`id`, `username`, `password`, `full_name`, `email`, `role`, `status`, `last_login`, `created_at`, `updated_at`) VALUES
(1, 'admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مدير النظام', '<EMAIL>', 'admin', 'active', NULL, '2025-07-13 19:22:42', '2025-07-13 19:22:42');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `activity_logs`
--
ALTER TABLE `activity_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_activity_logs_user` (`user_id`),
  ADD KEY `idx_activity_logs_created` (`created_at`);

--
-- Indexes for table `exams`
--
ALTER TABLE `exams`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `exam_link` (`exam_link`),
  ADD KEY `created_by` (`created_by`),
  ADD KEY `idx_exams_status` (`status`),
  ADD KEY `idx_exams_start_time` (`start_time`),
  ADD KEY `idx_exams_end_time` (`end_time`),
  ADD KEY `idx_exams_subject` (`subject_id`),
  ADD KEY `idx_exam_link` (`exam_link`);

--
-- Indexes for table `exam_lectures`
--
ALTER TABLE `exam_lectures`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_exam_lecture` (`exam_id`,`lecture_id`),
  ADD KEY `lecture_id` (`lecture_id`);

--
-- Indexes for table `exam_questions`
--
ALTER TABLE `exam_questions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_exam_question` (`exam_id`,`question_id`),
  ADD KEY `question_id` (`question_id`);

--
-- Indexes for table `exam_results`
--
ALTER TABLE `exam_results`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_exam_student` (`exam_id`,`student_id`),
  ADD KEY `idx_student` (`student_id`),
  ADD KEY `idx_exam` (`exam_id`);

--
-- Indexes for table `lectures`
--
ALTER TABLE `lectures`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_lecture` (`subject_id`,`lecture_number`),
  ADD KEY `created_by` (`created_by`),
  ADD KEY `idx_lectures_subject` (`subject_id`);

--
-- Indexes for table `notifications`
--
ALTER TABLE `notifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `questions`
--
ALTER TABLE `questions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `created_by` (`created_by`),
  ADD KEY `idx_questions_lecture` (`lecture_id`);

--
-- Indexes for table `student_answers`
--
ALTER TABLE `student_answers`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `idx_attempt_answer` (`exam_id`,`student_id`,`question_id`,`attempt_id`),
  ADD KEY `fk_attempt` (`attempt_id`);

--
-- Indexes for table `subjects`
--
ALTER TABLE `subjects`
  ADD PRIMARY KEY (`id`),
  ADD KEY `created_by` (`created_by`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD UNIQUE KEY `email` (`email`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `activity_logs`
--
ALTER TABLE `activity_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=176;

--
-- AUTO_INCREMENT for table `exams`
--
ALTER TABLE `exams`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=22;

--
-- AUTO_INCREMENT for table `exam_lectures`
--
ALTER TABLE `exam_lectures`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=278;

--
-- AUTO_INCREMENT for table `exam_questions`
--
ALTER TABLE `exam_questions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=153;

--
-- AUTO_INCREMENT for table `exam_results`
--
ALTER TABLE `exam_results`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=40;

--
-- AUTO_INCREMENT for table `lectures`
--
ALTER TABLE `lectures`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;

--
-- AUTO_INCREMENT for table `notifications`
--
ALTER TABLE `notifications`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `questions`
--
ALTER TABLE `questions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=31;

--
-- AUTO_INCREMENT for table `student_answers`
--
ALTER TABLE `student_answers`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=215;

--
-- AUTO_INCREMENT for table `subjects`
--
ALTER TABLE `subjects`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- قيود الجداول المُلقاة.
--

--
-- قيود الجداول `activity_logs`
--
ALTER TABLE `activity_logs`
  ADD CONSTRAINT `activity_logs_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- قيود الجداول `exams`
--
ALTER TABLE `exams`
  ADD CONSTRAINT `exams_ibfk_1` FOREIGN KEY (`subject_id`) REFERENCES `subjects` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `exams_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- قيود الجداول `exam_lectures`
--
ALTER TABLE `exam_lectures`
  ADD CONSTRAINT `exam_lectures_ibfk_1` FOREIGN KEY (`exam_id`) REFERENCES `exams` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `exam_lectures_ibfk_2` FOREIGN KEY (`lecture_id`) REFERENCES `lectures` (`id`) ON DELETE CASCADE;

--
-- قيود الجداول `exam_questions`
--
ALTER TABLE `exam_questions`
  ADD CONSTRAINT `exam_questions_ibfk_1` FOREIGN KEY (`exam_id`) REFERENCES `exams` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `exam_questions_ibfk_2` FOREIGN KEY (`question_id`) REFERENCES `questions` (`id`) ON DELETE CASCADE;

--
-- قيود الجداول `exam_results`
--
ALTER TABLE `exam_results`
  ADD CONSTRAINT `exam_results_ibfk_1` FOREIGN KEY (`exam_id`) REFERENCES `exams` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- قيود الجداول `lectures`
--
ALTER TABLE `lectures`
  ADD CONSTRAINT `lectures_ibfk_1` FOREIGN KEY (`subject_id`) REFERENCES `subjects` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `lectures_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- قيود الجداول `notifications`
--
ALTER TABLE `notifications`
  ADD CONSTRAINT `notifications_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- قيود الجداول `questions`
--
ALTER TABLE `questions`
  ADD CONSTRAINT `questions_ibfk_1` FOREIGN KEY (`lecture_id`) REFERENCES `lectures` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `questions_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- قيود الجداول `student_answers`
--
ALTER TABLE `student_answers`
  ADD CONSTRAINT `fk_attempt` FOREIGN KEY (`attempt_id`) REFERENCES `exam_results` (`id`) ON DELETE CASCADE;

--
-- قيود الجداول `subjects`
--
ALTER TABLE `subjects`
  ADD CONSTRAINT `subjects_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
