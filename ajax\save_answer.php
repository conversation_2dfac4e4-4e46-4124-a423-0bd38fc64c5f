<?php

ob_start();

// إعدادات الأمان
ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);
error_reporting(0);

// تعيين header للـ JSON
header('Content-Type: application/json; charset=utf-8');

// تنظيف أي output سابق
ob_clean();

session_start();

// تطبيق headers الأمنية
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');

// تشخيص أولي
error_log("save_answer.php called - Method: " . $_SERVER['REQUEST_METHOD']);
error_log("save_answer.php - POST data: " . json_encode($_POST));

// فحص طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'error' => 'طريقة طلب غير صحيحة', 'method' => $_SERVER['REQUEST_METHOD']]);
    exit();
}

// فحص Content-Type
$content_type = $_SERVER['CONTENT_TYPE'] ?? '';
if (strpos($content_type, 'application/x-www-form-urlencoded') === false &&
    strpos($content_type, 'multipart/form-data') === false) {
    echo json_encode(['success' => false, 'error' => 'نوع المحتوى غير صحيح']);
    exit();
}

// محاولة تحميل الملفات مع معالجة الأخطاء
try {
    require_once '../config/database.php';
} catch (Exception $e) {
    echo json_encode(['success' => false, 'error' => 'Database config error: ' . $e->getMessage()]);
    exit();
}

try {
    require_once '../includes/functions.php';
} catch (Exception $e) {
    // تجاهل خطأ functions.php إذا لم يكن موجود
    // echo json_encode(['success' => false, 'error' => 'Functions error: ' . $e->getMessage()]);
    // exit();
}

// تم تعطيل Rate Limiting مؤقتاً
// $client_ip = $_SERVER['REMOTE_ADDR'];
// $rate_limit_key = 'ajax_' . $client_ip;

// تشخيص الجلسة
error_log("save_answer.php - Session ID: " . session_id());
error_log("save_answer.php - Session data exists: " . (isset($_SESSION['exam_data']) ? 'yes' : 'no'));

if (!isset($_SESSION['exam_data']) || !isset($_SESSION['exam_data']['exam_id']) || !isset($_SESSION['exam_data']['student_id'])) {
    $session_debug = [
        'session_id' => session_id(),
        'exam_data_exists' => isset($_SESSION['exam_data']),
        'session_keys' => array_keys($_SESSION ?? [])
    ];

    echo json_encode([
        'success' => false,
        'error' => 'جلسة الامتحان غير صالحة',
        'debug' => $session_debug
    ]);
    exit();
}

if (!isset($_POST['question_id']) || !isset($_POST['answer'])) {
    echo json_encode([
        'success' => false,
        'error' => 'بيانات غير مكتملة',
        'received_data' => [
            'question_id' => isset($_POST['question_id']) ? 'exists' : 'missing',
            'answer' => isset($_POST['answer']) ? 'exists' : 'missing'
        ]
    ]);
    exit();
}

$exam_data = $_SESSION['exam_data'];
$question_id = (int)$_POST['question_id'];
$answer = $_POST['answer'];

if (!in_array($answer, ['a','b','c','d'])) {
    echo json_encode(['success' => false, 'error' => 'إجابة غير صحيحة']);
    exit();
}

try {
    $pdo->beginTransaction();

    $stmt = $pdo->prepare("
        SELECT er.*, e.duration_minutes
        FROM exam_results er
        JOIN exams e ON er.exam_id = e.id
        WHERE er.id = ?
        AND er.exam_id = ?
        AND er.student_id = ?
        AND er.end_time IS NULL
    ");
    $stmt->execute([$exam_data['attempt_id'], $exam_data['exam_id'], $exam_data['student_id']]);
    $attempt = $stmt->fetch();

    if (!$attempt) {
        throw new Exception('محاولة الامتحان غير صالحة');
    }

    $start_time = new DateTime($attempt['start_time']);
    $current_time = new DateTime();
    $time_elapsed = $current_time->getTimestamp() - $start_time->getTimestamp();
    $time_remaining = ($attempt['duration_minutes'] * 60) - $time_elapsed;

    if ($time_remaining <= 0) {

        $stmt = $pdo->prepare("UPDATE exam_results SET end_time = NOW() WHERE id = ?");
        $stmt->execute([$attempt['id']]);
        $pdo->commit();
        echo json_encode(['success' => false, 'error' => 'انتهى وقت الامتحان', 'timeout' => true]);
        exit();
    }

    $stmt = $pdo->prepare("
        SELECT q.*, eq.question_order
        FROM questions q
        JOIN exam_questions eq ON q.id = eq.question_id
        WHERE q.id = ? AND eq.exam_id = ? AND q.status = 'active'
    ");
    $stmt->execute([$question_id, $exam_data['exam_id']]);
    $question = $stmt->fetch();

    if (!$question) {
        throw new Exception('السؤال غير موجود أو غير نشط');
    }

    $stmt = $pdo->prepare("
        INSERT INTO student_answers
            (exam_id, student_id, question_id, selected_answer, attempt_id)
        VALUES
            (?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
            selected_answer = VALUES(selected_answer)
    ");

    $stmt->execute([
        $exam_data['exam_id'],
        $exam_data['student_id'],
        $question_id,
        $answer,
        $exam_data['attempt_id']
    ]);

    $pdo->commit();

    if (ob_get_level()) {
        ob_clean();
    }

    echo json_encode([
        'success' => true,
        'stats' => [
            'time_remaining' => $time_remaining
        ]
    ]);

} catch (Exception $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    error_log("Error in save_answer.php: " . $e->getMessage());

    if (ob_get_level()) {
        ob_clean();
    }

    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
} catch (PDOException $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    error_log("Database Error in save_answer.php: " . $e->getMessage());

    if (ob_get_level()) {
        ob_clean();
    }

    echo json_encode([
        'success' => false,
        'error' => 'خطأ في قاعدة البيانات'
    ]);
}

// تنظيف output buffer وإرسال JSON فقط
ob_clean();
ob_end_flush();