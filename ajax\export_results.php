<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit();
}

$exam_id = isset($_GET['exam_id']) ? (int)$_GET['exam_id'] : 0;
$exam_type = isset($_GET['exam_type']) ? sanitize($_GET['exam_type']) : '';

try {
    $where_conditions = [];
    $params = [];

    if ($exam_id > 0) {
        $where_conditions[] = "er.exam_id = ?";
        $params[] = $exam_id;
    }

    if (!empty($exam_type)) {
        $where_conditions[] = "e.exam_type = ?";
        $params[] = $exam_type;
    }

    $where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";

    $sql = "SELECT
                er.*,
                e.title as exam_title,
                e.exam_type,
                e.passing_percentage,
                s.name as subject_name,
                COALESCE(
                    ROUND(
                        (SELECT SUM(CASE WHEN sa.selected_answer COLLATE utf8mb4_unicode_ci = q.correct_answer THEN q.points ELSE 0 END)
                         FROM student_answers sa
                         JOIN questions q ON sa.question_id = q.id
                         WHERE sa.exam_id = er.exam_id AND sa.student_id COLLATE utf8mb4_unicode_ci = er.student_id AND sa.attempt_id = er.id) * 100.0 /
                        NULLIF((SELECT SUM(q.points)
                                FROM student_answers sa
                                JOIN questions q ON sa.question_id = q.id
                                WHERE sa.exam_id = er.exam_id AND sa.student_id COLLATE utf8mb4_unicode_ci = er.student_id AND sa.attempt_id = er.id), 0),
                        2
                    ), 0
                ) as percentage
            FROM exam_results er
            LEFT JOIN exams e ON er.exam_id = e.id
            LEFT JOIN subjects s ON e.subject_id = s.id
            $where_clause
            ORDER BY er.submitted_at DESC";

    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $results = $stmt->fetchAll();

    if (empty($results)) {
        echo json_encode([
            'success' => false,
            'message' => 'لا توجد نتائج للتصدير'
        ]);
        exit();
    }

    $filename = 'exam_results_' . date('Y-m-d_H-i-s') . '.csv';
    $filepath = '../exports/' . $filename;

    if (!is_dir('../exports')) {
        mkdir('../exports', 0755, true);
    }

    $file = fopen($filepath, 'w');

    fputcsv($file, [
        'رقم الطالب',
        'اسم الطالب',
        'السكشن',
        'عنوان الامتحان',
        'نوع الامتحان',
        'المادة',
        'تاريخ الإرسال',
        'مدة الامتحان',
        'النسبة المئوية',
        'الحالة'
    ]);

    foreach ($results as $result) {
        $percentage = isset($result['percentage']) ? (float)$result['percentage'] : 0;
        $passing_percentage = isset($result['passing_percentage']) ? (float)$result['passing_percentage'] : 60;
        $is_passed = $percentage >= $passing_percentage;

        fputcsv($file, [
            $result['student_id'],
            $result['student_name'],
            $result['section_number'],
            $result['exam_title'],
            $result['exam_type'] === 'exam' ? 'امتحان' : 'كويز',
            $result['subject_name'],
            $result['submitted_at'],
            $result['duration_minutes'] . ' دقيقة',
            number_format($percentage, 1) . '%',
            $is_passed ? 'ناجح' : 'راسب'
        ]);
    }

    fclose($file);

    logActivity($_SESSION['user_id'], 'export_results', "تصدير نتائج الامتحانات: $filename");

    echo json_encode([
        'success' => true,
        'message' => 'تم تصدير النتائج بنجاح',
        'filename' => $filename,
        'download_url' => 'exports/' . $filename
    ]);

} catch (PDOException $e) {
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في قاعدة البيانات'
    ]);
}
?>