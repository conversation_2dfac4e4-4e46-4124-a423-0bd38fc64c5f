<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

if (!isLoggedIn()) {
    header('Location: ../index.php');
    exit();
}

if (!isAdmin() && !isAssistant()) {
    header('Location: ../index.php');
    exit();
}

$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    if (!verifyCSRFToken($_POST['csrf_token'])) {
        $error = 'خطأ في التحقق من الأمان';
    } else {
        if ($_POST['action'] === 'delete') {
            $id = (int)$_POST['id'];

            try {
                $stmt = $pdo->prepare("DELETE FROM exam_results WHERE id = ?");
                if ($stmt->execute([$id])) {
                    $message = 'تم حذف النتيجة بنجاح';
                    logActivity($_SESSION['user_id'], 'delete_result', "حذف نتيجة برقم: $id");
                } else {
                    $error = 'خطأ في حذف النتيجة';
                }
            } catch (PDOException $e) {
                $error = 'خطأ في قاعدة البيانات';
            }
        }
    }
}

$exam_id = isset($_GET['exam_id']) ? (int)$_GET['exam_id'] : 0;

try {
    if ($exam_id > 0) {

        $stmt = $pdo->prepare("SELECT er.*, e.title as exam_title, e.passing_percentage, e.exam_link, er.student_name, er.student_id as student_email,
                            COALESCE(
                                (SELECT SUM(CASE WHEN sa.selected_answer COLLATE utf8mb4_unicode_ci = q.correct_answer THEN q.points ELSE 0 END)
                                 FROM student_answers sa
                                 JOIN questions q ON sa.question_id = q.id
                                 WHERE sa.exam_id = er.exam_id AND sa.student_id COLLATE utf8mb4_unicode_ci = er.student_id AND sa.attempt_id = er.id), 0
                            ) as earned_points,
                            COALESCE(
                                (SELECT SUM(q.points)
                                 FROM student_answers sa
                                 JOIN questions q ON sa.question_id = q.id
                                 WHERE sa.exam_id = er.exam_id AND sa.student_id COLLATE utf8mb4_unicode_ci = er.student_id AND sa.attempt_id = er.id), 0
                            ) as total_points
                            FROM exam_results er
                            LEFT JOIN exams e ON er.exam_id = e.id
                            WHERE er.exam_id = ?
                            ORDER BY er.end_time DESC");
        $stmt->execute([$exam_id]);
    } else {

        $stmt = $pdo->query("SELECT er.*, e.title as exam_title, e.passing_percentage, e.exam_link, er.student_name, er.student_id as student_email,
                            COALESCE(
                                (SELECT SUM(CASE WHEN sa.selected_answer COLLATE utf8mb4_unicode_ci = q.correct_answer THEN q.points ELSE 0 END)
                                 FROM student_answers sa
                                 JOIN questions q ON sa.question_id = q.id
                                 WHERE sa.exam_id = er.exam_id AND sa.student_id COLLATE utf8mb4_unicode_ci = er.student_id AND sa.attempt_id = er.id), 0
                            ) as earned_points,
                            COALESCE(
                                (SELECT SUM(q.points)
                                 FROM student_answers sa
                                 JOIN questions q ON sa.question_id = q.id
                                 WHERE sa.exam_id = er.exam_id AND sa.student_id COLLATE utf8mb4_unicode_ci = er.student_id AND sa.attempt_id = er.id), 0
                            ) as total_points
                            FROM exam_results er
                            LEFT JOIN exams e ON er.exam_id = e.id
                            ORDER BY er.end_time DESC");
    }
    $results = $stmt->fetchAll();

} catch (PDOException $e) {
    $error = 'خطأ في تحميل البيانات: ' . $e->getMessage();
    error_log('Exam results error: ' . $e->getMessage());
}

$csrf_token = generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>نتائج الامتحانات - نظام الامتحانات الإلكترونية</title>
    <link <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;500;600&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="../assets/css/mobile.css" rel="stylesheet">
    <style>
        body {
            background: #183046;
            min-height: 100vh;
            font-family: 'Cairo', sans-serif;
            overflow-x: hidden;
            overflow-y: auto;
        }
        .navbar {
            background: #fff !important;
            box-shadow: 0 2px 10px rgba(0,0,0,0.07);
            border-bottom: 1px solid #e3e6f0;
        }
        .navbar-brand {
            font-weight: 700;
            font-size: 1.2rem;
            color: #2c3e50 !important;
            line-height: 1.2;
            display: flex;
            align-items: center;
        }

        .brand-text {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .nav-link, .navbar-nav .nav-link {
            color: #2c3e50 !important;
            font-weight: 600;
            border-radius: 25px;
            margin: 0 0.3rem;
        }
        .nav-link.active, .nav-link:hover {
            background: #2196f3;
            color: #fff !important;
        }
        .exams-header {
            margin-top: 2.5rem;
            margin-bottom: 2.5rem;
            text-align: right;
        }
        .exams-title {
            font-size: 2.3rem;
            font-weight: 900;
            color: #fff;
            margin-bottom: 0.5rem;
        }
        .exams-subtitle {
            color: #e0e0e0;
            font-size: 1.1rem;
            font-weight: 400;
        }
        .modern-card {
            background: #fff;
            border-radius: 16px;
            box-shadow: 0 4px 24px rgba(102,126,234,0.08);
            padding: 1.5rem 1.2rem;
            margin-bottom: 2rem;
        }
        .modern-card .card-header {
            background: none;
            border: none;
            padding: 0 0 1rem 0;
            color: #2196f3;
            font-weight: 700;
            font-size: 1.2rem;
        }
        .modern-card .list-group-item {
            border: none;
            border-bottom: 1px solid #f0f0f0;
            padding: 0.7rem 0.2rem;
        }
        .modern-card .list-group-item:last-child {
            border-bottom: none;
        }
        .card.border-0.shadow-sm {
            border-radius: 18px;
            background: #fff;
            box-shadow: 0 8px 32px rgba(102,126,234,0.08);
            padding: 1.5rem 1.2rem;
        }
        .card-header.bg-primary {
            background: #2196f3 !important;
            color: #fff !important;
            border-radius: 16px 16px 0 0 !important;
            font-weight: 700;
            font-size: 1.2rem;
        }
        .btn-primary, .btn-outline-primary:hover {
            background: #2196f3 !important;
            border-color: #2196f3 !important;
            color: #fff !important;
        }
        .btn-outline-primary {
            color: #2196f3 !important;
            border-color: #2196f3 !important;
            background: #fff !important;
        }
        .btn-outline-primary:focus, .btn-outline-primary:active {
            background: #2196f3 !important;
            color: #fff !important;
        }
        .table {
            background: #fff;
            border-radius: 12px;
            overflow: hidden;
        }
        .table th, .table td {
            vertical-align: middle;
        }

        .grade-display {
            min-width: 120px;
        }

        .grade-display .badge {
            font-size: 0.9em;
            padding: 6px 12px;
        }

        .grade-display strong {
            font-size: 1.1em;
        }

        .grade-display small {
            opacity: 0.8;
        }

        .total-points-badge {
            font-size: 0.85em;
            padding: 5px 10px;
        }
        .modal-content {
            border-radius: 16px;
            border: none;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
        }
        .modal-header {
            border-bottom: 1px solid #f0f0f0;
            border-radius: 16px 16px 0 0;
        }
        .modal-title {
            color: #2196f3;
            font-weight: 700;
        }
        .form-label {
            font-weight: 600;
            color: #2c3e50;
        }
        .form-control, .form-select {
            border-radius: 10px;
            border: 1px solid #e3e6f0;
        }
        .form-control:focus, .form-select:focus {
            border-color: #2196f3;
            box-shadow: 0 0 0 0.2rem rgba(33, 150, 243, 0.25);
        }
        .btn-close {
            margin: 0;
        }
        .card {
            border: 1px solid #e3e6f0;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }
        .card-header {
            background: #f8f9fa;
            border-bottom: 1px solid #e3e6f0;
            border-radius: 12px 12px 0 0;
        }
        .form-check-input:checked {
            background-color: #2196f3;
            border-color: #2196f3;
        }
        .form-check-label {
            cursor: pointer;
        }
        .modal-xl {
            max-width: 90%;
        }
        .lecture-checkbox {
            border: 1px solid #e3e6f0;
            border-radius: 8px;
            padding: 0.5rem;
            margin-bottom: 0.5rem;
            transition: all 0.3s ease;
        }
        .lecture-checkbox:hover {
            background-color: #f8f9fa;
            border-color: #2196f3;
        }
        .lecture-checkbox .form-check-label {
            cursor: pointer;
            width: 100%;
        }
        .lecture-checkbox .form-check-input:checked + .form-check-label {
            color: #2196f3;
            font-weight: 600;
        }
        .footer {
            background: #2c3e50;
            color: #fff;
            padding: 2.5rem 0 1rem;
            margin-top: 3rem;
        }
        .footer-title {
            font-size: 1.2rem;
            font-weight: 700;
        }
        .footer-bottom {
            border-top: 1px solid rgba(255,255,255,0.1);
            padding-top: 1rem;
            text-align: center;
            opacity: 0.7;
        }
        @media (max-width: 991px) {
            .exams-header {margin-top: 1.5rem;}
        }

        @media (max-width: 768px) {

            .exams-header .d-flex {
                flex-direction: column;
                align-items: flex-start !important;
                gap: 1rem;
            }

            .exams-header .d-flex > div:last-child {
                width: 100%;
                display: flex;
                flex-direction: column;
                gap: 0.5rem;
            }

            .exams-header .btn {
                width: 100%;
                justify-content: center;
                padding: 0.75rem 1rem;
                font-size: 0.9rem;
                border-radius: 8px;
            }

            .exams-header .btn i {
                margin-left: 0.5rem;
            }

            .exams-title {
                font-size: 1.5rem;
                margin-bottom: 0.5rem;
                text-align: center;
            }

            .exams-subtitle {
                font-size: 0.9rem;
                margin-bottom: 1rem;
                text-align: center;
            }

            .table-responsive {
                border: none;
                margin-bottom: 1rem;
                border-radius: 12px;
                overflow: hidden;
            }

            .table {
                font-size: 0.85rem;
                margin-bottom: 0;
            }

            .table th,
            .table td {
                padding: 0.75rem 0.5rem;
                vertical-align: middle;
            }

            .table th {
                background-color: #f8f9fa;
                font-weight: 600;
                font-size: 0.8rem;
                text-align: center;
            }

            .table .btn {
                padding: 0.4rem 0.6rem;
                font-size: 0.75rem;
                margin: 0.1rem;
                border-radius: 6px;
                min-width: 60px;
            }

            .table .btn i {
                font-size: 0.7rem;
            }

            .table .btn-group {
                display: flex;
                flex-direction: column;
                gap: 0.3rem;
                width: 100%;
            }

            .table .btn-group .btn {
                width: 100%;
                margin: 0;
            }

            .card {
                margin-bottom: 1.5rem;
                border-radius: 12px;
                border: none;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }

            .card-header {
                padding: 1.25rem;
                font-size: 1rem;
                font-weight: 600;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border-radius: 12px 12px 0 0 !important;
                text-align: center;
            }

            .card-body {
                padding: 1.25rem;
            }

            .badge {
                font-size: 0.75rem;
                padding: 0.4rem 0.8rem;
                border-radius: 20px;
            }

            .alert {
                border-radius: 10px;
                padding: 1rem;
                font-size: 0.9rem;
                border: none;
                margin-bottom: 1.5rem;
            }

            .alert .btn-close {
                padding: 0.5rem;
            }

            .progress {
                height: 25px;
                border-radius: 12px;
                background-color: #e9ecef;
            }

            .progress-bar {
                font-size: 0.85rem;
                font-weight: 600;
                border-radius: 12px;
            }

            .stats-card {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border-radius: 12px;
                padding: 1.5rem;
                margin-bottom: 1rem;
                text-align: center;
            }

            .stats-number {
                font-size: 2rem;
                font-weight: 700;
                margin-bottom: 0.5rem;
            }

            .stats-label {
                font-size: 0.9rem;
                opacity: 0.9;
            }
        }

        @media (max-width: 576px) {
            .exams-header .btn {
                font-size: 0.85rem;
                padding: 0.6rem 0.8rem;
            }

            .exams-title {
                font-size: 1.3rem;
            }

            .exams-subtitle {
                font-size: 0.8rem;
            }

            .table {
                font-size: 0.8rem;
            }

            .table th,
            .table td {
                padding: 0.4rem 0.2rem;
            }

            .table .btn {
                padding: 0.2rem 0.4rem;
                font-size: 0.7rem;
            }

            .card-header {
                padding: 0.8rem;
                font-size: 0.95rem;
            }

            .card-body {
                padding: 0.8rem;
            }

            .badge {
                font-size: 0.65rem;
                padding: 0.25rem 0.5rem;
            }

            .alert {
                padding: 0.8rem;
                font-size: 0.85rem;
            }
        }

        @media (max-width: 768px) {
            .container {
                padding-left: 15px;
                padding-right: 15px;
            }

            .btn:active {
                transform: scale(0.98);
            }

            .card:hover {
                transform: none;
            }

            .table-hover tbody tr:hover {
                background-color: rgba(0, 123, 255, 0.05);
            }
        }

        @media (max-width: 576px) {
            .container {
                padding-left: 10px;
                padding-right: 10px;
            }
        }
    </style>
</head>
<body>
    <?php include '../includes/mobile_navbar.php'; ?>
    <div class="container" style="margin-top: 80px;">
        <div class="exams-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="exams-title">
                        <i class="fas fa-chart-bar text-primary me-2"></i>
                        نتائج الامتحانات
                        <?php if ($exam_id > 0 && !empty($results)): ?>
                            <small class="text-muted">- <?php echo htmlspecialchars($results[0]['exam_title']); ?></small>
                        <?php endif; ?>
                    </div>
                    <div class="exams-subtitle">
                        <?php if ($exam_id > 0): ?>
                            عرض نتائج امتحان محدد - قسم الـ Forensic - جامعة المنيا
                        <?php else: ?>
                            عرض وإدارة نتائج امتحانات قسم الـ Forensic - جامعة المنيا
                        <?php endif; ?>
                    </div>
                </div>
                <div>
                    <a href="exams.php" class="btn btn-outline-primary me-2">
                        <i class="fas fa-arrow-left me-2"></i>
                        رجوع للامتحانات
                    </a>
                    <a href="results.php" class="btn btn-primary">
                        <i class="fas fa-list me-2"></i>
                        عرض النتائج المفصلة
                    </a>
                </div>
            </div>
        </div>

        <!-- Messages -->
        <?php if ($message): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo $message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?php echo $error; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Results Table -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    قائمة نتائج الامتحانات
                    <?php if (!empty($results)): ?>
                        <span class="badge bg-light text-dark ms-2"><?php echo count($results); ?> نتيجة</span>
                    <?php endif; ?>
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($results)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-chart-bar text-muted" style="font-size: 3rem;"></i>
                        <p class="text-muted mt-3">لا توجد نتائج امتحانات بعد</p>
                        <p class="text-muted">ستظهر هنا نتائج الامتحانات بعد أن يقوم الطلاب بحلها</p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover" id="resultsTable">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>اسم الطالب</th>
                                    <th>رقم الطالب</th>
                                    <th>عنوان الامتحان</th>
                                    <th>الدرجة الكلية</th>
                                    <th>نتيجة الطالب</th>
                                    <th>وقت التسليم</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($results as $index => $result): ?>
                                    <tr>
                                        <td><?php echo $index + 1; ?></td>
                                        <td>
                                            <strong><?php echo htmlspecialchars($result['student_name']); ?></strong>
                                        </td>
                                        <td><?php echo htmlspecialchars($result['student_id']); ?></td>
                                        <td>
                                            <strong><?php echo htmlspecialchars($result['exam_title']); ?></strong>
                                        </td>
                                        <td class="text-center">
                                            <?php
                                                $total_points = isset($result['total_points']) ? (int)$result['total_points'] : 0;
                                            ?>
                                            <span class="badge bg-info total-points-badge">
                                                <i class="fas fa-star me-1"></i><?php echo $total_points; ?> نقطة
                                            </span>
                                        </td>
                                        <td class="text-center grade-display">
                                            <?php
                                                $earned_points = isset($result['earned_points']) ? (int)$result['earned_points'] : 0;
                                                $percentage = $total_points > 0 ? ($earned_points / $total_points) * 100 : 0;
                                                $passing_percentage = isset($result['passing_percentage']) ? (float)$result['passing_percentage'] : 60;
                                                $is_passed = $percentage >= $passing_percentage;
                                            ?>
                                            <div class="d-flex flex-column align-items-center">
                                                <span class="badge bg-<?php echo $is_passed ? 'success' : 'danger'; ?> mb-1">
                                                    <strong><?php echo $earned_points; ?></strong>/<small><?php echo $total_points; ?></small>
                                                </span>
                                                <small class="text-<?php echo $is_passed ? 'success' : 'danger'; ?> fw-bold">
                                                    <?php echo number_format($percentage, 1); ?>%
                                                    <?php if ($is_passed): ?>
                                                        <i class="fas fa-check-circle ms-1"></i>
                                                    <?php else: ?>
                                                        <i class="fas fa-times-circle ms-1"></i>
                                                    <?php endif; ?>
                                                </small>
                                            </div>
                                        </td>
                                        <td><?php echo date('Y-m-d H:i', strtotime($result['end_time'])); ?></td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="../exam_result.php/<?php echo htmlspecialchars($result['exam_link']); ?>/<?php echo $result['id']; ?>/<?php echo htmlspecialchars($result['student_id']); ?>" class="btn btn-sm btn-outline-primary" title="عرض التفاصيل">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <button type="button" class="btn btn-sm btn-outline-danger" title="حذف" onclick="deleteResult(<?php echo $result['id']; ?>)">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <div class="footer-title">
                        <i class="fas fa-graduation-cap me-2"></i>
                        جامعة المنيا - قسم الـ Forensic
                    </div>
                    <p class="mt-3">نظام الامتحانات الإلكترونية المتطور لإدارة وتقييم الطلاب</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <div class="footer-title">روابط سريعة</div>
                    <ul class="list-unstyled mt-3">
                        <li><a href="dashboard.php" class="text-light text-decoration-none">لوحة التحكم</a></li>
                        <li><a href="subjects.php" class="text-light text-decoration-none">المواد</a></li>
                        <li><a href="exams.php" class="text-light text-decoration-none">الامتحانات</a></li>
                        <li><a href="results.php" class="text-light text-decoration-none">النتائج</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p class="mb-0">جميع الحقوق محفوظة &copy; <?php echo date('Y'); ?> جامعة المنيا - قسم الـ Forensic</p>
            </div>
        </div>
    </footer>

    <script src="https:
    <script>
        function deleteResult(id) {
            if (confirm('هل أنت متأكد من حذف هذه النتيجة؟')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                    <input type="hidden" name="action" value="delete">
                    <input type="hidden" name="id" value="${id}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }
    </script>
    <script src="../assets/js/mobile.js"></script>
</body>
</html>