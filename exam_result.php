<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

$error = '';
$exam = null;
$result = null;

$exam_link = '';
$student_id = '';
$attempt_id = null;

// جلب exam_link من GET parameter أو PATH_INFO
if (isset($_GET['link'])) {
    $exam_link = $_GET['link'];
} else {
    $path_info = $_SERVER['PATH_INFO'] ?? '';
    if ($path_info && preg_match('~/([a-zA-Z0-9_]+)$~', $path_info, $matches)) {
        $exam_link = $matches[1];
    }

    if (empty($exam_link)) {
        $request_uri = $_SERVER['REQUEST_URI'] ?? '';
        if (preg_match('~/exam_result\.php/([a-zA-Z0-9_]+)~', $request_uri, $matches)) {
            $exam_link = $matches[1];
        }
    }
}

// جلب student_id و attempt_id من session أو GET parameters
if (isset($_SESSION['exam_data'])) {
    $student_id = $_SESSION['exam_data']['student_id'] ?? '';
    $attempt_id = $_SESSION['exam_data']['attempt_id'] ?? null;
}

// إذا لم نجدهم في session، جرب GET parameters
if (empty($student_id) && isset($_GET['student_id'])) {
    $student_id = $_GET['student_id'];
}
if (!$attempt_id && isset($_GET['attempt_id'])) {
    $attempt_id = (int)$_GET['attempt_id'];
}

if (empty($exam_link)) {
    $error = 'رابط الامتحان مطلوب';
} elseif (empty($student_id)) {
    $error = 'معرف الطالب مطلوب';
}

if (!$error && (!empty($exam_link) && !empty($student_id))) {

    try {

        $stmt = $pdo->prepare("SELECT * FROM exams WHERE exam_link = ?");
        $stmt->execute([$exam_link]);
        $exam = $stmt->fetch();

        if (!$exam) {
            $error = 'الامتحان غير موجود';
        } else {

            if ($attempt_id) {
                $stmt = $pdo->prepare("SELECT * FROM exam_results WHERE exam_id = ? AND student_id = ? AND id = ?");
                $stmt->execute([$exam['id'], $student_id, $attempt_id]);
            } else {
                $stmt = $pdo->prepare("SELECT * FROM exam_results WHERE exam_id = ? AND student_id = ? ORDER BY id DESC LIMIT 1");
                $stmt->execute([$exam['id'], $student_id]);
            }
            $result = $stmt->fetch();

            if (!$result) {
                $error = 'النتيجة غير موجودة';
            }
        }
    } catch (PDOException $e) {
        $error = 'خطأ في تحميل النتيجة';
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نتيجة الامتحان - جامعة المنيا</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;500;600&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <style>
        .result-card {
            background: #fff;
            border-radius: 16px;
            padding: 2rem;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            border: 1px solid #e3e6ea;
        }
        .result-percentage {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.3rem;
        }
        .stat-box {
            background: #fff;
            border-radius: 12px;
            padding: 1.2rem;
            text-align: center;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            border: 1px solid #e3e6ea;
            height: 100%;
        }
        .stat-value {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: #1565c0;
        }
        .stat-label {
            color: #666;
            font-size: 0.95rem;
            font-weight: 500;
        }
        .result-header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1.5rem;
            border-bottom: 1px solid #e3e6ea;
        }
        .result-header .icon {
            font-size: 3.5rem;
            margin-bottom: 1rem;
            color: #1565c0;
        }
        .result-header .title {
            font-size: 1.6rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }
        .result-header .subtitle {
            color: #666;
            font-size: 1.1rem;
        }
        .student-info {
            background: #f8fafc;
            border-radius: 12px;
            padding: 1.2rem;
            margin-bottom: 2rem;
        }
        .student-info .info-item {
            margin-bottom: 0.8rem;
            display: flex;
            align-items: center;
        }
        .student-info .info-label {
            color: #666;
            font-weight: 600;
            margin-left: 0.5rem;
            min-width: 120px;
        }
        .student-info .info-value {
            color: #2c3e50;
            font-weight: 500;
        }
        .pass-status {
            text-align: center;
            padding: 1rem;
            border-radius: 12px;
            margin: 2rem 0;
        }
        .pass-status.passed {
            background: #e8f5e9;
            border: 1px solid #a5d6a7;
            color: #2e7d32;
        }
        .pass-status.failed {
            background: #fdecea;
            border: 1px solid #ef9a9a;
            color: #c62828;
        }
        .pass-status .status-icon {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }
        .pass-status .status-text {
            font-size: 1.3rem;
            font-weight: 700;
            margin-bottom: 0.3rem;
        }
        .pass-status .status-subtext {
            font-size: 0.95rem;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="header-bar" style="background:#003366;color:#fff;font-size:1.3rem;font-weight:bold;text-align:center;padding:0.8rem 0 0.6rem 0;letter-spacing:1px;border-bottom:3px solid #e3e6ea;box-shadow:0 2px 8px #00336622;">
        <div style="font-size:1.08em;font-weight:700;letter-spacing:0.5px;line-height:1.2;">
            <i class="fas fa-graduation-cap ms-2" style="font-size:1.1em;"></i>
            جامعة المنيا - قسم الـ Forensic
        </div>
    </div>
    <?php if ($exam): ?>
    <div style="background:linear-gradient(90deg,#e3f0ff 0%,#f6fafd 100%);border-bottom:2px solid #e3e6ea;padding:1.1rem 0 0.7rem 0;text-align:center;box-shadow:0 2px 8px #00336611;">
        <span style="font-size:1.35rem;font-weight:800;color:#1565c0;letter-spacing:0.5px;">
            <i class="fas fa-file-alt me-2" style="color:#1565c0;"></i>
            <?php echo htmlspecialchars($exam['title']); ?>
        </span>
        <?php if (!empty($exam['exam_type'])): ?>
        <span style="font-size:1.08rem;font-weight:600;color:#00796b;background:#e0f7fa;border-radius:8px;padding:0.2em 1.1em;margin-right:1.2em;display:inline-block;">
            <i class="fas fa-list-ul me-1"></i>
            <?php echo htmlspecialchars($exam['exam_type']); ?>
        </span>
        <?php endif; ?>
    </div>
    <?php
    $can_show_result = false;
    if (!empty($exam)) {
        if (!empty($exam['show_results_immediately']) && $exam['show_results_immediately']) {
            $can_show_result = true;
        } elseif (!empty($exam['show_results_after_end']) && $exam['show_results_after_end']) {

            $now = time();
            $end_time = isset($result['end_time']) ? strtotime($result['end_time']) : null;
            if ($end_time && $now > $end_time) {
                $can_show_result = true;
            }
        }
    }
    ?>
    <?php if ($can_show_result): ?>
    <div class="container" style="max-width:700px;">
        <div class="card shadow-sm mt-4 mb-3" style="border-radius:16px;background:#f8fbff;border:1.5px solid #e3e6ea;">
            <div class="card-body d-flex flex-column align-items-center p-4">
                <div class="avatar mb-2" style="width:60px;height:60px;border-radius:50%;background:#e3f0ff;color:#003366;display:flex;align-items:center;justify-content:center;font-size:2.2rem;box-shadow:0 2px 8px #00336622;">
                    <i class="fas fa-user"></i>
                </div>
                <div class="name mb-1" style="font-size:1.08rem;font-weight:700;color:#003366;"> <?php echo htmlspecialchars($result['student_name']); ?> </div>
                <div class="id mb-1" style="font-size:0.97rem;color:#888;">رقم الطالب: <?php echo htmlspecialchars($result['student_id']); ?></div>
                <?php if (!empty($result['section_number'])): ?>
                <div class="section mb-1" style="font-size:0.97rem;color:#1565c0;font-weight:600;">السكشن: <?php echo htmlspecialchars($result['section_number']); ?></div>
                <?php endif; ?>
            </div>
        </div>
        <?php

        $start_time = isset($result['start_time']) ? strtotime($result['start_time']) : null;
        $end_time = isset($result['end_time']) ? strtotime($result['end_time']) : null;
        $duration_minutes = isset($exam['duration_minutes']) ? (int)$exam['duration_minutes'] : null;
        $actual_minutes = ($start_time && $end_time) ? round(($end_time - $start_time) / 60, 2) : null;
        $actual_seconds = ($start_time && $end_time) ? ($end_time - $start_time) : null;
        $actual_h = $actual_m = $actual_s = null;
        if ($actual_seconds !== null) {
            $actual_h = floor($actual_seconds / 3600);
            $actual_m = floor(($actual_seconds % 3600) / 60);
            $actual_s = $actual_seconds % 60;
        }

        $total_points = 0;
        $student_points = 0;
        $questions = [];
        if (!empty($exam['id']) && !empty($result['id'])) {

            $stmt = $pdo->prepare("SELECT q.*, sa.selected_answer FROM questions q JOIN student_answers sa ON sa.question_id = q.id WHERE sa.exam_id = ? AND sa.student_id = ? AND sa.attempt_id = ?");
            $stmt->execute([$exam['id'], $result['student_id'], $result['id']]);
            $questions = $stmt->fetchAll();
            foreach ($questions as $q) {
                $total_points += (int)$q['points'];
                if ($q['selected_answer'] !== '' && $q['selected_answer'] !== null && $q['selected_answer'] === $q['correct_answer']) {
                    $student_points += (int)$q['points'];
                }
            }
        }
        ?>
        <div class="row g-3 mb-4 justify-content-center">
            <div class="col-md-6 col-12">
                <div class="stat-box mb-2">
                    <div class="stat-label">ميعاد الدخول</div>
                    <div class="stat-value" style="font-size:1.1rem;"><i class="fas fa-sign-in-alt me-1"></i> <?php echo $start_time ? date('Y-m-d H:i:s', $start_time) : '-'; ?></div>
                </div>
            </div>
            <div class="col-md-6 col-12">
                <div class="stat-box mb-2">
                    <div class="stat-label">ميعاد الإنهاء</div>
                    <div class="stat-value" style="font-size:1.1rem;"><i class="fas fa-sign-out-alt me-1"></i> <?php echo $end_time ? date('Y-m-d H:i:s', $end_time) : '-'; ?></div>
                </div>
            </div>
            <div class="col-md-4 col-12">
                <div class="stat-box mb-2">
                    <div class="stat-label">الوقت الكلي للامتحان</div>
                    <div class="stat-value"><?php echo $duration_minutes ? $duration_minutes . ' دقيقة' : '-'; ?></div>
                </div>
            </div>
            <div class="col-md-4 col-12">
                <div class="stat-box mb-2">
                    <div class="stat-label">الوقت الذي استغرقته</div>
                    <div class="stat-value">
                        <?php if ($actual_seconds !== null): ?>
                            <span style="font-weight:700;">
                                <i class="fas fa-clock me-1"></i>
                                <?php echo $actual_seconds . ' ثانية'; ?>
                            </span>
                        <?php else: ?>
                            -
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <div class="col-md-4 col-12">
                <div class="stat-box mb-2">
                    <div class="stat-label">الدرجة الكلية للامتحان</div>
                    <div class="stat-value"><?php echo $total_points; ?></div>
                </div>
            </div>
            <div class="col-md-6 col-12">
                <div class="stat-box mb-2">
                    <div class="stat-label">درجتك</div>
                    <div class="stat-value" style="color:#43a047;font-size:1.5rem;font-weight:900;"><i class="fas fa-star"></i> <?php echo $student_points; ?> / <?php echo $total_points; ?></div>
                </div>
            </div>
        </div>
        <?php

        if (!empty($questions)):
        ?>
        <div class="mb-5">
            <h4 class="mb-3" style="color:#1565c0;font-weight:800;">تفاصيل الأسئلة وإجاباتك</h4>
            <div class="row g-4" style="padding-right:0;padding-left:0;">
                <?php foreach ($questions as $idx => $q):
                    $is_correct = ($q['selected_answer'] !== '' && $q['selected_answer'] !== null && $q['selected_answer'] === $q['correct_answer']);
                    $answer_text = '';
                    switch ($q['selected_answer']) {
                        case 'a': $answer_text = $q['option_a']; break;
                        case 'b': $answer_text = $q['option_b']; break;
                        case 'c': $answer_text = $q['option_c']; break;
                        case 'd': $answer_text = $q['option_d']; break;
                        case '':
                        case null:
                        default: $answer_text = '<span style="color:#888;">لم يجب</span>';
                    }
                    $correct_text = '';
                    switch ($q['correct_answer']) {
                        case 'a': $correct_text = $q['option_a']; break;
                        case 'b': $correct_text = $q['option_b']; break;
                        case 'c': $correct_text = $q['option_c']; break;
                        case 'd': $correct_text = $q['option_d']; break;
                        default: $correct_text = '-';
                    }
                ?>
                <div class="col-12">
                    <div class="question-card" style="max-width:700px;margin-right:auto;margin-left:auto;border-radius:14px;border:2px solid <?php echo $is_correct ? '#43a047' : '#e74c3c'; ?>;background:#f8fbff;box-shadow:0 2px 10px #00336611;padding:1.2rem 1rem;height:100%;display:flex;flex-direction:column;justify-content:space-between;margin-bottom:1.2rem;">
                        <div>
                            <div style="font-size:1.08rem;font-weight:700;color:#1565c0;margin-bottom:0.5em;">
                                <i class="fas fa-question-circle me-1"></i>
                                سؤال <?php echo $idx+1; ?>
                            </div>
                            <div style="font-size:1.07rem;font-weight:600;margin-bottom:0.7em;color:#222; direction:ltr; text-align:left;">
                                <?php echo htmlspecialchars($q['question_text']); ?>
                            </div>
                            <ul style="list-style:none;padding:0;margin-bottom:0.7em;">
                                <?php foreach(['a','b','c','d'] as $opt): ?>
                                <li style="margin-bottom:0.3em;direction:ltr;text-align:left;<?php if($q['selected_answer'] !== '' && $q['selected_answer'] !== null && $q['selected_answer']===$opt)echo'font-weight:bold;'; ?><?php if($q['correct_answer']===$opt)echo'color:#43a047;'; ?>display:flex;align-items:center;gap:6px;">
                                    <span style="display:inline-block;width:22px;"><?php echo strtoupper($opt); ?>.</span>
                                    <span><?php echo htmlspecialchars($q['option_'.$opt]); ?></span>
                                    <?php if($q['selected_answer'] !== '' && $q['selected_answer'] !== null && $q['selected_answer']===$opt)echo'<span style="color:#1565c0;font-size:1.1em;"> <i class=\'fas fa-user-check\'></i> اختيارك</span>'; ?>
                                    <?php if($q['correct_answer']===$opt)echo'<span style="color:#43a047;font-size:1.1em;"> <i class=\'fas fa-check\'></i> صحيحة</span>'; ?>
                                </li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                        <div style="margin-top:0.5em;">
                            <span style="font-weight:600;color:#888;">إجابتك:</span>
                            <span style="font-weight:700;color:<?php echo $is_correct ? '#43a047' : '#e74c3c'; ?>;">
                                <?php echo (is_null($q['selected_answer']) || $q['selected_answer'] === '') ? '<span style=\'color:#888;\'>لم يجب</span>' : htmlspecialchars($answer_text); ?>
                            </span>
                            <?php if (!$is_correct): ?>
                            <span style="color:#888;font-size:0.97em;"> (الإجابة الصحيحة: <span style="color:#43a047;font-weight:700;"> <?php echo htmlspecialchars($correct_text); ?> </span>)</span>
                            <?php endif; ?>
                            <span class="badge bg-info ms-2" style="font-size:0.98em;">درجة السؤال: <?php echo $q['points']; ?></span>
                            <?php if ($is_correct): ?>
                                <span class="badge bg-success ms-2"><i class="fas fa-check"></i> إجابة صحيحة</span>
                            <?php else: ?>
                                <span class="badge bg-danger ms-2"><i class="fas fa-times"></i> إجابة خاطئة</span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>
    </div>
    <?php else: ?>
    <div class="alert alert-warning mt-4" style="max-width:700px;margin:40px auto 0 auto;text-align:center;font-size:1.2em;">
        النتيجة غير متاحة حالياً.
    </div>
    <?php endif; ?>
    <?php endif; ?>
    <!-- تم حذف باقي محتوى الصفحة بناءً على طلب المستخدم -->

    <!-- Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>