<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

if (!isLoggedIn()) {
    header('Location: ../index.php');
    exit();
}

if (!isAdmin() && !isAssistant()) {
    header('Location: ../index.php');
    exit();
}

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['delete_result'])) {
    if (!verifyCSRFToken($_POST['csrf_token'])) {
        $error = 'خطأ في التحقق من الأمان';
    } else {
        $result_id = (int)$_POST['result_id'];
        try {
            $stmt = $pdo->prepare("DELETE FROM exam_results WHERE id = ?");
            if ($stmt->execute([$result_id])) {
                $success = 'تم حذف النتيجة بنجاح';
                logActivity($_SESSION['user_id'], 'delete_result', "حذف نتيجة الامتحان ID: $result_id");
            } else {
                $error = 'فشل في حذف النتيجة';
            }
        } catch (PDOException $e) {
            $error = 'خطأ في النظام';
        }
    }
}

$exam_type = isset($_GET['exam_type']) ? $_GET['exam_type'] : '';
$search = isset($_GET['search']) ? sanitize($_GET['search']) : '';

$where_conditions = [];
$params = [];

if ($exam_type) {
    $where_conditions[] = "e.exam_type = ?";
    $params[] = $exam_type;
}

try {

    $exam_where = "status != 'deleted'";
    $exam_params = [];

    if ($exam_type) {
        $exam_where .= " AND exam_type = ?";
        $exam_params[] = $exam_type;
    }

    $stmt = $pdo->prepare("SELECT id, title, exam_type FROM exams WHERE $exam_where ORDER BY title");
    $stmt->execute($exam_params);
    $all_exams = $stmt->fetchAll();

    $student_where = "er.end_time IS NOT NULL";
    $student_params = [];

    if ($exam_type) {
        $student_where .= " AND e.exam_type = ?";
        $student_params[] = $exam_type;
    }

    if ($search) {
        $student_where .= " AND (er.student_name LIKE ? OR er.student_id LIKE ?)";
        $student_params[] = "%$search%";
        $student_params[] = "%$search%";
    }

    $stmt = $pdo->prepare("
        SELECT DISTINCT er.student_id, er.student_name, er.section_number
        FROM exam_results er
        LEFT JOIN exams e ON er.exam_id = e.id
        WHERE $student_where
        ORDER BY er.student_id ASC
    ");
    $stmt->execute($student_params);
    $all_students = $stmt->fetchAll();

$student_exam_results = [];
foreach ($all_students as $student) {
    $student_exam_results[$student['student_id']] = [
        'student_name' => $student['student_name'],
        'section_number' => $student['section_number'],
        'exams' => []
    ];

    foreach ($all_exams as $exam) {

        $result_where = "er.student_id = ? AND er.exam_id = ? AND er.end_time IS NOT NULL";
        $result_params = [$student['student_id'], $exam['id']];

        $stmt = $pdo->prepare("
            SELECT
                er.*,
                e.title as exam_title,
                e.passing_percentage,
                COALESCE(
                    (SELECT SUM(CASE WHEN sa.selected_answer COLLATE utf8mb4_unicode_ci = q.correct_answer THEN q.points ELSE 0 END)
                     FROM student_answers sa
                     JOIN questions q ON sa.question_id = q.id
                     WHERE sa.exam_id = er.exam_id AND sa.student_id COLLATE utf8mb4_unicode_ci = er.student_id AND sa.attempt_id = er.id), 0
                ) as earned_points,
                COALESCE(
                    (SELECT SUM(q.points)
                     FROM student_answers sa
                     JOIN questions q ON sa.question_id = q.id
                     WHERE sa.exam_id = er.exam_id AND sa.student_id COLLATE utf8mb4_unicode_ci = er.student_id AND sa.attempt_id = er.id), 0
                ) as total_points
            FROM exam_results er
            LEFT JOIN exams e ON er.exam_id = e.id
            WHERE $result_where
            ORDER BY er.end_time DESC
            LIMIT 1
        ");
        $stmt->execute($result_params);
        $result = $stmt->fetch();

        $student_exam_results[$student['student_id']]['exams'][$exam['id']] = $result;
    }
}

    $stmt = $pdo->query("SELECT id, name FROM subjects WHERE status = 'active' ORDER BY name");
    $subjects = $stmt->fetchAll();

    $total_students = count($all_students);
    $total_results = 0;

    foreach ($student_exam_results as $student_id => $student_data) {
        foreach ($student_data['exams'] as $exam_id => $result) {
            if ($result) {
                $total_results++;
            }
        }
    }

} catch (PDOException $e) {
    $error = 'خطأ في تحميل البيانات: ' . $e->getMessage();
    $student_exam_results = [];
    $all_exams = [];
    $subjects = [];
    $total_results = 0;
    $total_students = 0;
}

$csrf_token = generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>نتائج الامتحانات - نظام الامتحانات الإلكترونية</title>
    <link <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;500;600&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="../assets/css/mobile.css" rel="stylesheet">
    <style>
        body {
            background: #183046;
            min-height: 100vh;
            font-family: 'Cairo', sans-serif;
            overflow-x: hidden;
            overflow-y: auto;
        }
        .navbar {
            background: #fff !important;
            box-shadow: 0 2px 10px rgba(0,0,0,0.07);
            border-bottom: 1px solid #e3e6f0;
        }
        .navbar-brand {
            font-weight: 700;
            font-size: 1.2rem;
            color: #2c3e50 !important;
            line-height: 1.2;
            display: flex;
            align-items: center;
        }

        .brand-text {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .nav-link, .navbar-nav .nav-link {
            color: #2c3e50 !important;
            font-weight: 600;
            border-radius: 25px;
            margin: 0 0.3rem;
        }
        .nav-link.active, .nav-link:hover {
            background: #2196f3;
            color: #fff !important;
        }
        .results-header {
            margin-top: 2.5rem;
            margin-bottom: 2.5rem;
            text-align: right;
        }
        .results-title {
            font-size: 2.3rem;
            font-weight: 900;
            color: #fff;
            margin-bottom: 0.5rem;
        }
        .results-subtitle {
            color: #e0e0e0;
            font-size: 1.1rem;
            font-weight: 400;
        }
        .modern-card {
            background: #fff;
            border-radius: 16px;
            box-shadow: 0 4px 24px rgba(102,126,234,0.08);
            padding: 1.5rem 1.2rem;
            margin-bottom: 2rem;
        }
        .modern-card .card-header {
            background: none;
            border: none;
            padding: 0 0 1rem 0;
            color: #2196f3;
            font-weight: 700;
            font-size: 1.2rem;
        }
        .modern-card .list-group-item {
            border: none;
            border-bottom: 1px solid #f0f0f0;
            padding: 0.7rem 0.2rem;
        }
        .modern-card .list-group-item:last-child {
            border-bottom: none;
        }
        .card.border-0.shadow-sm {
            border-radius: 18px;
            background: #fff;
            box-shadow: 0 8px 32px rgba(102,126,234,0.08);
            padding: 1.5rem 1.2rem;
        }
        .card-header.bg-primary {
            background: #2196f3 !important;
            color: #fff !important;
            border-radius: 16px 16px 0 0 !important;
            font-weight: 700;
            font-size: 1.2rem;
        }
        .btn-primary, .btn-outline-primary:hover {
            background: #2196f3 !important;
            border-color: #2196f3 !important;
            color: #fff !important;
        }
        .btn-outline-primary {
            color: #2196f3 !important;
            border-color: #2196f3 !important;
            background: #fff !important;
        }
        .btn-outline-primary:focus, .btn-outline-primary:active {
            background: #2196f3 !important;
            color: #fff !important;
        }
        .table {
            background: #fff;
            border-radius: 12px;
            overflow: hidden;
        }
        .table th, .table td {
            vertical-align: middle;
        }
        .modal-content {
            border-radius: 16px;
        }
        .modal-header {
            border-bottom: 1px solid #f0f0f0;
        }
        .modal-title {
            color: #2196f3;
            font-weight: 700;
        }
        .form-label {
            font-weight: 600;
            color: #2c3e50;
        }
        .form-control, .form-select {
            border-radius: 10px;
        }
        .btn-close {
            margin: 0;
        }
        .footer {
            background: #2c3e50;
            color: #fff;
            padding: 2.5rem 0 1rem;
            margin-top: 3rem;
        }
        .footer-title {
            font-size: 1.2rem;
            font-weight: 700;
        }
        .footer-bottom {
            border-top: 1px solid rgba(255,255,255,0.1);
            padding-top: 1rem;
            text-align: center;
            opacity: 0.7;
        }
        @media (max-width: 991px) {
            .results-header {margin-top: 1.5rem;}
        }

        .table-bordered th, .table-bordered td {
            border: 1px solid #dee2e6;
            vertical-align: middle;
        }

        .table thead th {
            background-color: #f8f9fa;
            font-weight: 600;
            border-bottom: 2px solid #dee2e6;
        }

        .exam-cell {
            min-width: 120px;
            padding: 8px;
        }

        .exam-result {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
        }

        .grade-badge {
            font-size: 0.85em;
            padding: 4px 8px;
        }

        .grade-badge strong {
            font-size: 1.1em;
        }

        .grade-badge small {
            opacity: 0.8;
        }

        .attendance-badge {
            font-size: 0.7em;
            padding: 2px 6px;
        }

        .student-row-excellent {
            background-color: rgba(40, 167, 69, 0.1);
        }

        .student-row-good {
            background-color: rgba(255, 193, 7, 0.1);
        }

        .student-row-poor {
            background-color: rgba(220, 53, 69, 0.1);
        }

        .btn-outline-danger:hover {
            background-color: #dc3545;
            border-color: #dc3545;
            color: white;
        }

        .btn-outline-success:hover {
            background-color: #198754;
            border-color: #198754;
            color: white;
        }

        .btn-outline-danger:disabled,
        .btn-outline-success:disabled {
            opacity: 0.7;
            cursor: not-allowed;
        }

        .exam-type-badge {
            font-size: 0.65em !important;
            padding: 2px 6px !important;
            margin-top: 2px;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <?php include '../includes/mobile_navbar.php'; ?>
    <div class="container" style="margin-top: 80px;">
        <div class="results-header">
            <div class="results-title">
                <i class="fas fa-chart-bar text-primary me-2"></i>
                نتائج الامتحانات
            </div>
            <div class="results-subtitle">عرض وإدارة نتائج امتحانات قسم الـ Forensic - جامعة المنيا</div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-6 mb-3">
                <div class="card stat-card border-0 shadow-sm">
                    <div class="card-body text-center">
                        <div class="stat-number text-primary"><?php echo $total_results; ?></div>
                        <div class="stat-label">إجمالي النتائج</div>
                        <i class="fas fa-file-alt text-muted mt-2" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
            <div class="col-md-6 mb-3">
                <div class="card stat-card border-0 shadow-sm">
                    <div class="card-body text-center">
                        <div class="stat-number text-success"><?php echo $total_students; ?></div>
                        <div class="stat-label">إجمالي الطلاب</div>
                        <i class="fas fa-users text-muted mt-2" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>

        </div>

        <!-- Filter Form -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">
                            <i class="fas fa-filter me-2"></i>
                            فلترة النتائج
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-2">
                                <label class="form-label">نوع الامتحان</label>
                                <select name="exam_type" class="form-select">
                                    <option value="">الكل</option>
                                    <option value="exam" <?php echo $exam_type === 'exam' ? 'selected' : ''; ?>>امتحان</option>
                                    <option value="quiz" <?php echo $exam_type === 'quiz' ? 'selected' : ''; ?>>كويز</option>
                                </select>
                            </div>

                            <div class="col-md-6">
                                <label class="form-label">بحث</label>
                                <input type="text" name="search" class="form-control" placeholder="اسم الطالب أو رقم الطالب" value="<?php echo htmlspecialchars($search); ?>">
                            </div>
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-2"></i>
                                    بحث
                                </button>
                                <a href="results.php" class="btn btn-secondary">
                                    <i class="fas fa-times me-2"></i>
                                    مسح الفلاتر
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Results Table -->
        <div class="row">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-light">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-table me-2"></i>
                                قائمة النتائج
                                <?php if (!empty($student_exam_results)): ?>
                                    <span class="badge bg-primary ms-2"><?php echo count($student_exam_results); ?> طالب</span>
                                <?php endif; ?>
                            </h5>
                            <?php if (!empty($student_exam_results)): ?>
                                <div class="d-flex gap-2">
                                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="exportToPDF()" id="pdfButton">
                                        <i class="fas fa-file-pdf me-2"></i>
                                        تحميل PDF
                                    </button>
                                    <button type="button" class="btn btn-outline-success btn-sm" onclick="exportToExcel()" id="excelButton">
                                        <i class="fas fa-file-excel me-2"></i>
                                        تحميل Excel
                                    </button>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if ($error): ?>
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <?php echo $error; ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>

                        <?php if ($success): ?>
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                <i class="fas fa-check-circle me-2"></i>
                                <?php echo $success; ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>

                        <?php if (empty($student_exam_results)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-inbox text-muted" style="font-size: 4rem;"></i>
                                <h4 class="text-muted mt-3">لا توجد نتائج</h4>
                                <p class="text-muted">لم يتم العثور على نتائج تطابق معايير البحث المحددة</p>
                                <?php if ($exam_type || $search): ?>
                                    <div class="mt-3">
                                        <small class="text-muted">الفلاتر المطبقة:</small>
                                        <div class="mt-2">
                                            <?php if ($exam_type): ?>
                                                <span class="badge bg-primary me-2">نوع الامتحان: <?php echo $exam_type === 'exam' ? 'امتحان' : 'كويز'; ?></span>
                                            <?php endif; ?>
                                            <?php if ($search): ?>
                                                <span class="badge bg-info me-2">البحث: <?php echo htmlspecialchars($search); ?></span>
                                            <?php endif; ?>
                                        </div>
                                        <a href="results.php" class="btn btn-outline-primary btn-sm mt-3">
                                            <i class="fas fa-times me-2"></i>إزالة جميع الفلاتر
                                        </a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover table-bordered">
                                    <thead class="table-light">
                                        <tr>
                                            <th rowspan="2" class="align-middle text-center">
                                                رقم الطالب
                                            </th>
                                            <th rowspan="2" class="align-middle text-center">
                                                اسم الطالب
                                            </th>
                                            <th rowspan="2" class="align-middle text-center">
                                                السكشن
                                            </th>
                                            <?php foreach ($all_exams as $exam): ?>
                                                <th class="text-center" style="min-width: 120px;">
                                                    <div><?php echo htmlspecialchars($exam['title']); ?></div>
                                                    <small class="text-muted">
                                                        <span class="badge bg-<?php echo $exam['exam_type'] === 'exam' ? 'primary' : 'info'; ?> exam-type-badge">
                                                            <?php echo $exam['exam_type'] === 'exam' ? 'امتحان' : 'كويز'; ?>
                                                        </span>
                                                    </small>
                                                </th>
                                            <?php endforeach; ?>
                                            <th class="text-center" style="min-width: 120px;">
                                                <i class="fas fa-calendar-check"></i> عدد مرات الحضور
                                            </th>
                                        </tr>
                                        <tr>
                                            <?php foreach ($all_exams as $exam): ?>
                                                <th class="text-center text-muted" style="font-size: 0.85em;">
                                                    النقاط / الحضور
                                                </th>
                                            <?php endforeach; ?>
                                            <th class="text-center text-muted" style="font-size: 0.85em;">
                                                إجمالي الحضور
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($student_exam_results as $student_id => $student_data): ?>
                                            <?php

                                                $temp_attendance = 0;
                                                $temp_total_exams = count($all_exams);
                                                foreach ($student_data['exams'] as $exam_result) {
                                                    if ($exam_result) {
                                                        $temp_attendance++;
                                                    }
                                                }
                                                $attendance_rate = $temp_total_exams > 0 ? ($temp_attendance / $temp_total_exams) * 100 : 0;
                                                $row_class = '';
                                                if ($attendance_rate == 100) {
                                                    $row_class = 'student-row-excellent';
                                                } elseif ($attendance_rate >= 50) {
                                                    $row_class = 'student-row-good';
                                                } elseif ($attendance_rate > 0) {
                                                    $row_class = 'student-row-poor';
                                                }
                                            ?>
                                            <tr class="<?php echo $row_class; ?>">
                                                <td class="text-center">
                                                    <span class="badge bg-primary fs-6 px-3 py-2">
                                                        <?php echo htmlspecialchars($student_id); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($student_data['student_name']); ?></strong>
                                                </td>
                                                <td class="text-center">
                                                    <?php echo htmlspecialchars($student_data['section_number'] ?: '-'); ?>
                                                </td>
                                                <?php
                                                    $attendance_count = 0;
                                                ?>
                                                <?php foreach ($all_exams as $exam): ?>
                                                    <td class="text-center exam-cell">
                                                        <?php
                                                            $exam_result = $student_data['exams'][$exam['id']];
                                                            if ($exam_result):
                                                                $earned_points = isset($exam_result['earned_points']) ? (int)$exam_result['earned_points'] : 0;
                                                                $total_points = isset($exam_result['total_points']) ? (int)$exam_result['total_points'] : 0;
                                                                $percentage = $total_points > 0 ? ($earned_points / $total_points) * 100 : 0;
                                                                $passing_percentage = isset($exam_result['passing_percentage']) ? (float)$exam_result['passing_percentage'] : 60;
                                                                $is_passed = $percentage >= $passing_percentage;

                                                                $attendance_count++;
                                                        ?>
                                                            <div class="exam-result">
                                                                <span class="badge bg-<?php echo $is_passed ? 'success' : 'danger'; ?> grade-badge">
                                                                    <strong><?php echo $earned_points; ?></strong><small>/<?php echo $total_points; ?></small>
                                                                </span>
                                                                <span class="badge bg-<?php echo $is_passed ? 'success' : 'secondary'; ?> attendance-badge">
                                                                    <i class="fas fa-user-check"></i> حاضر
                                                                </span>
                                                            </div>
                                                        <?php else: ?>
                                                            <div class="exam-result">
                                                                <span class="badge bg-light text-dark grade-badge">-</span>
                                                                <span class="badge bg-warning text-dark attendance-badge">
                                                                    <i class="fas fa-user-times"></i> غايب
                                                                </span>
                                                            </div>
                                                        <?php endif; ?>
                                                    </td>
                                                <?php endforeach; ?>

                                                <!-- عمود عدد مرات الحضور -->
                                                <td class="text-center exam-cell">
                                                    <?php
                                                        $total_exams = count($all_exams);
                                                        $attendance_color = $attendance_count == $total_exams ? 'success' : ($attendance_count > 0 ? 'warning' : 'secondary');
                                                    ?>
                                                    <div class="exam-result">
                                                        <span class="badge bg-<?php echo $attendance_color; ?> grade-badge fw-bold">
                                                            <i class="fas fa-calendar-check"></i> <?php echo $attendance_count; ?>/<?php echo $total_exams; ?>
                                                        </span>
                                                        <small class="text-muted">
                                                            <?php
                                                                if ($attendance_count == $total_exams) {
                                                                    echo '<i class="fas fa-check-circle text-success"></i> حضور كامل';
                                                                } elseif ($attendance_count > 0) {
                                                                    echo '<i class="fas fa-exclamation-triangle text-warning"></i> حضور جزئي';
                                                                } else {
                                                                    echo '<i class="fas fa-times-circle text-danger"></i> لم يحضر';
                                                                }
                                                            ?>
                                                        </small>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تأكيد الحذف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>هل أنت متأكد من حذف نتيجة الطالب: <strong id="studentName"></strong>؟</p>
                    <p class="text-danger">هذا الإجراء لا يمكن التراجع عنه.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                        <input type="hidden" name="result_id" id="resultId">
                        <button type="submit" name="delete_result" class="btn btn-danger">حذف</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    </div>
    <footer class="footer">
        <div class="container">
            <div class="footer-title">جامعة المنيا - قسم الـ Forensic</div>
            <div class="footer-bottom">
                &copy; 2024 جميع الحقوق محفوظة - جامعة المنيا - قسم الـ Forensic
            </div>
        </div>
    </footer>
    <script src="https:
    <script src="../assets/js/main.js"></script>
    <script src="https:
    <script src="https:
    <script src="https:
    <script src="https:

    <script>

        window.addEventListener('load', function() {
            console.log('Page loaded');
            console.log('jsPDF available:', typeof window.jspdf !== 'undefined');
            if (typeof window.jspdf !== 'undefined') {
                console.log('jsPDF version:', window.jspdf.jsPDF.version);
            }
        });
    </script>
    <script>
        function deleteResult(resultId, studentName) {
            document.getElementById('resultId').value = resultId;
            document.getElementById('studentName').textContent = studentName;
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }

        function sortStudents() {
            const tbody = document.querySelector('.table tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));

            rows.sort((a, b) => {
                const aStudentId = parseInt(a.cells[0].textContent.trim()) || 0;
                const bStudentId = parseInt(b.cells[0].textContent.trim()) || 0;
                return aStudentId - bStudentId;
            });

            rows.forEach(row => tbody.appendChild(row));
        }

        document.addEventListener('DOMContentLoaded', function() {
            sortStudents();
        });

        function exportResults() {
            const params = new URLSearchParams(window.location.search);
            window.open(`../ajax/export_results.php?${params.toString()}`, '_blank');
        }

        function printResults() {
            window.print();
        }

        function exportToExcel() {
            console.log('exportToExcel function called');

            const button = document.getElementById('excelButton');
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الإنشاء...';
            button.disabled = true;

            setTimeout(() => {
                try {

                    if (typeof XLSX === 'undefined') {
                        throw new Error('مكتبة Excel غير محملة');
                    }

                    console.log('XLSX library loaded successfully');

                    const table = document.querySelector('.table');
                    if (!table) {
                        throw new Error('لم يتم العثور على الجدول');
                    }

                    const wb = XLSX.utils.book_new();

                    const headers = [];
                    const headerCells = table.querySelectorAll('thead tr:first-child th');
                    headerCells.forEach(cell => {
                        headers.push(cell.textContent.trim());
                    });

                    const data = [headers];
                    const rows = table.querySelectorAll('tbody tr');

                    rows.forEach(row => {
                        const rowData = [];
                        const cells = row.querySelectorAll('td');

                        cells.forEach((cell, index) => {
                            if (index === 0) {

                                rowData.push(cell.textContent.trim());
                            } else if (index === 1) {

                                rowData.push(cell.textContent.trim());
                            } else if (index === 2) {

                                rowData.push(cell.textContent.trim());
                            } else if (index === cells.length - 1) {

                                const attendanceText = cell.textContent.trim();
                                const attendanceMatch = attendanceText.match(/(\d+\/\d+)/);
                                if (attendanceMatch) {
                                    rowData.push(attendanceMatch[1]);
                                } else if (attendanceText.includes('حضور كامل')) {
                                    rowData.push('حضور كامل');
                                } else if (attendanceText.includes('حضور جزئي')) {
                                    rowData.push('حضور جزئي');
                                } else if (attendanceText.includes('لم يحضر')) {
                                    rowData.push('لم يحضر');
                                } else {
                                    rowData.push('-');
                                }
                            } else {

                                const gradeElement = cell.querySelector('.grade-badge');
                                if (gradeElement) {
                                    const gradeText = gradeElement.textContent.trim();
                                    rowData.push(gradeText);
                                } else {
                                    rowData.push('غائب');
                                }
                            }
                        });

                        data.push(rowData);
                    });

                    console.log('Data collected for Excel:', data);

                    const ws = XLSX.utils.aoa_to_sheet(data);

                    const range = XLSX.utils.decode_range(ws['!ref']);

                    ws['!cols'] = [
                        { wch: 15 },
                        { wch: 25 },
                        { wch: 10 },
                    ];

                    for (let i = 3; i < headers.length; i++) {
                        if (!ws['!cols'][i]) ws['!cols'][i] = {};
                        ws['!cols'][i].wch = 15;
                    }

                    for (let col = 0; col < headers.length; col++) {
                        const cellAddress = XLSX.utils.encode_cell({ r: 0, c: col });
                        if (ws[cellAddress]) {
                            ws[cellAddress].s = {
                                font: { bold: true, color: { rgb: "FFFFFF" }, sz: 12 },
                                fill: { fgColor: { rgb: "3490DC" } },
                                alignment: { horizontal: "center", vertical: "center" },
                                border: {
                                    top: { style: "thin", color: { rgb: "000000" } },
                                    bottom: { style: "thin", color: { rgb: "000000" } },
                                    left: { style: "thin", color: { rgb: "000000" } },
                                    right: { style: "thin", color: { rgb: "000000" } }
                                }
                            };
                        }
                    }

                    for (let row = 1; row <= range.e.r; row++) {
                        for (let col = 0; col <= range.e.c; col++) {
                            const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
                            if (ws[cellAddress]) {
                                ws[cellAddress].s = {
                                    alignment: { horizontal: "center", vertical: "center" },
                                    border: {
                                        top: { style: "thin", color: { rgb: "CCCCCC" } },
                                        bottom: { style: "thin", color: { rgb: "CCCCCC" } },
                                        left: { style: "thin", color: { rgb: "CCCCCC" } },
                                        right: { style: "thin", color: { rgb: "CCCCCC" } }
                                    }
                                };

                                if (row % 2 === 0) {
                                    ws[cellAddress].s.fill = { fgColor: { rgb: "F8F9FA" } };
                                }

                                if (col >= 3 && col < headers.length - 1) {
                                    const cellValue = ws[cellAddress].v;
                                    if (typeof cellValue === 'string' && cellValue.includes('/')) {
                                        const [score, total] = cellValue.split('/').map(Number);
                                        const percentage = (score / total) * 100;

                                        if (percentage >= 85) {
                                            ws[cellAddress].s.fill = { fgColor: { rgb: "D4EDDA" } };
                                            ws[cellAddress].s.font = { color: { rgb: "155724" } };
                                        } else if (percentage >= 70) {
                                            ws[cellAddress].s.fill = { fgColor: { rgb: "FFF3CD" } };
                                            ws[cellAddress].s.font = { color: { rgb: "856404" } };
                                        } else if (percentage >= 50) {
                                            ws[cellAddress].s.fill = { fgColor: { rgb: "F8D7DA" } };
                                            ws[cellAddress].s.font = { color: { rgb: "721C24" } };
                                        }
                                    } else if (cellValue === 'غائب') {
                                        ws[cellAddress].s.fill = { fgColor: { rgb: "E2E3E5" } };
                                        ws[cellAddress].s.font = { color: { rgb: "6C757D" } };
                                    }
                                }
                            }
                        }
                    }

                    XLSX.utils.book_append_sheet(wb, ws, "نتائج الامتحانات");

                    const infoData = [
                        ['تقرير نتائج الامتحانات'],
                        ['قسم الطب الشرعي والسموم الإكلينيكية'],
                        ['جامعة المنيا - كلية الطب'],
                        [''],
                        ['تاريخ التقرير:', new Date().toLocaleDateString('ar-EG')],
                        ['وقت الإنشاء:', new Date().toLocaleTimeString('ar-EG')],
                        ['عدد الطلاب:', rows.length],
                        ['عدد الامتحانات:', headers.length - 3],
                        [''],
                        ['ملاحظات:'],
                        ['- الدرجات المعروضة تشمل النقاط المكتسبة من إجمالي النقاط'],
                        ['- الألوان تشير إلى مستوى الأداء (أخضر: ممتاز، أصفر: جيد، أحمر: ضعيف)'],
                        ['- "غائب" يعني عدم حضور الطالب للامتحان'],
                        ['- عدد مرات الحضور يشير إلى حضور المحاضرات']
                    ];

                    const infoWs = XLSX.utils.aoa_to_sheet(infoData);

                    infoWs['!cols'] = [{ wch: 30 }, { wch: 30 }];

                    if (infoWs['A1']) {
                        infoWs['A1'].s = {
                            font: { bold: true, sz: 16, color: { rgb: "3490DC" } },
                            alignment: { horizontal: "center" }
                        };
                    }

                    ['A2', 'A3'].forEach(cell => {
                        if (infoWs[cell]) {
                            infoWs[cell].s = {
                                font: { bold: true, sz: 12 },
                                alignment: { horizontal: "center" }
                            };
                        }
                    });

                    ['A5', 'A6', 'A7', 'A8', 'A10'].forEach(cell => {
                        if (infoWs[cell]) {
                            infoWs[cell].s = {
                                font: { bold: true },
                                fill: { fgColor: { rgb: "F8F9FA" } }
                            };
                        }
                    });

                    XLSX.utils.book_append_sheet(wb, infoWs, "معلومات التقرير");

                    const filename = 'نتائج_الامتحانات_' + new Date().toISOString().split('T')[0] + '.xlsx';
                    XLSX.writeFile(wb, filename);

                    console.log('Excel file saved as:', filename);

                    alert('تم تحميل ملف Excel بنجاح!');

                } catch (error) {
                    console.error('Error in exportToExcel:', error);
                    alert('حدث خطأ أثناء إنشاء ملف Excel: ' + error.message);
                } finally {

                    button.innerHTML = originalText;
                    button.disabled = false;
                }
            }, 500);
        }

        function exportToPDF() {
            console.log('exportToPDF function called');

            const button = document.getElementById('pdfButton');
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الإنشاء...';
            button.disabled = true;

            setTimeout(() => {
                try {

                    if (typeof window.jspdf === 'undefined') {
                        throw new Error('مكتبة PDF غير محملة');
                    }

                    if (typeof html2canvas === 'undefined') {
                        throw new Error('مكتبة html2canvas غير محملة');
                    }

                    const { jsPDF } = window.jspdf;
                    console.log('jsPDF loaded successfully');

                    const table = document.querySelector('.table');
                    if (!table) {
                        throw new Error('لم يتم العثور على الجدول');
                    }

                    const exportContainer = document.createElement('div');
                    exportContainer.style.position = 'absolute';
                    exportContainer.style.left = '-9999px';
                    exportContainer.style.top = '0';
                    exportContainer.style.backgroundColor = 'white';
                    exportContainer.style.padding = '15px';
                    exportContainer.style.fontFamily = 'Arial, sans-serif';
                    exportContainer.style.direction = 'rtl';
                    exportContainer.style.width = '1300px';
                    exportContainer.style.maxWidth = '1300px';

                    const title = document.createElement('h2');
                    title.textContent = 'تقرير نتائج الامتحانات - قسم الطب الشرعي';
                    title.style.textAlign = 'center';
                    title.style.marginBottom = '10px';
                    title.style.color = '#333';
                    title.style.fontSize = '22px';

                    const subtitle = document.createElement('p');
                    subtitle.textContent = 'جامعة المنيا - كلية الطب - قسم الطب الشرعي والسموم الإكلينيكية';
                    subtitle.style.textAlign = 'center';
                    subtitle.style.marginBottom = '6px';
                    subtitle.style.fontSize = '14px';
                    subtitle.style.color = '#666';

                    const date = document.createElement('p');
                    date.textContent = 'تاريخ التقرير: ' + new Date().toLocaleDateString('ar-EG');
                    date.style.textAlign = 'center';
                    date.style.marginBottom = '18px';
                    date.style.fontSize = '12px';
                    date.style.color = '#666';

                    const tableClone = table.cloneNode(true);
                    tableClone.style.width = '100%';
                    tableClone.style.borderCollapse = 'collapse';
                    tableClone.style.fontSize = '11px';
                    tableClone.style.tableLayout = 'fixed';

                    const cells = tableClone.querySelectorAll('th, td');
                    cells.forEach((cell, index) => {
                        cell.style.border = '1.5px solid #ddd';
                        cell.style.padding = '6px 4px';
                        cell.style.textAlign = 'center';
                        cell.style.verticalAlign = 'middle';
                        cell.style.wordWrap = 'break-word';
                        cell.style.overflow = 'hidden';
                    });

                    const headerCells = tableClone.querySelectorAll('thead th');
                    headerCells.forEach((header, index) => {
                        if (index === 0) {
                            header.style.width = '100px';
                        } else if (index === 1) {
                            header.style.width = '180px';
                        } else if (index === 2) {
                            header.style.width = '80px';
                        } else if (index === headerCells.length - 1) {
                            header.style.width = '100px';
                        } else {
                            header.style.width = '110px';
                        }

                        header.style.backgroundColor = '#3490dc';
                        header.style.color = 'white';
                        header.style.fontWeight = 'bold';
                        header.style.fontSize = '10px';
                    });

                    const dataCells = tableClone.querySelectorAll('tbody td');
                    dataCells.forEach((cell, index) => {
                        const colIndex = index % headerCells.length;
                        if (colIndex === 0) {
                            cell.style.width = '100px';
                        } else if (colIndex === 1) {
                            cell.style.width = '180px';
                        } else if (colIndex === 2) {
                            cell.style.width = '80px';
                        } else if (colIndex === headerCells.length - 1) {
                            cell.style.width = '100px';
                        } else {
                            cell.style.width = '110px';
                        }
                    });

                    exportContainer.appendChild(title);
                    exportContainer.appendChild(subtitle);
                    exportContainer.appendChild(date);
                    exportContainer.appendChild(tableClone);
                    document.body.appendChild(exportContainer);

                    html2canvas(exportContainer, {
                        scale: 1.8,
                        useCORS: true,
                        allowTaint: true,
                        backgroundColor: '#ffffff',
                        width: 1400,
                        height: 900
                    }).then(canvas => {

                        const doc = new jsPDF('l', 'mm', 'a4');

                        const imgData = canvas.toDataURL('image/png');
                        const pageWidth = doc.internal.pageSize.getWidth();
                        const pageHeight = doc.internal.pageSize.getHeight();

                        const maxWidth = pageWidth - 20;
                        const maxHeight = pageHeight - 30;

                        let imgWidth = maxWidth;
                        let imgHeight = (canvas.height * imgWidth) / canvas.width;

                        if (imgHeight > maxHeight) {

                            const numPages = Math.ceil(imgHeight / maxHeight);
                            const pageImgHeight = maxHeight;
                            const pageCanvasHeight = (canvas.height * pageImgHeight) / imgHeight;

                            for (let page = 0; page < numPages; page++) {
                                if (page > 0) {
                                    doc.addPage();
                                }

                                const tempCanvas = document.createElement('canvas');
                                const tempCtx = tempCanvas.getContext('2d');
                                tempCanvas.width = canvas.width;
                                tempCanvas.height = pageCanvasHeight;

                                tempCtx.drawImage(
                                    canvas,
                                    0, page * pageCanvasHeight,
                                    canvas.width, pageCanvasHeight,
                                    0, 0,
                                    canvas.width, pageCanvasHeight
                                );

                                const pageImgData = tempCanvas.toDataURL('image/png');
                                const x = (pageWidth - imgWidth) / 2;
                                const y = 15;

                                doc.addImage(pageImgData, 'PNG', x, y, imgWidth, pageImgHeight);

                                doc.setFontSize(8);
                                doc.text(`صفحة ${page + 1} من ${numPages}`, pageWidth - 20, pageHeight - 10, { align: 'right' });
                            }
                        } else {

                            const x = (pageWidth - imgWidth) / 2;
                            const y = (pageHeight - imgHeight) / 2;

                            doc.addImage(imgData, 'PNG', x, y, imgWidth, imgHeight);

                            doc.setFontSize(8);
                            doc.text('صفحة 1', pageWidth - 20, pageHeight - 10, { align: 'right' });
                        }

                        const filename = 'نتائج_الامتحانات_' + new Date().toISOString().split('T')[0] + '.pdf';
                        doc.save(filename);

                        document.body.removeChild(exportContainer);

                        alert('تم تحميل ملف PDF بنجاح!');

                    }).catch(error => {
                        console.error('Error with html2canvas:', error);
                        document.body.removeChild(exportContainer);
                        throw error;
                    });

                } catch (error) {
                    console.error('Error in exportToPDF:', error);
                    alert('حدث خطأ أثناء إنشاء ملف PDF: ' + error.message);
                } finally {

                    button.innerHTML = originalText;
                    button.disabled = false;
                }
            }, 500);
        }
    </script>
    <script src="../assets/js/mobile.js"></script>
    <!-- Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>