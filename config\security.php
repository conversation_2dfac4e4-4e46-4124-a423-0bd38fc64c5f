<?php
/**
 * إعدادات الأمان الأساسية
 * نظام الامتحانات الإلكترونية
 */

// منع الوصول المباشر
if (!defined('SECURITY_CONFIG_LOADED')) {
    die('Access Denied');
}

// إعدادات Rate Limiting
define('RATE_LIMIT_LOGIN', 5);           // محاولات تسجيل الدخول
define('RATE_LIMIT_LOGIN_WINDOW', 900);  // 15 دقيقة
define('RATE_LIMIT_API', 100);           // طلبات API
define('RATE_LIMIT_API_WINDOW', 60);     // دقيقة واحدة

// إعدادات الجلسة
define('SESSION_MAX_LIFETIME', 10800);   // 3 ساعات

// قائمة بـ User Agents المحظورة
define('BLOCKED_USER_AGENTS', [
    'sqlmap', 'nikto', 'nmap', 'masscan', 'nessus', 'openvas', 'w3af', 'skipfish', 'burp', 'owasp'
]);

// قائمة بـ IP addresses محظورة
define('BLOCKED_IPS', []);

// إعدادات Content Security Policy
define('CSP_POLICY', [
    'default-src' => "'self'",
    'script-src' => "'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com",
    'style-src' => "'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://fonts.googleapis.com",
    'font-src' => "'self' https://cdnjs.cloudflare.com https://fonts.gstatic.com",
    'img-src' => "'self' data:",
    'frame-ancestors' => "'none'",
    'form-action' => "'self'"
]);

/**
 * دالة للتحقق من User Agent المشبوه
 */
function isBlockedUserAgent($user_agent) {
    $user_agent = strtolower($user_agent);
    foreach (BLOCKED_USER_AGENTS as $blocked) {
        if (strpos($user_agent, strtolower($blocked)) !== false) {
            return true;
        }
    }
    return false;
}

/**
 * دالة للتحقق من IP محظور
 */
function isBlockedIP($ip) {
    return in_array($ip, BLOCKED_IPS);
}

/**
 * دالة لتوليد CSP header
 */
function generateCSPHeader() {
    $csp_parts = [];
    foreach (CSP_POLICY as $directive => $value) {
        $csp_parts[] = "$directive $value";
    }
    return implode('; ', $csp_parts);
}
?>
