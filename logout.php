<?php
// إعدادات الجلسة الآمنة
ini_set('session.cookie_httponly', 1);
ini_set('session.cookie_secure', isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 1 : 0);
ini_set('session.use_strict_mode', 1);

session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// تطبيق headers الأمنية
setSecurityHeaders();

if (isLoggedIn()) {
    // تسجيل عملية تسجيل الخروج
    logActivity($_SESSION['user_id'], 'logout', 'تسجيل خروج');
    logSecurityEvent('user_logout', "User {$_SESSION['username']} logged out", 'low');

    // حفظ معرف المستخدم قبل تدمير الجلسة
    $user_id = $_SESSION['user_id'];

    // تدمير الجلسة بشكل آمن
    $_SESSION = array();

    // حذف cookie الجلسة
    if (ini_get("session.use_cookies")) {
        $params = session_get_cookie_params();
        setcookie(session_name(), '', time() - 42000,
            $params["path"], $params["domain"],
            $params["secure"], $params["httponly"]
        );
    }

    // تدمير الجلسة
    session_destroy();

    // مسح أي rate limiting للمستخدم (اختياري)
    if (isset($_SERVER['REMOTE_ADDR'])) {
        clearRateLimit('login_' . $_SERVER['REMOTE_ADDR']);
    }
}

// منع التخزين المؤقت لهذه الصفحة
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

header('Location: index.php');
exit();
?>