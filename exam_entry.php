<?php
date_default_timezone_set('Africa/Cairo');
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

$error = '';
$exam = null;
$exam_link = '';

// التحقق من وجود رابط الامتحان
$exam_link = '';
if (isset($_GET['link'])) {
    $exam_link = $_GET['link'];
} else {
    // جرب جلب الكود من PATH_INFO
    $path_info = $_SERVER['PATH_INFO'] ?? '';
    if ($path_info && preg_match('~/([a-zA-Z0-9_]+)$~', $path_info, $matches)) {
        $exam_link = $matches[1];
    }

    // إذا لم نجد الرابط، جرب جلبه من REQUEST_URI
    if (empty($exam_link)) {
        $request_uri = $_SERVER['REQUEST_URI'] ?? '';
        // جرب patterns مختلفة
        if (preg_match('~/exam_entry\.php/([a-zA-Z0-9_]+)(?:\?.*)?$~', $request_uri, $matches)) {
            $exam_link = $matches[1];
        } elseif (preg_match('~/exam_entry\.php/exam_questions\.php/([a-zA-Z0-9_]+)~', $request_uri, $matches)) {
            // إذا كان هناك تداخل في الرابط، استخرج الكود الصحيح
            $exam_link = $matches[1];
        }
    }
}

// تسجيل للتشخيص
error_log("exam_entry.php - PATH_INFO: " . ($_SERVER['PATH_INFO'] ?? 'not set'));
error_log("exam_entry.php - REQUEST_URI: " . ($_SERVER['REQUEST_URI'] ?? 'not set'));
error_log("exam_entry.php - exam_link: " . $exam_link);

if (empty($exam_link)) {
    $error = 'رابط الامتحان مطلوب';
    error_log("No exam link provided");
} else {
    $exam_link = sanitize($exam_link);
    try {
        $stmt = $pdo->prepare("SELECT * FROM exams WHERE exam_link = ? AND status = 'active'");
        $stmt->execute([$exam_link]);
        $exam = $stmt->fetch();
        
        if (!$exam) {
            $error = 'رابط الامتحان غير صحيح أو الامتحان غير متاح';
            error_log("Invalid exam link: " . $exam_link);
        } else {
            $now = new DateTime();
            $start_time = new DateTime($exam['start_time']);
            $end_time = new DateTime($exam['end_time']);
            
            if ($now < $start_time) {
                $error = 'الامتحان لم يبدأ بعد. سيبدأ في: ' . $start_time->format('Y-m-d H:i');
                error_log("Exam not started yet. Start time: " . $start_time->format('Y-m-d H:i'));
            } elseif ($now > $end_time) {
                $error = 'انتهى وقت الامتحان. انتهى في: ' . $end_time->format('Y-m-d H:i');
                error_log("Exam ended. End time: " . $end_time->format('Y-m-d H:i'));
            }
        }
    } catch (PDOException $e) {
        error_log("Database error while fetching exam: " . $e->getMessage());
        $error = 'خطأ في النظام';
    }
}

// معالجة تقديم النموذج
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // التحقق من وجود جميع البيانات المطلوبة
    if (empty($_POST['student_id']) || empty($_POST['student_name']) || empty($_POST['section_number']) || empty($_POST['exam_link'])) {
        $error = 'يرجى إدخال جميع البيانات المطلوبة';
        error_log("Missing required fields in form submission");
    } else {
        try {
            $pdo->beginTransaction();

            // تنظيف وتحقق من البيانات
            $student_id = sanitize($_POST['student_id']);
            $student_name = sanitize($_POST['student_name']);
            $exam_link = sanitize($_POST['exam_link']);
            $section_number = sanitize($_POST['section_number']);

            // التحقق من صحة البيانات
            if (!validateStudentName($student_name)) {
                throw new Exception('يرجى إدخال اسم صحيح باللغة العربية أو الإنجليزية');
            }

            if (!validateStudentId($student_id)) {
                throw new Exception('يرجى إدخال رقم طالب صحيح');
            }

            // التحقق من وجود الامتحان
            $stmt = $pdo->prepare("SELECT * FROM exams WHERE exam_link = ? AND status = 'active'");
            $stmt->execute([$exam_link]);
            $exam = $stmt->fetch();

            if (!$exam) {
                throw new Exception('الامتحان غير متاح');
            }

            // التحقق من وجود محاولة جارية (غير مكتملة)
            $stmt = $pdo->prepare("SELECT * FROM exam_results WHERE exam_id = ? AND student_id = ? AND end_time IS NULL ORDER BY id DESC LIMIT 1");
            $stmt->execute([$exam['id'], $student_id]);
            $ongoing_attempt = $stmt->fetch();

            if ($ongoing_attempt) {
                // هناك محاولة جارية، توجيه إلى صفحة الأسئلة
                $_SESSION['exam_data'] = [
                    'exam_id' => $exam['id'],
                    'student_id' => $student_id,
                    'student_name' => $ongoing_attempt['student_name'],
                    'section_number' => $ongoing_attempt['section_number'],
                    'attempt_id' => $ongoing_attempt['id'],
                    'start_time' => $ongoing_attempt['start_time'],
                    'duration_minutes' => $ongoing_attempt['duration_minutes']
                ];

                $base_url = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://" . $_SERVER['HTTP_HOST'];
                header("Location: " . $base_url . "/exam_questions.php/" . urlencode($exam_link));
                exit();
            }

            // التحقق من عدد المحاولات المكتملة
            $stmt = $pdo->prepare("SELECT COUNT(*) as attempts FROM exam_results WHERE exam_id = ? AND student_id = ? AND end_time IS NOT NULL");
            $stmt->execute([$exam['id'], $student_id]);
            $attempts = $stmt->fetch();

            $total_allowed_entries = $exam['max_attempts'] + 1; // المرة الأساسية + المحاولات الإضافية

            if ($attempts['attempts'] >= $total_allowed_entries) {
                if ($exam['max_attempts'] == 0) {
                    throw new Exception('لقد أنهيت الامتحان ولا يُسمح بالإعادة');
                } else {
                    throw new Exception('لقد استنفذت جميع المحاولات المسموحة لهذا الامتحان');
                }
            }

            // إنشاء محاولة جديدة مع حفظ عدد الأسئلة الكلي ومجموع النقاط الكلي ومدة الامتحان
            $stmt = $pdo->prepare("INSERT INTO exam_results 
                (exam_id, student_id, student_name, section_number, start_time, duration_minutes) 
                VALUES (?, ?, ?, ?, NOW(), ?)");
            
            if (!$stmt->execute([$exam['id'], $student_id, $student_name, $section_number, $exam['duration_minutes']])) {
                throw new Exception('حدث خطأ أثناء بدء الامتحان');
            }

            $attempt_id = $pdo->lastInsertId();
            
            // حذف أي إجابات سابقة مرتبطة بهذه المحاولة (احتياطي)
            $stmt = $pdo->prepare("DELETE FROM student_answers WHERE exam_id = ? AND student_id = ? AND attempt_id = ?");
            $stmt->execute([$exam['id'], $student_id, $attempt_id]);

            // جلب الأسئلة التي ستظهر فعلياً للطالب (بنفس طريقة exam_questions.php)
            $stmt = $pdo->prepare("
                SELECT q.id
                FROM questions q
                JOIN exam_questions eq ON q.id = eq.question_id
                WHERE eq.exam_id = ? AND q.status = 'active'
                " . ($exam['randomize_questions'] ? 'ORDER BY RAND()' : 'ORDER BY eq.question_order') . "
                LIMIT ?
            ");
            $stmt->execute([$exam['id'], $exam['questions_count']]);
            $exam_questions = $stmt->fetchAll();

            // حفظ الأسئلة المختارة في الجلسة لضمان ثبات الأسئلة أثناء الامتحان
            $selected_question_ids = array_column($exam_questions, 'id');
            $_SESSION['selected_questions_' . $exam['id'] . '_' . $attempt_id] = $selected_question_ids;

            // تسجيل الأسئلة المختارة للمراجعة
            error_log("Selected questions for exam {$exam['id']}, attempt {$attempt_id}: " . implode(', ', $selected_question_ids));

            // إنشاء سجلات فارغة لجميع الأسئلة
            if (!empty($exam_questions)) {
                $insert_stmt = $pdo->prepare("
                    INSERT INTO student_answers (exam_id, student_id, question_id, selected_answer, attempt_id)
                    VALUES (?, ?, ?, '', ?)
                ");

                foreach ($exam_questions as $question) {
                    $insert_stmt->execute([$exam['id'], $student_id, $question['id'], $attempt_id]);
                }
            }

            // حذف الأسئلة المشاهدة من الجلسة عند بدء محاولة جديدة
            if (isset($_SESSION['visited_questions'])) {
                unset($_SESSION['visited_questions']);
            }

            // تخزين بيانات الامتحان في الجلسة
            $_SESSION['exam_data'] = [
                'exam_id' => $exam['id'],
                'student_id' => $student_id,
                'student_name' => $student_name,
                'section_number' => $section_number,
                'attempt_id' => $attempt_id,
                'start_time' => date('Y-m-d H:i:s'),
                'duration_minutes' => $exam['duration_minutes'] // أضف هذا السطر
            ];
            
            // تسجيل النشاط
            logActivity(null, 'exam_started', "بدء امتحان: $student_name - {$exam['title']}");
            
            $pdo->commit();
            
            // إعادة التوجيه إلى صفحة الأسئلة
            $base_url = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://" . $_SERVER['HTTP_HOST'];
            $redirect_url = $base_url . "/exam_questions.php/" . urlencode($exam_link);
            header("Location: " . $redirect_url);
            exit();

        } catch (Exception $e) {
            if ($pdo->inTransaction()) {
                $pdo->rollBack();
            }
            error_log("Error in exam_entry.php: " . $e->getMessage());
            $error = $e->getMessage();
        }
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>دخول الامتحان - جامعة المنيا</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;500;600&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;500;600&family=Cairo:wght@300;400;600;700&display=swap"//fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            min-height: 100vh;
            font-family: 'Cairo', sans-serif;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            position: relative;
            overflow-x: hidden;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.1) 0%, transparent 50%);
            z-index: -1;
        }

        /* Loading Animation */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            transition: opacity 0.5s ease, visibility 0.5s ease;
        }

        .loading-overlay.hidden {
            opacity: 0;
            visibility: hidden;
        }

        .loading-container {
            text-align: center;
            color: white;
        }

        .loading-text {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 30px;
            opacity: 0;
            animation: fadeInUp 1s ease 0.5s forwards;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }

        .loading-subtitle {
            font-size: 16px;
            opacity: 0;
            animation: fadeInUp 1s ease 1s forwards;
            margin-top: 20px;
            color: rgba(255, 255, 255, 0.8);
        }

        .progress-container {
            width: 200px;
            height: 4px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 2px;
            margin: 20px auto 0;
            overflow: hidden;
            opacity: 0;
            animation: fadeInUp 1s ease 1.5s forwards;
        }

        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #fff, rgba(255, 255, 255, 0.8));
            border-radius: 2px;
            width: 0%;
            animation: progressFill 2s ease-in-out 1.5s forwards;
        }

        @keyframes progressFill {
            0% { width: 0%; }
            100% { width: 100%; }
        }

        .loading-spinner {
            position: relative;
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
        }

        .loading-spinner div {
            position: absolute;
            top: 33px;
            width: 13px;
            height: 13px;
            border-radius: 50%;
            background: #fff;
            animation-timing-function: cubic-bezier(0, 1, 1, 0);
        }

        .loading-spinner div:nth-child(1) {
            left: 8px;
            animation: lds-ellipsis1 0.6s infinite;
        }

        .loading-spinner div:nth-child(2) {
            left: 8px;
            animation: lds-ellipsis2 0.6s infinite;
        }

        .loading-spinner div:nth-child(3) {
            left: 32px;
            animation: lds-ellipsis2 0.6s infinite;
        }

        .loading-spinner div:nth-child(4) {
            left: 56px;
            animation: lds-ellipsis3 0.6s infinite;
        }

        @keyframes lds-ellipsis1 {
            0% { transform: scale(0); }
            100% { transform: scale(1); }
        }

        @keyframes lds-ellipsis3 {
            0% { transform: scale(1); }
            100% { transform: scale(0); }
        }

        @keyframes lds-ellipsis2 {
            0% { transform: translate(0, 0); }
            100% { transform: translate(24px, 0); }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Main content initially hidden */
        .main-content {
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.8s ease;
        }

        .main-content.show {
            opacity: 1;
            transform: translateY(0);
        }

        .exam-entry-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            max-width: 1200px;
            width: 100%;
            align-items: start;
        }

        .exam-entry-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            box-shadow:
                0 32px 64px rgba(0, 0, 0, 0.25),
                0 16px 32px rgba(0, 0, 0, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            padding: 40px;
            width: 100%;
            position: relative;
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .exam-info-card {
            background: rgba(26, 26, 46, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            box-shadow:
                0 32px 64px rgba(0, 0, 0, 0.4),
                0 16px 32px rgba(0, 0, 0, 0.25),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            padding: 40px;
            width: 100%;
            position: relative;
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.1);
            color: white;
        }

        .exam-entry-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c);
            background-size: 300% 100%;
            animation: gradientShift 4s ease infinite;
        }

        .exam-info-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #00c9ff, #92fe9d, #fa709a, #fee140);
            background-size: 300% 100%;
            animation: gradientShift 4s ease infinite reverse;
        }

        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }
        .exam-logo {
            text-align: center;
            margin-bottom: 30px;
        }

        .exam-brand {
            font-size: 32px;
            font-weight: 800;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 12px;
            display: block;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .exam-title-main {
            font-size: 26px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 10px;
            text-align: center;
        }

        .exam-desc {
            color: #6c757d;
            font-size: 16px;
            text-align: center;
            margin-bottom: 30px;
            line-height: 1.6;
            font-weight: 500;
        }

        .exam-info-title {
            font-size: 24px;
            font-weight: 700;
            color: #ffffff;
            margin-bottom: 25px;
            text-align: center;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
        .form-label {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 15px;
            display: block;
        }

        .form-control {
            width: 100%;
            padding: 14px 18px;
            border: 2px solid #e8ecf0;
            border-radius: 12px;
            font-size: 16px;
            color: #2c3e50;
            background: rgba(255, 255, 255, 0.9);
            margin-bottom: 22px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(10px);
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow:
                0 0 0 3px rgba(102, 126, 234, 0.15),
                0 4px 12px rgba(102, 126, 234, 0.1);
            transform: translateY(-1px);
        }

        .form-control::placeholder {
            color: #a0a6b1;
            font-weight: 400;
        }
        .btn-primary {
            width: 100%;
            padding: 16px 24px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 17px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            margin-top: 15px;
            position: relative;
            overflow: hidden;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .btn-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn-primary:hover::before {
            left: 100%;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
            transform: translateY(-3px);
            box-shadow:
                0 12px 24px rgba(102, 126, 234, 0.4),
                0 6px 12px rgba(102, 126, 234, 0.2);
        }

        .btn-primary:active {
            transform: translateY(-1px);
        }
        .alert {
            padding: 15px 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            border: none;
        }

        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border-left: 4px solid #dc3545;
        }

        .exam-info {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 25px;
            margin-bottom: 25px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 14px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .info-item:hover {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            padding: 14px 12px;
            margin: 0 -12px;
        }

        .info-item:last-child {
            border-bottom: none;
        }

        .info-label {
            font-weight: 600;
            color: rgba(255, 255, 255, 0.9);
            font-size: 15px;
        }

        .info-value {
            color: #ffffff;
            font-weight: 700;
            font-size: 15px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }
        .countdown-timer {
            display: flex;
            gap: 8px;
            justify-content: center;
            margin-top: 15px;
            padding: 12px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }

        .countdown-item {
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 10px 8px;
            border-radius: 8px;
            min-width: 50px;
            box-shadow:
                0 4px 8px rgba(102, 126, 234, 0.3),
                0 2px 4px rgba(102, 126, 234, 0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .countdown-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .countdown-item:hover::before {
            opacity: 1;
        }

        .countdown-number {
            font-size: 18px;
            font-weight: 700;
            display: block;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        .countdown-label {
            font-size: 11px;
            margin-top: 4px;
            opacity: 0.95;
            font-weight: 500;
        }

        .countdown-timer.warning .countdown-item {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .countdown-timer.expired .countdown-item {
            background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
        }
        .exam-footer {
            color: #7f8c8d;
            font-size: 14px;
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
        }

        /* تحسينات للشاشات الصغيرة */
        @media (max-width: 1024px) {
            .exam-entry-container {
                grid-template-columns: 1fr;
                gap: 30px;
                max-width: 600px;
            }
        }

        @media (max-width: 768px) {
            body {
                padding: 15px;
            }

            .exam-entry-card,
            .exam-info-card {
                padding: 30px 25px;
            }

            .exam-brand {
                font-size: 28px;
            }

            .exam-title-main,
            .exam-info-title {
                font-size: 22px;
            }

            .countdown-timer {
                gap: 6px;
            }

            .countdown-item {
                min-width: 45px;
                padding: 8px 6px;
            }

            .countdown-number {
                font-size: 16px;
            }
        }

        @media (max-width: 480px) {
            .exam-entry-card,
            .exam-info-card {
                padding: 25px 20px;
            }

            .exam-brand {
                font-size: 24px;
            }

            .exam-title-main,
            .exam-info-title {
                font-size: 20px;
            }

            .form-control {
                padding: 12px 16px;
                font-size: 15px;
            }

            .btn-primary {
                padding: 14px 20px;
                font-size: 15px;
            }

            .countdown-timer {
                gap: 5px;
            }

            .countdown-item {
                min-width: 40px;
                padding: 8px 5px;
            }

            .countdown-number {
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
<!-- Loading Overlay -->
<div class="loading-overlay" id="loadingOverlay">
    <div class="loading-container">
        <div class="loading-text">جاري تحميل الامتحان...</div>
        <div class="loading-spinner">
            <div></div>
            <div></div>
            <div></div>
            <div></div>
        </div>
        <div class="loading-subtitle">
            يرجى الانتظار قليلاً
        </div>
        <div class="progress-container">
            <div class="progress-bar"></div>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="main-content" id="mainContent">
    <div class="exam-entry-container">
        <!-- Left Column: Form -->
        <div class="exam-entry-card">
            <div class="exam-logo">
                <span class="exam-brand">قسم الـ Forensic</span>
            </div>
            <div class="exam-title-main">الامتحان الإلكتروني</div>
            <div class="exam-desc">
                أدخل بياناتك للانضمام إلى الامتحان.
            </div>

            <?php if ($error): ?>
                <div class="alert alert-danger text-center">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo $error; ?>
                </div>
            <?php endif; ?>

            <?php if ($exam && !$error): ?>
                <div id="examStatus" class="alert alert-info text-center">
                    <i class="fas fa-clock me-2"></i>
                    <span id="statusMessage">الامتحان متاح - يمكنك البدء الآن</span>
                </div>

                <form method="POST" class="needs-validation" novalidate autocomplete="off">
                    <input type="hidden" name="exam_link" value="<?php echo htmlspecialchars($exam_link); ?>">

                    <label for="student_name" class="form-label">اسم الطالب *</label>
                    <input type="text" class="form-control" id="student_name" name="student_name" placeholder="أدخل اسمك الكامل" required>

                    <label for="student_id" class="form-label">رقم الطالب *</label>
                    <input type="text" class="form-control" id="student_id" name="student_id" placeholder="أدخل رقم الطالب" required>

                    <label for="section_number" class="form-label">اسم/رقم السكشن *</label>
                    <input type="text" class="form-control" id="section_number" name="section_number" placeholder="أدخل اسم أو رقم السكشن" required>

                    <button type="submit" class="btn btn-primary">
                        بدء الامتحان
                    </button>
                </form>
            <?php endif; ?>

            <?php if (!$exam && !$error): ?>
                <div class="text-center">
                    <i class="fas fa-link text-muted" style="font-size: 4rem;"></i>
                    <h4 class="text-muted mt-3">رابط الامتحان غير صحيح</h4>
                    <p class="text-muted">يرجى التأكد من صحة الرابط المقدم</p>
                </div>
            <?php endif; ?>

            <div class="exam-footer">
                جامعة المنيا - قسم الـ Forensic<br>
                <span style="font-size:0.95em;">نظام الامتحانات الإلكترونية</span>
            </div>
        </div>

        <!-- Right Column: Exam Info -->
        <?php if ($exam && !$error): ?>
        <div class="exam-info-card">
            <div class="exam-info-title">معلومات الامتحان</div>

            <div class="exam-info">
                <div class="info-item">
                    <span class="info-label">عنوان الامتحان:</span>
                    <span class="info-value"><?php echo htmlspecialchars($exam['title']); ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">نوع الامتحان:</span>
                    <span class="info-value"><?php echo $exam['exam_type'] === 'exam' ? 'امتحان' : 'كويز'; ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">مدة الامتحان:</span>
                    <span class="info-value"><?php echo $exam['duration_minutes']; ?> دقيقة</span>
                </div>
                <div class="info-item">
                    <span class="info-label">عدد الأسئلة:</span>
                    <span class="info-value"><?php echo $exam['questions_count']; ?> سؤال</span>
                </div>
                <div class="info-item">
                    <span class="info-label">عدد المحاولات:</span>
                    <span class="info-value">
                        <?php
                        if ($exam['max_attempts'] == 0) {
                            echo 'دخول واحد فقط';
                        } else {
                            $total_entries = $exam['max_attempts'] + 1;
                            echo $total_entries . ' دخول (' . $exam['max_attempts'] . ' محاولة إضافية)';
                        }
                        ?>
                    </span>
                </div>
                <div class="info-item">
                    <span class="info-label">وقت النهاية:</span>
                    <span class="info-value"><?php echo date('Y-m-d H:i', strtotime($exam['end_time'])); ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">الوقت المتبقي:</span>
                    <span class="info-value" id="timeRemaining">
                        <div class="countdown-timer" id="countdown">
                            <div class="countdown-item">
                                <span class="countdown-number" id="days">00</span>
                                <span class="countdown-label">يوم</span>
                            </div>
                            <div class="countdown-item">
                                <span class="countdown-number" id="hours">00</span>
                                <span class="countdown-label">ساعة</span>
                            </div>
                            <div class="countdown-item">
                                <span class="countdown-number" id="minutes">00</span>
                                <span class="countdown-label">دقيقة</span>
                            </div>
                            <div class="countdown-item">
                                <span class="countdown-number" id="seconds">00</span>
                                <span class="countdown-label">ثانية</span>
                            </div>
                        </div>
                    </span>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
        // Loading Animation Control
        window.addEventListener('load', function() {
            // Simulate loading steps
            const loadingTexts = [
                'جاري تحميل الامتحان...',
                'جاري التحقق من البيانات...',
                'جاري تحضير الأسئلة...',
                'تم التحميل بنجاح!'
            ];

            let currentTextIndex = 0;
            const loadingTextElement = document.querySelector('.loading-text');

            // Change loading text every 600ms
            const textInterval = setInterval(function() {
                if (currentTextIndex < loadingTexts.length - 1) {
                    currentTextIndex++;
                    loadingTextElement.textContent = loadingTexts[currentTextIndex];
                } else {
                    clearInterval(textInterval);
                }
            }, 600);

            // Hide loading overlay after 3 seconds
            setTimeout(function() {
                const loadingOverlay = document.getElementById('loadingOverlay');
                const mainContent = document.getElementById('mainContent');

                // Hide loading overlay
                loadingOverlay.classList.add('hidden');

                // Show main content with animation
                setTimeout(function() {
                    mainContent.classList.add('show');
                }, 300);

            }, 3000); // 3 seconds loading time
        });
</script>
<script>
        // التحقق من صحة النموذج
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                var forms = document.getElementsByClassName('needs-validation');
                var validation = Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);
        })();

        // مراقبة الوقت التلقائية
        <?php if ($exam && !$error): ?>
        const examEndTime = new Date('<?php echo $exam['end_time']; ?>').getTime();
        const examStartTime = new Date('<?php echo $exam['start_time']; ?>').getTime();
        const now = new Date().getTime();
        
        function updateExamStatus() {
            const currentTime = new Date().getTime();
            const timeRemaining = examEndTime - currentTime;
            const timeToStart = examStartTime - currentTime;
            
            const countdownElement = document.getElementById('countdown');
            const statusElement = document.getElementById('examStatus');
            const statusMessage = document.getElementById('statusMessage');
            const form = document.querySelector('form');
            
            // عناصر العداد
            const daysElement = document.getElementById('days');
            const hoursElement = document.getElementById('hours');
            const minutesElement = document.getElementById('minutes');
            const secondsElement = document.getElementById('seconds');
            
            if (timeToStart > 0) {
                // الامتحان لم يبدأ بعد
                const days = Math.floor(timeToStart / (1000 * 60 * 60 * 24));
                const hours = Math.floor((timeToStart % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                const minutes = Math.floor((timeToStart % (1000 * 60 * 60)) / (1000 * 60));
                const seconds = Math.floor((timeToStart % (1000 * 60)) / 1000);
                
                daysElement.textContent = days.toString().padStart(2, '0');
                hoursElement.textContent = hours.toString().padStart(2, '0');
                minutesElement.textContent = minutes.toString().padStart(2, '0');
                secondsElement.textContent = seconds.toString().padStart(2, '0');
                
                countdownElement.className = 'countdown-timer';
                statusElement.className = 'alert alert-warning';
                statusMessage.innerHTML = 'الامتحان لم يبدأ بعد - يرجى الانتظار';
                form.style.display = 'none';
                
            } else if (timeRemaining > 0) {
                // الامتحان نشط
                const days = Math.floor(timeRemaining / (1000 * 60 * 60 * 24));
                const hours = Math.floor((timeRemaining % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                const minutes = Math.floor((timeRemaining % (1000 * 60 * 60)) / (1000 * 60));
                const seconds = Math.floor((timeRemaining % (1000 * 60)) / 1000);
                
                daysElement.textContent = days.toString().padStart(2, '0');
                hoursElement.textContent = hours.toString().padStart(2, '0');
                minutesElement.textContent = minutes.toString().padStart(2, '0');
                secondsElement.textContent = seconds.toString().padStart(2, '0');
                
                countdownElement.className = 'countdown-timer';
                statusElement.className = 'alert alert-success';
                statusMessage.innerHTML = 'الامتحان متاح - يمكنك البدء الآن';
                form.style.display = 'block';
                
                // تحذير عند اقتراب انتهاء الوقت (أقل من 5 دقائق)
                if (timeRemaining < 5 * 60 * 1000) {
                    countdownElement.className = 'countdown-timer warning';
                    statusElement.className = 'alert alert-danger';
                    statusMessage.innerHTML = 'تحذير: الوقت المتبقي أقل من 5 دقائق!';
                }
                
            } else {
                // انتهى وقت الامتحان
                daysElement.textContent = '00';
                hoursElement.textContent = '00';
                minutesElement.textContent = '00';
                secondsElement.textContent = '00';
                
                countdownElement.className = 'countdown-timer expired';
                statusElement.className = 'alert alert-danger';
                statusMessage.innerHTML = 'انتهى وقت الامتحان - لم يعد متاحاً للدخول';
                form.style.display = 'none';
                
                // إظهار رسالة انتهاء الامتحان
                const examCard = document.querySelector('.exam-entry-card');
                const endMessage = document.createElement('div');
                endMessage.className = 'text-center mt-4';
                endMessage.innerHTML = `
                    <i class="fas fa-stop-circle text-danger" style="font-size: 3rem;"></i>
                    <h4 class="text-danger mt-3">انتهى وقت الامتحان</h4>
                    <p class="text-muted">لم يعد بإمكانك الدخول للامتحان</p>
                `;
                examCard.appendChild(endMessage);
                
                // إيقاف التحديث
                clearInterval(timerInterval);
                return;
            }
        }
        
        // تحديث كل ثانية
        updateExamStatus();
        const timerInterval = setInterval(updateExamStatus, 1000);
        
        // تحديث الصفحة كل دقيقة للتأكد من التزامن
        setInterval(function() {
            location.reload();
        }, 60000); // كل دقيقة
        
        <?php endif; ?>
    </script>
</body>
</html> 