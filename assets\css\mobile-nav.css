/* تحسينات القائمة الجانبية للموبايل */
/* نظام الامتحانات الإلكترونية */

/* إعدادات عامة للموبايل */
@media (max-width: 991.98px) {
    
    /* تحسين الـ navbar toggler */
    .navbar-toggler {
        border: 1px solid #dee2e6 !important;
        padding: 0.4rem 0.6rem !important;
        background-color: #f8f9fa !important;
        border-radius: 6px !important;
        transition: all 0.3s ease !important;
    }

    .navbar-toggler:hover {
        background-color: #e9ecef !important;
        border-color: #adb5bd !important;
    }

    .navbar-toggler:focus {
        box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25) !important;
        outline: none !important;
    }

    .navbar-toggler-icon {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%2833, 37, 41, 0.75%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e") !important;
        width: 1.2em !important;
        height: 1.2em !important;
    }

    /* تحسين القائمة المنسدلة */
    .navbar-collapse {
        background-color: #fff !important;
        border-radius: 12px !important;
        margin-top: 0.75rem !important;
        box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
        padding: 1.5rem !important;
        border: 1px solid #e9ecef !important;
        position: relative !important;
        z-index: 1050 !important;
    }

    .navbar-collapse::before {
        content: '';
        position: absolute;
        top: -8px;
        right: 20px;
        width: 0;
        height: 0;
        border-left: 8px solid transparent;
        border-right: 8px solid transparent;
        border-bottom: 8px solid #fff;
        z-index: 1051;
    }

    /* تحسين روابط القائمة */
    .navbar-nav {
        margin: 0 !important;
        padding: 0 !important;
    }

    .navbar-nav .nav-item {
        margin: 0.3rem 0 !important;
    }
    
    .navbar-nav .nav-link {
        padding: 1rem 1.25rem !important;
        margin: 0 !important;
        border-radius: 8px !important;
        transition: all 0.3s ease !important;
        color: #495057 !important;
        font-weight: 500 !important;
        font-size: 1rem !important;
        display: flex !important;
        align-items: center !important;
        position: relative !important;
        overflow: hidden !important;
    }

    .navbar-nav .nav-link::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(0,123,255,0.1), transparent);
        transition: left 0.5s ease;
    }
    
    .navbar-nav .nav-link:hover::before {
        left: 100%;
    }
    
    .navbar-nav .nav-link:hover {
        background-color: #f8f9fa !important;
        color: #007bff !important;
        transform: translateX(8px) !important;
        box-shadow: 0 2px 8px rgba(0,123,255,0.15) !important;
    }
    
    .navbar-nav .nav-link.active {
        background: linear-gradient(135deg, #007bff, #0056b3) !important;
        color: #fff !important;
        box-shadow: 0 4px 12px rgba(0,123,255,0.3) !important;
        transform: translateX(5px) !important;
    }

    .navbar-nav .nav-link.active::before {
        display: none;
    }

    /* تحسين أزرار الموبايل */
    .mobile-nav-buttons {
        display: flex !important;
        visibility: visible !important;
        opacity: 1 !important;
        align-items: center !important;
        gap: 0.5rem !important;
        z-index: 1000 !important;
    }

    .mobile-nav-buttons .btn {
        display: flex !important;
        visibility: visible !important;
        opacity: 1 !important;
        width: 42px !important;
        height: 42px !important;
        border-radius: 10px !important;
        align-items: center !important;
        justify-content: center !important;
        padding: 0 !important;
        margin: 0 !important;
        transition: all 0.3s ease !important;
        position: relative !important;
        overflow: hidden !important;
    }

    .mobile-nav-buttons .btn::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        background: rgba(255,255,255,0.3);
        border-radius: 50%;
        transform: translate(-50%, -50%);
        transition: width 0.3s ease, height 0.3s ease;
    }

    .mobile-nav-buttons .btn:active::before {
        width: 100px;
        height: 100px;
    }

    .mobile-nav-buttons .btn i {
        font-size: 16px !important;
        z-index: 1 !important;
        position: relative !important;
    }

    .mobile-nav-buttons .btn-outline-light {
        background-color: #f8f9fa !important;
        border-color: #dee2e6 !important;
        color: #495057 !important;
    }

    .mobile-nav-buttons .btn-outline-light:hover,
    .mobile-nav-buttons .btn-outline-light:active {
        background-color: #e9ecef !important;
        border-color: #adb5bd !important;
        color: #495057 !important;
        transform: translateY(-2px) !important;
        box-shadow: 0 4px 8px rgba(0,0,0,0.15) !important;
    }

    .mobile-nav-buttons .btn-outline-danger {
        background-color: #f8f9fa !important;
        border-color: #dc3545 !important;
        color: #dc3545 !important;
    }

    .mobile-nav-buttons .btn-outline-danger:hover,
    .mobile-nav-buttons .btn-outline-danger:active {
        background-color: #dc3545 !important;
        border-color: #dc3545 !important;
        color: #fff !important;
        transform: translateY(-2px) !important;
        box-shadow: 0 4px 8px rgba(220,53,69,0.3) !important;
    }
}

/* تأثيرات الحركة */
@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-15px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes slideUp {
    from {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
    to {
        opacity: 0;
        transform: translateY(-15px) scale(0.95);
    }
}

.navbar-collapse.show {
    animation: slideDown 0.3s ease-out !important;
}

.navbar-collapse.collapsing {
    animation: slideUp 0.3s ease-in !important;
}

/* تحسينات إضافية للشاشات الصغيرة جداً */
@media (max-width: 576px) {
    .mobile-nav-buttons .btn {
        width: 38px !important;
        height: 38px !important;
    }

    .mobile-nav-buttons .btn i {
        font-size: 14px !important;
    }

    .navbar-collapse {
        margin-top: 0.5rem !important;
        padding: 1rem !important;
    }

    .navbar-nav .nav-link {
        padding: 0.875rem 1rem !important;
        font-size: 0.95rem !important;
    }
}

/* إصلاح مشاكل اللمس */
.navbar-toggler,
.mobile-nav-buttons .btn,
.navbar-nav .nav-link {
    -webkit-tap-highlight-color: transparent !important;
    touch-action: manipulation !important;
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    user-select: none !important;
}

/* تحسين الأداء */
.navbar-collapse,
.mobile-nav-buttons,
.navbar-nav .nav-link {
    will-change: transform, opacity !important;
    backface-visibility: hidden !important;
    -webkit-backface-visibility: hidden !important;
}
